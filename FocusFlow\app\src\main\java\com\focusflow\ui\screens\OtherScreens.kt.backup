package com.focusflow.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.Expense
import com.focusflow.ui.viewmodel.ExpenseViewModel
import com.focusflow.ui.viewmodel.ExpenseCategories
import com.focusflow.ui.viewmodel.AICoachViewModel
import com.focusflow.ui.viewmodel.ChatMessage
import androidx.compose.animation.core.*
import kotlinx.datetime.LocalDateTime
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun ExpensesScreen(
    viewModel: ExpenseViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val allExpenses by viewModel.allExpenses.collectAsStateWithLifecycle(initialValue = emptyList())
    
    var showAddExpenseDialog by remember { mutableStateOf(false) }
    var selectedPeriod by remember { mutableStateOf("This Week") }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with period selector
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Expenses",
                style = MaterialTheme.typography.h4,
                fontWeight = FontWeight.Bold
            )
            
            // Period selector
            Row {
                listOf("This Week", "This Month", "All Time").forEach { period ->
                    FilterChip(
                        selected = selectedPeriod == period,
                        onClick = { selectedPeriod = period },
                        modifier = Modifier.padding(horizontal = 4.dp)
                    ) {
                        Text(
                            text = period,
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Spending summary card
        SpendingSummaryCard(
            totalSpent = uiState.totalSpentThisPeriod,
            period = uiState.budgetPeriod
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Quick category spending overview
        QuickCategoryOverview(expenses = uiState.currentPeriodExpenses)

        Spacer(modifier = Modifier.height(16.dp))

        // Expenses list header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Recent Transactions",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            TextButton(
                onClick = { showAddExpenseDialog = true }
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Add")
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Expenses list
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(allExpenses.take(20)) { expense ->
                ExpenseItem(
                    expense = expense,
                    onDelete = { viewModel.deleteExpense(expense) }
                )
            }
        }
    }

    // Add expense dialog with impulse control
    if (showAddExpenseDialog) {
        com.focusflow.ui.components.EnhancedAddExpenseDialog(
            onDismiss = { showAddExpenseDialog = false },
            onAddExpense = { amount, category, description, merchant ->
                viewModel.addExpense(amount, category, description, merchant)
                showAddExpenseDialog = false
            }
        )
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show snackbar or handle error
            viewModel.clearError()
        }
    }
}

@Composable
fun SpendingSummaryCard(
    totalSpent: Double,
    period: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Total Spent",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "$${String.format("%.2f", totalSpent)}",
                style = MaterialTheme.typography.h3,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.primary
            )
            Text(
                text = "This ${period.replaceFirstChar { it.lowercase() }}",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun QuickCategoryOverview(expenses: List<Expense>) {
    val categoryTotals = expenses.groupBy { it.category }
        .mapValues { (_, expenses) -> expenses.sumOf { it.amount } }
        .toList()
        .sortedByDescending { it.second }
        .take(5)

    if (categoryTotals.isNotEmpty()) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = 4.dp,
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Top Categories",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(12.dp))
                
                categoryTotals.forEach { (category, total) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = category,
                            style = MaterialTheme.typography.body2,
                            modifier = Modifier.weight(1f)
                        )
                        Text(
                            text = "$${String.format("%.2f", total)}",
                            style = MaterialTheme.typography.body2,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ExpenseItem(
    expense: Expense,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = expense.description,
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = expense.category,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                expense.merchant?.let { merchant ->
                    Text(
                        text = merchant,
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.5f)
                    )
                }
                Text(
                    text = formatDate(expense.date),
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.5f)
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "$${String.format("%.2f", expense.amount)}",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
                IconButton(
                    onClick = onDelete,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = "Delete",
                        tint = MaterialTheme.colors.error,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun AddExpenseDialog(
    onDismiss: () -> Unit,
    onAddExpense: (Double, String, String, String?) -> Unit
) {
    var amount by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var merchant by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Add Expense",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                // Amount input
                OutlinedTextField(
                    value = amount,
                    onValueChange = { amount = it },
                    label = { Text("Amount") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Category selection
                Text(
                    text = "Category",
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(4.dp))
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(ExpenseCategories.categories) { category ->
                        FilterChip(
                            selected = selectedCategory == category,
                            onClick = { selectedCategory = category }
                        ) {
                            Text(
                                text = category,
                                fontSize = 12.sp
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Description input
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Merchant input (optional)
                OutlinedTextField(
                    value = merchant,
                    onValueChange = { merchant = it },
                    label = { Text("Merchant (Optional)") },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val amountDouble = amount.toDoubleOrNull()
                    if (amountDouble != null && amountDouble > 0 && selectedCategory.isNotBlank() && description.isNotBlank()) {
                        onAddExpense(
                            amountDouble,
                            selectedCategory,
                            description,
                            merchant.takeIf { it.isNotBlank() }
                        )
                    }
                },
                enabled = amount.toDoubleOrNull() != null && 
                         amount.toDoubleOrNull()!! > 0 && 
                         selectedCategory.isNotBlank() && 
                         description.isNotBlank()
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun FilterChip(
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Surface(
        modifier = modifier.clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        color = if (selected) MaterialTheme.colors.primary else MaterialTheme.colors.surface,
        elevation = if (selected) 4.dp else 1.dp
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            contentAlignment = Alignment.Center
        ) {
            CompositionLocalProvider(
                LocalContentColor provides if (selected) MaterialTheme.colors.onPrimary else MaterialTheme.colors.onSurface
            ) {
                content()
            }
        }
    }
}

private fun formatDate(dateTime: LocalDateTime): String {
    return dateTime.toString() // Simplified formatting
}





@Composable
fun HabitsScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Health & Habits",
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "Track your mood, sleep, exercise, and medication here",
            style = MaterialTheme.typography.body1
        )
    }
}

@Composable
fun TasksScreen(
    viewModel: com.focusflow.ui.viewmodel.TaskViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val filteredTasks by viewModel.filteredTasks.collectAsStateWithLifecycle()
    val selectedFilter by viewModel.selectedFilter.collectAsStateWithLifecycle()
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    val recommendedTasks by viewModel.recommendedTasks.collectAsStateWithLifecycle()

    var showCreateTaskDialog by remember { mutableStateOf(false) }
    var showBreakDownDialog by remember { mutableStateOf(false) }
    var selectedTaskForBreakdown by remember { mutableStateOf<com.focusflow.data.model.Task?>(null) }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Header with statistics
            TasksHeaderCard(
                statistics = uiState.taskStatistics,
                isLoading = uiState.isLoading,
                onCreateTask = { showCreateTaskDialog = true }
            )
        }

        item {
            // Search and filter bar
            TaskSearchAndFilterBar(
                searchQuery = searchQuery,
                selectedFilter = selectedFilter,
                onSearchQueryChange = viewModel::setSearchQuery,
                onFilterChange = viewModel::setFilter
            )
        }

        // Recommended tasks section (ADHD-friendly)
        if (recommendedTasks.isNotEmpty() && selectedFilter == com.focusflow.ui.viewmodel.TaskFilter.ALL) {
            item {
                RecommendedTasksCard(
                    tasks = recommendedTasks,
                    onTaskComplete = viewModel::completeTask,
                    onTaskEdit = { /* TODO: Implement edit */ },
                    onBreakDown = { task ->
                        selectedTaskForBreakdown = task
                        showBreakDownDialog = true
                    }
                )
            }
        }

        item {
            // Tasks list
            TasksListCard(
                tasks = filteredTasks,
                selectedFilter = selectedFilter,
                onTaskComplete = viewModel::completeTask,
                onTaskDelete = viewModel::deleteTask,
                onTaskEdit = { /* TODO: Implement edit */ },
                onBreakDown = { task ->
                    selectedTaskForBreakdown = task
                    showBreakDownDialog = true
                },
                viewModel = viewModel
            )
        }
    }

    // Create task dialog
    if (showCreateTaskDialog) {
        CreateTaskDialog(
            availableCategories = uiState.availableCategories,
            isCreating = uiState.isCreating,
            onDismiss = { showCreateTaskDialog = false },
            onCreateTask = { title, description, dueDate, priority, category, duration ->
                viewModel.createTask(title, description, dueDate, priority, category, duration)
                showCreateTaskDialog = false
            }
        )
    }

    // Break down task dialog
    if (showBreakDownDialog && selectedTaskForBreakdown != null) {
        BreakDownTaskDialog(
            task = selectedTaskForBreakdown!!,
            onDismiss = {
                showBreakDownDialog = false
                selectedTaskForBreakdown = null
            },
            onBreakDown = { subtasks ->
                viewModel.breakDownTask(selectedTaskForBreakdown!!, subtasks)
                showBreakDownDialog = false
                selectedTaskForBreakdown = null
            }
        )
    }

    // Success message
    uiState.lastAction?.let { message ->
        LaunchedEffect(message) {
            kotlinx.coroutines.delay(3000)
            viewModel.clearLastAction()
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f),
            elevation = 2.dp
        ) {
            Text(
                text = message,
                modifier = Modifier.padding(12.dp),
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.primary
            )
        }
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            viewModel.clearError()
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            backgroundColor = MaterialTheme.colors.error.copy(alpha = 0.1f),
            elevation = 2.dp
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colors.error,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = error,
                    color = MaterialTheme.colors.error,
                    style = MaterialTheme.typography.body2,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
fun AICoachScreen(
    viewModel: AICoachViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "AI Coach",
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "Your ADHD-friendly financial assistant",
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Suggested prompts when no conversation
        if (uiState.conversations.isEmpty()) {
            SuggestedPromptsSection(
                onPromptSelected = viewModel::sendMessage
            )
        } else {
            // Chat messages
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(uiState.conversations) { message ->
                    MessageBubble(message = message)
                }

                if (uiState.isLoading) {
                    item {
                        TypingIndicator()
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Input field
        MessageInputField(
            message = uiState.currentMessage,
            onMessageChange = viewModel::updateCurrentMessage,
            onSend = {
                viewModel.sendMessage(uiState.currentMessage)
            },
            enabled = !uiState.isLoading
        )

        // Error handling
        uiState.error?.let { error ->
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                backgroundColor = MaterialTheme.colors.error.copy(alpha = 0.1f),
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = error,
                    color = MaterialTheme.colors.error,
                    modifier = Modifier.padding(12.dp),
                    style = MaterialTheme.typography.body2
                )
            }
        }
    }
}

@Composable
fun SuggestedPromptsSection(
    onPromptSelected: (String) -> Unit
) {
    val suggestedPrompts = listOf(
        "💰 Analyze my spending this week",
        "📊 Help me create a budget",
        "💳 Review my debt situation",
        "🎯 Break down a big task",
        "📈 Show my progress",
        "💡 Give me a money-saving tip"
    )

    Column {
        Text(
            text = "How can I help you today?",
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(suggestedPrompts) { prompt ->
                SuggestedPromptCard(
                    prompt = prompt,
                    onClick = { onPromptSelected(prompt) }
                )
            }
        }
    }
}

@Composable
fun SuggestedPromptCard(
    prompt: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Text(
            text = prompt,
            modifier = Modifier.padding(16.dp),
            style = MaterialTheme.typography.body2,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun MessageBubble(message: ChatMessage) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = if (message.isFromUser) Arrangement.End else Arrangement.Start
    ) {
        if (!message.isFromUser) {
            // AI avatar
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(
                        MaterialTheme.colors.primary,
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    Icons.Default.Person,
                    contentDescription = "AI",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
        }

        Card(
            modifier = Modifier.widthIn(max = 280.dp),
            backgroundColor = if (message.isFromUser) {
                MaterialTheme.colors.primary
            } else {
                MaterialTheme.colors.surface
            },
            elevation = 2.dp,
            shape = RoundedCornerShape(
                topStart = 16.dp,
                topEnd = 16.dp,
                bottomStart = if (message.isFromUser) 16.dp else 4.dp,
                bottomEnd = if (message.isFromUser) 4.dp else 16.dp
            )
        ) {
            Text(
                text = message.content,
                modifier = Modifier.padding(12.dp),
                color = if (message.isFromUser) {
                    Color.White
                } else {
                    MaterialTheme.colors.onSurface
                },
                style = MaterialTheme.typography.body2
            )
        }

        if (message.isFromUser) {
            Spacer(modifier = Modifier.width(8.dp))
            // User avatar
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(
                        MaterialTheme.colors.secondary,
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    Icons.Default.Person,
                    contentDescription = "User",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun TypingIndicator() {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(32.dp)
                .background(
                    MaterialTheme.colors.primary,
                    CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                Icons.Default.Person,
                contentDescription = "AI",
                tint = Color.White,
                modifier = Modifier.size(20.dp)
            )
        }
        Spacer(modifier = Modifier.width(8.dp))

        Card(
            backgroundColor = MaterialTheme.colors.surface,
            elevation = 2.dp,
            shape = RoundedCornerShape(16.dp, 16.dp, 16.dp, 4.dp)
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(3) { index ->
                    val alpha by animateFloatAsState(
                        targetValue = if ((System.currentTimeMillis() / 500) % 3 == index.toLong()) 1f else 0.3f,
                        animationSpec = infiniteRepeatable(
                            animation = tween(500),
                            repeatMode = RepeatMode.Reverse
                        )
                    )
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                MaterialTheme.colors.onSurface.copy(alpha = alpha),
                                CircleShape
                            )
                    )
                    if (index < 2) Spacer(modifier = Modifier.width(4.dp))
                }
            }
        }
    }
}

@Composable
fun MessageInputField(
    message: String,
    onMessageChange: (String) -> Unit,
    onSend: () -> Unit,
    enabled: Boolean = true
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Bottom
    ) {
        OutlinedTextField(
            value = message,
            onValueChange = onMessageChange,
            placeholder = { Text("Ask me anything about your finances...") },
            modifier = Modifier.weight(1f),
            enabled = enabled,
            maxLines = 3,
            shape = RoundedCornerShape(24.dp)
        )

        Spacer(modifier = Modifier.width(8.dp))

        FloatingActionButton(
            onClick = if (enabled && message.isNotBlank()) onSend else { {} },
            modifier = Modifier.size(48.dp),
            backgroundColor = if (enabled && message.isNotBlank()) {
                MaterialTheme.colors.primary
            } else {
                MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            }
        ) {
            Icon(
                Icons.Default.Send,
                contentDescription = "Send",
                tint = Color.White
            )
        }
    }
}

@Composable
fun TasksHeaderCard(
    statistics: com.focusflow.data.repository.TaskStatistics?,
    isLoading: Boolean,
    onCreateTask: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Tasks & To-Do",
                    style = MaterialTheme.typography.h5,
                    fontWeight = FontWeight.Bold
                )

                FloatingActionButton(
                    onClick = onCreateTask,
                    modifier = Modifier.size(40.dp),
                    backgroundColor = MaterialTheme.colors.primary
                ) {
                    Icon(
                        Icons.Default.Add,
                        contentDescription = "Add Task",
                        tint = Color.White
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(60.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else if (statistics != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    TaskStatItem(
                        label = "Total",
                        value = statistics.totalTasks.toString(),
                        color = MaterialTheme.colors.onSurface
                    )
                    TaskStatItem(
                        label = "Completed",
                        value = statistics.completedTasks.toString(),
                        color = Color(0xFF4CAF50)
                    )
                    TaskStatItem(
                        label = "Pending",
                        value = statistics.incompleteTasks.toString(),
                        color = Color(0xFFFF9800)
                    )
                    TaskStatItem(
                        label = "Overdue",
                        value = statistics.overdueTasks.toString(),
                        color = Color(0xFFF44336)
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                LinearProgressIndicator(
                    progress = (statistics.completionRate / 100).toFloat(),
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFF4CAF50)
                )

                Text(
                    text = "Completion Rate: ${String.format("%.1f", statistics.completionRate)}%",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

@Composable
fun TaskStatItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun TaskSearchAndFilterBar(
    searchQuery: String,
    selectedFilter: com.focusflow.ui.viewmodel.TaskFilter,
    onSearchQueryChange: (String) -> Unit,
    onFilterChange: (com.focusflow.ui.viewmodel.TaskFilter) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Search bar
            OutlinedTextField(
                value = searchQuery,
                onValueChange = onSearchQueryChange,
                placeholder = { Text("Search tasks...") },
                leadingIcon = {
                    Icon(Icons.Default.Search, contentDescription = "Search")
                },
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp)
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Filter chips
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(com.focusflow.ui.viewmodel.TaskFilter.values()) { filter ->
                    FilterChip(
                        selected = selectedFilter == filter,
                        onClick = { onFilterChange(filter) }
                    ) {
                        Text(
                            text = when (filter) {
                                com.focusflow.ui.viewmodel.TaskFilter.ALL -> "All"
                                com.focusflow.ui.viewmodel.TaskFilter.INCOMPLETE -> "Pending"
                                com.focusflow.ui.viewmodel.TaskFilter.COMPLETED -> "Completed"
                                com.focusflow.ui.viewmodel.TaskFilter.TODAY -> "Today"
                                com.focusflow.ui.viewmodel.TaskFilter.OVERDUE -> "Overdue"
                                com.focusflow.ui.viewmodel.TaskFilter.HIGH_PRIORITY -> "High Priority"
                                com.focusflow.ui.viewmodel.TaskFilter.MEDIUM_PRIORITY -> "Medium Priority"
                                com.focusflow.ui.viewmodel.TaskFilter.LOW_PRIORITY -> "Low Priority"
                            },
                            style = MaterialTheme.typography.caption
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun RecommendedTasksCard(
    tasks: List<com.focusflow.data.model.Task>,
    onTaskComplete: (com.focusflow.data.model.Task) -> Unit,
    onTaskEdit: (com.focusflow.data.model.Task) -> Unit,
    onBreakDown: (com.focusflow.data.model.Task) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.05f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Star,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "ADHD-Friendly Recommendations",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colors.primary
                )
            }

            Text(
                text = "Quick wins and high-priority tasks to help you build momentum",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 4.dp, bottom = 12.dp)
            )

            tasks.take(3).forEach { task ->
                TaskItemCompact(
                    task = task,
                    onComplete = { onTaskComplete(task) },
                    onEdit = { onTaskEdit(task) },
                    onBreakDown = { onBreakDown(task) }
                )
                if (task != tasks.last()) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

@Composable
fun TaskItemCompact(
    task: com.focusflow.data.model.Task,
    onComplete: () -> Unit,
    onEdit: () -> Unit,
    onBreakDown: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 1.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = task.isCompleted,
                onCheckedChange = { if (!task.isCompleted) onComplete() }
            )

            Spacer(modifier = Modifier.width(8.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Medium
                )

                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = when (task.priority) {
                            "high" -> "🔴"
                            "medium" -> "🟡"
                            "low" -> "🟢"
                            else -> "⚪"
                        },
                        style = MaterialTheme.typography.caption
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = task.estimatedDuration?.let { "${it}m" } ?: "No estimate",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }

            if ((task.estimatedDuration ?: 60) > 30) {
                IconButton(
                    onClick = onBreakDown,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        Icons.Default.CallSplit,
                        contentDescription = "Break Down",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun TasksListCard(
    tasks: List<com.focusflow.data.model.Task>,
    selectedFilter: com.focusflow.ui.viewmodel.TaskFilter,
    onTaskComplete: (com.focusflow.data.model.Task) -> Unit,
    onTaskDelete: (com.focusflow.data.model.Task) -> Unit,
    onTaskEdit: (com.focusflow.data.model.Task) -> Unit,
    onBreakDown: (com.focusflow.data.model.Task) -> Unit,
    viewModel: com.focusflow.ui.viewmodel.TaskViewModel
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = when (selectedFilter) {
                    com.focusflow.ui.viewmodel.TaskFilter.ALL -> "All Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.INCOMPLETE -> "Pending Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.COMPLETED -> "Completed Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.TODAY -> "Today's Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.OVERDUE -> "Overdue Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.HIGH_PRIORITY -> "High Priority Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.MEDIUM_PRIORITY -> "Medium Priority Tasks (${tasks.size})"
                    com.focusflow.ui.viewmodel.TaskFilter.LOW_PRIORITY -> "Low Priority Tasks (${tasks.size})"
                },
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 12.dp)
            )

            if (tasks.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "🎉",
                            style = MaterialTheme.typography.h4
                        )
                        Text(
                            text = when (selectedFilter) {
                                com.focusflow.ui.viewmodel.TaskFilter.COMPLETED -> "No completed tasks yet"
                                com.focusflow.ui.viewmodel.TaskFilter.OVERDUE -> "No overdue tasks!"
                                else -> "No tasks found"
                            },
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            } else {
                tasks.forEach { task ->
                    TaskItem(
                        task = task,
                        onComplete = { onTaskComplete(task) },
                        onDelete = { onTaskDelete(task) },
                        onEdit = { onTaskEdit(task) },
                        onBreakDown = { onBreakDown(task) },
                        viewModel = viewModel
                    )
                    if (task != tasks.last()) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }
            }
        }

@Composable
fun TaskItem(
    task: com.focusflow.data.model.Task,
    onComplete: () -> Unit,
    onDelete: () -> Unit,
    onEdit: () -> Unit,
    onBreakDown: () -> Unit,
    viewModel: com.focusflow.ui.viewmodel.TaskViewModel
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.Top
            ) {
                Checkbox(
                    checked = task.isCompleted,
                    onCheckedChange = { if (!task.isCompleted) onComplete() }
                )

                Spacer(modifier = Modifier.width(8.dp))

                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = task.title,
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium
                    )

                    task.description?.let { description ->
                        Text(
                            text = description,
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(4.dp))

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = viewModel.getPriorityEmoji(task.priority),
                            style = MaterialTheme.typography.caption
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = task.priority.replaceFirstChar { it.uppercase() },
                            style = MaterialTheme.typography.caption,
                            color = viewModel.getPriorityColor(task.priority)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = viewModel.formatDuration(task.estimatedDuration),
                            style = MaterialTheme.typography.caption,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )

                        task.category?.let { category ->
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "📁 $category",
                                style = MaterialTheme.typography.caption,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    }

                    Text(
                        text = viewModel.getTaskStatusMessage(task),
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }

                // Action buttons
                Row {
                    if ((task.estimatedDuration ?: 60) > 30 && !task.isCompleted) {
                        IconButton(
                            onClick = onBreakDown,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                Icons.Default.CallSplit,
                                contentDescription = "Break Down",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }

                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = MaterialTheme.colors.error,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CreateTaskDialog(
    availableCategories: List<String>,
    isCreating: Boolean,
    onDismiss: () -> Unit,
    onCreateTask: (String, String, kotlinx.datetime.LocalDateTime?, String, String?, Int?) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var priority by remember { mutableStateOf("medium") }
    var category by remember { mutableStateOf("") }
    var estimatedDuration by remember { mutableStateOf("") }
    var hasDueDate by remember { mutableStateOf(false) }
    var dueDateString by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Create New Task",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            LazyColumn {
                item {
                    OutlinedTextField(
                        value = title,
                        onValueChange = { title = it },
                        label = { Text("Task Title") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("Description (Optional)") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    Text(
                        text = "Priority",
                        style = MaterialTheme.typography.body2,
                        fontWeight = FontWeight.Medium
                    )
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        listOf("low", "medium", "high").forEach { p ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.clickable { priority = p }
                            ) {
                                RadioButton(
                                    selected = priority == p,
                                    onClick = { priority = p }
                                )
                                Text(
                                    text = p.replaceFirstChar { it.uppercase() },
                                    style = MaterialTheme.typography.body2
                                )
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = estimatedDuration,
                        onValueChange = { estimatedDuration = it },
                        label = { Text("Estimated Duration (minutes)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                if (availableCategories.isNotEmpty()) {
                    item {
                        Text(
                            text = "Category (Optional)",
                            style = MaterialTheme.typography.body2,
                            fontWeight = FontWeight.Medium
                        )
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(availableCategories) { cat ->
                                FilterChip(
                                    selected = category == cat,
                                    onClick = { category = if (category == cat) "" else cat }
                                ) {
                                    Text(cat, style = MaterialTheme.typography.caption)
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val duration = estimatedDuration.toIntOrNull()
                    onCreateTask(title, description, null, priority, category.takeIf { it.isNotBlank() }, duration)
                },
                enabled = title.isNotBlank() && !isCreating
            ) {
                if (isCreating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("Create")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun BreakDownTaskDialog(
    task: com.focusflow.data.model.Task,
    onDismiss: () -> Unit,
    onBreakDown: (List<String>) -> Unit
) {
    var subtasks by remember { mutableStateOf(listOf("", "", "")) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Break Down Task",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Breaking down: ${task.title}",
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                Text(
                    text = "Split this task into smaller, manageable pieces (ADHD-friendly approach):",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                subtasks.forEachIndexed { index, subtask ->
                    OutlinedTextField(
                        value = subtask,
                        onValueChange = { newValue ->
                            subtasks = subtasks.toMutableList().apply {
                                this[index] = newValue
                            }
                        },
                        label = { Text("Subtask ${index + 1}") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    if (index < subtasks.size - 1) {
                        Spacer(modifier = Modifier.height(8.dp))
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(
                        onClick = {
                            if (subtasks.size > 2) {
                                subtasks = subtasks.dropLast(1)
                            }
                        },
                        enabled = subtasks.size > 2
                    ) {
                        Text("Remove")
                    }

                    TextButton(
                        onClick = {
                            if (subtasks.size < 6) {
                                subtasks = subtasks + ""
                            }
                        },
                        enabled = subtasks.size < 6
                    ) {
                        Text("Add More")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val validSubtasks = subtasks.filter { it.isNotBlank() }
                    if (validSubtasks.isNotEmpty()) {
                        onBreakDown(validSubtasks)
                    }
                },
                enabled = subtasks.any { it.isNotBlank() }
            ) {
                Text("Break Down")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}


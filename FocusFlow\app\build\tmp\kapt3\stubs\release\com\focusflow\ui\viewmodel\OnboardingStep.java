package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/focusflow/ui/viewmodel/OnboardingStep;", "", "(Ljava/lang/String;I)V", "WELCOME", "ADHD_FRIENDLY", "GOALS_FINANCIAL", "GOALS_PERSONAL", "INCOME_SETUP", "DEBT_SETUP", "BUDGET_SETUP", "NOTIFICATION_SETUP", "COMPLETE", "app_release"})
public enum OnboardingStep {
    /*public static final*/ WELCOME /* = new WELCOME() */,
    /*public static final*/ ADHD_FRIENDLY /* = new ADHD_FRIENDLY() */,
    /*public static final*/ GOALS_FINANCIAL /* = new GOALS_FINANCIAL() */,
    /*public static final*/ GOALS_PERSONAL /* = new GOALS_PERSONAL() */,
    /*public static final*/ INCOME_SETUP /* = new INCOME_SETUP() */,
    /*public static final*/ DEBT_SETUP /* = new DEBT_SETUP() */,
    /*public static final*/ BUDGET_SETUP /* = new BUDGET_SETUP() */,
    /*public static final*/ NOTIFICATION_SETUP /* = new NOTIFICATION_SETUP() */,
    /*public static final*/ COMPLETE /* = new COMPLETE() */;
    
    OnboardingStep() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.ui.viewmodel.OnboardingStep> getEntries() {
        return null;
    }
}
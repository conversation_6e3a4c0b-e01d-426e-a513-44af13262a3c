package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.VirtualPet
import com.focusflow.data.repository.VirtualPetRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class VirtualPetViewModel @Inject constructor(
    private val virtualPetRepository: VirtualPetRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(VirtualPetUiState())
    val uiState: StateFlow<VirtualPetUiState> = _uiState.asStateFlow()

    val virtualPet = virtualPetRepository.getVirtualPet()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )

    init {
        loadOrCreatePet()
        
        // Observe pet changes and update UI state
        viewModelScope.launch {
            virtualPet.collect { pet ->
                if (pet != null) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        petEmoji = virtualPetRepository.getPetEmoji(pet.type, pet.happiness, pet.health),
                        petMood = virtualPetRepository.getPetMood(pet.happiness, pet.health),
                        error = null
                    )
                }
            }
        }
        
        // Simulate pet care periodically
        viewModelScope.launch {
            while (true) {
                kotlinx.coroutines.delay(300000) // Every 5 minutes
                simulatePetCare()
            }
        }
    }

    private fun loadOrCreatePet() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val existingPet = virtualPetRepository.getVirtualPetSync()
                if (existingPet == null) {
                    virtualPetRepository.createDefaultPet()
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load pet: ${e.message}"
                )
            }
        }
    }

    fun feedPet() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isFeeding = true, error = null)
                
                val updatedPet = virtualPetRepository.feedPet()
                if (updatedPet != null) {
                    _uiState.value = _uiState.value.copy(
                        isFeeding = false,
                        lastAction = "Fed ${updatedPet.name}! Happiness +10, Health +5",
                        showActionFeedback = true
                    )
                    
                    // Clear feedback after delay
                    kotlinx.coroutines.delay(3000)
                    _uiState.value = _uiState.value.copy(showActionFeedback = false)
                } else {
                    _uiState.value = _uiState.value.copy(
                        isFeeding = false,
                        error = "Failed to feed pet"
                    )
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isFeeding = false,
                    error = "Failed to feed pet: ${e.message}"
                )
            }
        }
    }

    fun playWithPet() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isPlaying = true, error = null)
                
                val updatedPet = virtualPetRepository.playWithPet()
                if (updatedPet != null) {
                    val levelUpMessage = if (updatedPet.level > (virtualPet.value?.level ?: 1)) {
                        " Level up! Now level ${updatedPet.level}!"
                    } else ""
                    
                    _uiState.value = _uiState.value.copy(
                        isPlaying = false,
                        lastAction = "Played with ${updatedPet.name}! Happiness +15, Experience +5$levelUpMessage",
                        showActionFeedback = true
                    )
                    
                    // Clear feedback after delay
                    kotlinx.coroutines.delay(3000)
                    _uiState.value = _uiState.value.copy(showActionFeedback = false)
                } else {
                    _uiState.value = _uiState.value.copy(
                        isPlaying = false,
                        error = "Failed to play with pet"
                    )
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isPlaying = false,
                    error = "Failed to play with pet: ${e.message}"
                )
            }
        }
    }

    fun renamePet(newName: String) {
        if (newName.isBlank()) return
        
        viewModelScope.launch {
            try {
                val updatedPet = virtualPetRepository.updatePetName(newName.trim())
                if (updatedPet != null) {
                    _uiState.value = _uiState.value.copy(
                        lastAction = "Pet renamed to ${updatedPet.name}!",
                        showActionFeedback = true
                    )
                    
                    kotlinx.coroutines.delay(2000)
                    _uiState.value = _uiState.value.copy(showActionFeedback = false)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to rename pet: ${e.message}"
                )
            }
        }
    }

    fun changePetType(newType: String) {
        viewModelScope.launch {
            try {
                val updatedPet = virtualPetRepository.changePetType(newType)
                if (updatedPet != null) {
                    _uiState.value = _uiState.value.copy(
                        lastAction = "Pet changed to ${newType}!",
                        showActionFeedback = true
                    )
                    
                    kotlinx.coroutines.delay(2000)
                    _uiState.value = _uiState.value.copy(showActionFeedback = false)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to change pet type: ${e.message}"
                )
            }
        }
    }

    fun addAccessory(accessory: String) {
        viewModelScope.launch {
            try {
                val updatedPet = virtualPetRepository.addAccessory(accessory)
                if (updatedPet != null) {
                    _uiState.value = _uiState.value.copy(
                        lastAction = "Added $accessory to ${updatedPet.name}!",
                        showActionFeedback = true
                    )
                    
                    kotlinx.coroutines.delay(2000)
                    _uiState.value = _uiState.value.copy(showActionFeedback = false)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to add accessory: ${e.message}"
                )
            }
        }
    }

    private fun simulatePetCare() {
        viewModelScope.launch {
            try {
                virtualPetRepository.simulatePetCare()
            } catch (e: Exception) {
                // Silently handle simulation errors
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun dismissActionFeedback() {
        _uiState.value = _uiState.value.copy(showActionFeedback = false)
    }
}

data class VirtualPetUiState(
    val isLoading: Boolean = false,
    val isFeeding: Boolean = false,
    val isPlaying: Boolean = false,
    val petEmoji: String = "🐱",
    val petMood: String = "Happy",
    val lastAction: String = "",
    val showActionFeedback: Boolean = false,
    val error: String? = null
)

package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000^\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001a(\u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a*\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0015\u0010\u0016\u001a(\u0010\u0017\u001a\u00020\u00012\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00100\u000b2\u0006\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\u0016\u0010\u0019\u001a\u00020\u00012\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u000bH\u0003\u001aJ\u0010\u001c\u001a\u00020\u00012\u0012\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000b0\u000b2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b2\u0006\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a<\u0010 \u001a\u00020\u00012\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u000b2\u0006\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010!\u001a\u00020\"2\b\b\u0002\u0010#\u001a\u00020\"H\u0007\u001a\u0015\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020\u0014H\u0002\u00a2\u0006\u0002\u0010&\u001a\u0015\u0010\'\u001a\u00020\u00122\u0006\u0010(\u001a\u00020)H\u0002\u00a2\u0006\u0002\u0010*\u001a<\u0010+\u001a\u00020\u0001*\u00020,2\u0012\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000b0\u000b2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00030\u000bH\u0002\u001a\"\u0010-\u001a\u00020\u0001*\u00020,2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00100\u000b2\u0006\u0010.\u001a\u00020\u0005H\u0002\u001a*\u0010/\u001a\u00020\u0001*\u00020,2\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u000b2\u0006\u00100\u001a\u00020)2\u0006\u0010!\u001a\u00020\"H\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00061"}, d2 = {"BudgetVarianceBar", "", "categoryName", "", "budgetAmount", "", "actualAmount", "modifier", "Landroidx/compose/ui/Modifier;", "BudgetVarianceChart", "budgetData", "", "Lcom/focusflow/ui/components/BudgetVarianceData;", "title", "CategoryLegendItem", "category", "Lcom/focusflow/ui/components/CategorySpending;", "color", "Landroidx/compose/ui/graphics/Color;", "percentage", "", "CategoryLegendItem-bw27NRU", "(Lcom/focusflow/ui/components/CategorySpending;JI)V", "CategorySpendingPieChart", "categories", "SpendingChartLegend", "data", "Lcom/focusflow/ui/components/SpendingDataPoint;", "SpendingHeatmap", "heatmapData", "dayLabels", "hourLabels", "SpendingTrendChart", "showGrid", "", "animateEntry", "getCategoryColor", "index", "(I)J", "getHeatmapColor", "intensity", "", "(F)J", "drawHeatmap", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "drawPieChart", "total", "drawSpendingChart", "animationProgress", "app_release"})
public final class VisualAnalyticsComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void SpendingTrendChart(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.SpendingDataPoint> data, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, boolean showGrid, boolean animateEntry) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CategorySpendingPieChart(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.CategorySpending> categories, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SpendingHeatmap(@org.jetbrains.annotations.NotNull
    java.util.List<? extends java.util.List<java.lang.Double>> heatmapData, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> dayLabels, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> hourLabels, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetVarianceChart(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.BudgetVarianceData> budgetData, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void BudgetVarianceBar(java.lang.String categoryName, double budgetAmount, double actualAmount, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void SpendingChartLegend(java.util.List<com.focusflow.ui.components.SpendingDataPoint> data) {
    }
    
    private static final void drawSpendingChart(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawSpendingChart, java.util.List<com.focusflow.ui.components.SpendingDataPoint> data, float animationProgress, boolean showGrid) {
    }
    
    private static final void drawPieChart(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawPieChart, java.util.List<com.focusflow.ui.components.CategorySpending> categories, double total) {
    }
    
    private static final void drawHeatmap(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawHeatmap, java.util.List<? extends java.util.List<java.lang.Double>> data, java.util.List<java.lang.String> dayLabels, java.util.List<java.lang.String> hourLabels) {
    }
    
    private static final long getCategoryColor(int index) {
        return 0L;
    }
    
    private static final long getHeatmapColor(float intensity) {
        return 0L;
    }
}
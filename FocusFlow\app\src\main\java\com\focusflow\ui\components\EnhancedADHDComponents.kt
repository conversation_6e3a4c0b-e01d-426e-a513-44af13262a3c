package com.focusflow.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.focusflow.utils.ADHDDesignValidator

/**
 * Enhanced ADHD-Friendly Components
 * Implements advanced ADHD design principles with improved feedback and accessibility
 */

/**
 * Enhanced ADHD-Friendly Action Button with comprehensive feedback
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun EnhancedADHDActionButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null,
    buttonStyle: ADHDButtonStyle = ADHDButtonStyle.PRIMARY,
    showLoadingState: Boolean = false,
    successMessage: String? = null
) {
    val haptic = LocalHapticFeedback.current
    var isPressed by remember { mutableStateOf(false) }
    var showSuccess by remember { mutableStateOf(false) }
    
    // Success animation
    LaunchedEffect(successMessage) {
        if (successMessage != null) {
            showSuccess = true
            kotlinx.coroutines.delay(2000)
            showSuccess = false
        }
    }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy)
    )
    
    val buttonColors = when (buttonStyle) {
        ADHDButtonStyle.PRIMARY -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.FOCUS_GREEN,
            contentColor = Color.White,
            disabledBackgroundColor = ADHDDesignValidator.Colors.DISABLED_TEXT
        )
        ADHDButtonStyle.SECONDARY -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.CALM_BLUE,
            contentColor = Color.White
        )
        ADHDButtonStyle.SUCCESS -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.SUCCESS_GREEN,
            contentColor = Color.White
        )
        ADHDButtonStyle.WARNING -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.WARNING_AMBER,
            contentColor = Color.Black
        )
        ADHDButtonStyle.DANGER -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.ERROR_RED,
            contentColor = Color.White
        )
    }
    
    Button(
        onClick = {
            if (enabled && !showLoadingState) {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onClick()
            }
        },
        modifier = modifier
            .scale(scale)
            .heightIn(min = ADHDDesignValidator.Standards.RECOMMENDED_TOUCH_TARGET_SIZE)
            .semantics {
                contentDescription = if (showLoadingState) "$text - Loading" else text
            },
        enabled = enabled && !showLoadingState,
        colors = buttonColors,
        shape = RoundedCornerShape(12.dp),
        elevation = ButtonDefaults.elevation(
            defaultElevation = 4.dp,
            pressedElevation = 2.dp,
            disabledElevation = 0.dp
        )
    ) {
        AnimatedContent(
            targetState = when {
                showSuccess -> "success"
                showLoadingState -> "loading"
                else -> "default"
            },
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) with fadeOut(animationSpec = tween(300))
            }
        ) { state ->
            when (state) {
                "success" -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            Icons.Default.CheckCircle,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = successMessage ?: "Success!",
                            fontWeight = FontWeight.Medium,
                            fontSize = 16.sp
                        )
                    }
                }
                "loading" -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = LocalContentColor.current,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Processing...",
                            fontWeight = FontWeight.Medium,
                            fontSize = 16.sp
                        )
                    }
                }
                else -> {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        icon?.let { iconVector ->
                            Icon(
                                iconVector,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Text(
                            text = text,
                            fontWeight = FontWeight.Medium,
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * Enhanced ADHD-Friendly Information Card with progressive disclosure
 */
@Composable
fun EnhancedADHDInfoCard(
    title: String,
    modifier: Modifier = Modifier,
    subtitle: String? = null,
    icon: ImageVector? = null,
    priority: ADHDPriority = ADHDPriority.NORMAL,
    expandable: Boolean = false,
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    var isExpanded by remember { mutableStateOf(!expandable) }
    val haptic = LocalHapticFeedback.current
    
    val cardBackgroundColor = when (priority) {
        ADHDPriority.HIGH -> ADHDDesignValidator.Colors.ERROR_RED.copy(alpha = 0.05f)
        ADHDPriority.MEDIUM -> ADHDDesignValidator.Colors.WARNING_AMBER.copy(alpha = 0.05f)
        ADHDPriority.NORMAL -> MaterialTheme.colors.surface
        ADHDPriority.LOW -> ADHDDesignValidator.Colors.CALM_BLUE.copy(alpha = 0.03f)
    }
    
    val borderColor = when (priority) {
        ADHDPriority.HIGH -> ADHDDesignValidator.Colors.ERROR_RED.copy(alpha = 0.3f)
        ADHDPriority.MEDIUM -> ADHDDesignValidator.Colors.WARNING_AMBER.copy(alpha = 0.3f)
        ADHDPriority.NORMAL -> Color.Transparent
        ADHDPriority.LOW -> ADHDDesignValidator.Colors.CALM_BLUE.copy(alpha = 0.2f)
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .then(
                if (onClick != null || expandable) {
                    Modifier.clickable {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        if (expandable) {
                            isExpanded = !isExpanded
                        }
                        onClick?.invoke()
                    }
                } else Modifier
            )
            .border(
                width = if (priority != ADHDPriority.NORMAL) 1.dp else 0.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            ),
        elevation = when (priority) {
            ADHDPriority.HIGH -> 8.dp
            ADHDPriority.MEDIUM -> 6.dp
            ADHDPriority.NORMAL -> 4.dp
            ADHDPriority.LOW -> 2.dp
        },
        shape = RoundedCornerShape(12.dp),
        backgroundColor = cardBackgroundColor
    ) {
        Column(
            modifier = Modifier.padding(ADHDDesignValidator.Standards.CARD_PADDING)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    icon?.let { iconVector ->
                        Icon(
                            iconVector,
                            contentDescription = null,
                            tint = when (priority) {
                                ADHDPriority.HIGH -> ADHDDesignValidator.Colors.ERROR_RED
                                ADHDPriority.MEDIUM -> ADHDDesignValidator.Colors.WARNING_AMBER
                                ADHDPriority.NORMAL -> MaterialTheme.colors.primary
                                ADHDPriority.LOW -> ADHDDesignValidator.Colors.CALM_BLUE
                            },
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                    }
                    
                    Column {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.h6,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colors.onSurface
                        )
                        subtitle?.let { sub ->
                            Text(
                                text = sub,
                                style = MaterialTheme.typography.body2,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    }
                }
                
                if (expandable) {
                    Icon(
                        if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (isExpanded) "Collapse" else "Expand",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
            
            // Content with animation
            AnimatedVisibility(
                visible = isExpanded,
                enter = expandVertically(
                    animationSpec = tween(300, easing = EaseOutCubic)
                ) + fadeIn(animationSpec = tween(300)),
                exit = shrinkVertically(
                    animationSpec = tween(300, easing = EaseInCubic)
                ) + fadeOut(animationSpec = tween(300))
            ) {
                Column {
                    if (expandable) {
                        Spacer(modifier = Modifier.height(12.dp))
                    }
                    content()
                }
            }
        }
    }
}

/**
 * Enhanced ADHD-Friendly Progress Indicator with motivational feedback
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun EnhancedADHDProgressIndicator(
    progress: Float,
    label: String,
    modifier: Modifier = Modifier,
    showPercentage: Boolean = true,
    motivationalMessages: List<String> = defaultMotivationalMessages,
    color: Color = ADHDDesignValidator.Colors.SUCCESS_GREEN,
    showMilestones: Boolean = true
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = 1000,
            easing = EaseOutCubic
        )
    )
    
    val currentMessage = remember(progress) {
        when {
            progress >= 1.0f -> motivationalMessages.getOrNull(4) ?: "🎉 Amazing! You did it!"
            progress >= 0.75f -> motivationalMessages.getOrNull(3) ?: "🌟 Almost there! Keep going!"
            progress >= 0.5f -> motivationalMessages.getOrNull(2) ?: "💪 Halfway there! You're doing great!"
            progress >= 0.25f -> motivationalMessages.getOrNull(1) ?: "🚀 Good progress! Keep it up!"
            else -> motivationalMessages.getOrNull(0) ?: "🌱 Every step counts! You've got this!"
        }
    }
    
    Column(modifier = modifier) {
        // Header with label and percentage
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.body1,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colors.onSurface
            )
            
            if (showPercentage) {
                Text(
                    text = "${(animatedProgress * 100).toInt()}%",
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Progress bar with milestones
        Box(modifier = Modifier.fillMaxWidth()) {
            LinearProgressIndicator(
                progress = animatedProgress,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(12.dp)
                    .clip(RoundedCornerShape(6.dp)),
                color = color,
                backgroundColor = color.copy(alpha = 0.2f)
            )
            
            // Milestone markers
            if (showMilestones) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    listOf(0.25f, 0.5f, 0.75f, 1.0f).forEach { milestone ->
                        Box(
                            modifier = Modifier
                                .size(4.dp)
                                .background(
                                    color = if (animatedProgress >= milestone) Color.White else Color.Transparent,
                                    shape = RoundedCornerShape(2.dp)
                                )
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Motivational message
        AnimatedContent(
            targetState = currentMessage,
            transitionSpec = {
                slideInVertically { it } + fadeIn() with slideOutVertically { -it } + fadeOut()
            }
        ) { message ->
            Text(
                text = message,
                style = MaterialTheme.typography.body2,
                color = color,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * Enhanced ADHD-Friendly Status Badge with clear visual hierarchy
 */
@Composable
fun EnhancedADHDStatusBadge(
    status: ADHDStatus,
    text: String,
    modifier: Modifier = Modifier,
    showIcon: Boolean = true,
    size: ADHDBadgeSize = ADHDBadgeSize.MEDIUM
) {
    val (backgroundColor, contentColor, icon) = when (status) {
        ADHDStatus.SUCCESS -> Triple(
            ADHDDesignValidator.Colors.SUCCESS_GREEN,
            Color.White,
            Icons.Default.CheckCircle
        )
        ADHDStatus.WARNING -> Triple(
            ADHDDesignValidator.Colors.WARNING_AMBER,
            Color.Black,
            Icons.Default.Warning
        )
        ADHDStatus.ERROR -> Triple(
            ADHDDesignValidator.Colors.ERROR_RED,
            Color.White,
            Icons.Default.Error
        )
        ADHDStatus.INFO -> Triple(
            ADHDDesignValidator.Colors.CALM_BLUE,
            Color.White,
            Icons.Default.Info
        )
    }
    
    val (padding, iconSize, textStyle) = when (size) {
        ADHDBadgeSize.SMALL -> Triple(
            PaddingValues(horizontal = 8.dp, vertical = 4.dp),
            16.dp,
            MaterialTheme.typography.caption
        )
        ADHDBadgeSize.MEDIUM -> Triple(
            PaddingValues(horizontal = 12.dp, vertical = 6.dp),
            18.dp,
            MaterialTheme.typography.body2
        )
        ADHDBadgeSize.LARGE -> Triple(
            PaddingValues(horizontal = 16.dp, vertical = 8.dp),
            20.dp,
            MaterialTheme.typography.body1
        )
    }
    
    Surface(
        modifier = modifier,
        color = backgroundColor,
        contentColor = contentColor,
        shape = RoundedCornerShape(50),
        elevation = 2.dp
    ) {
        Row(
            modifier = Modifier.padding(padding),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (showIcon) {
                Icon(
                    icon,
                    contentDescription = null,
                    modifier = Modifier.size(iconSize)
                )
                Spacer(modifier = Modifier.width(4.dp))
            }
            Text(
                text = text,
                style = textStyle,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

// Supporting enums and data classes
enum class ADHDButtonStyle { PRIMARY, SECONDARY, SUCCESS, WARNING, DANGER }
enum class ADHDPriority { HIGH, MEDIUM, NORMAL, LOW }
enum class ADHDStatus { SUCCESS, WARNING, ERROR, INFO }
enum class ADHDBadgeSize { SMALL, MEDIUM, LARGE }

private val defaultMotivationalMessages = listOf(
    "🌱 Every step counts! You've got this!",
    "🚀 Good progress! Keep it up!",
    "💪 Halfway there! You're doing great!",
    "🌟 Almost there! Keep going!",
    "🎉 Amazing! You did it!"
)

package com.focusflow.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.SettingsViewModel
import com.focusflow.ui.components.*
import android.Manifest
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts

@Composable
fun SettingsScreen(
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // Notification permission launcher
    val notificationPermissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            viewModel.initializeNotifications()
        }
        viewModel.checkNotificationPermission()
    }

    // Check notification permission on first load
    LaunchedEffect(Unit) {
        viewModel.checkNotificationPermission()
    }
    
    var showThemeDialog by remember { mutableStateOf(false) }
    var showFontSizeDialog by remember { mutableStateOf(false) }
    var showNotificationTimeDialog by remember { mutableStateOf(false) }
    var showBudgetPeriodDialog by remember { mutableStateOf(false) }
    var showGoalDialog by remember { mutableStateOf(false) }
    var showExportDialog by remember { mutableStateOf(false) }
    var showClearDataDialog by remember { mutableStateOf(false) }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            Text(
                text = "Settings",
                style = MaterialTheme.typography.h4,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }

        // Account & Profile Section
        item {
            SettingsSection(
                title = "Account & Profile",
                icon = Icons.Default.Person
            ) {
                SettingsItem(
                    title = "Profile Information",
                    subtitle = "Manage your account details",
                    icon = Icons.Default.AccountCircle,
                    onClick = { /* TODO: Implement profile management */ }
                )
            }
        }

        // Notifications & Reminders Section
        item {
            SettingsSection(
                title = "Notifications & Reminders",
                icon = Icons.Default.Notifications
            ) {
                SettingsToggleItem(
                    title = "Enable Notifications",
                    subtitle = if (uiState.hasNotificationPermission) {
                        "Get helpful reminders and updates"
                    } else {
                        "Permission required - tap to enable"
                    },
                    icon = Icons.Default.Notifications,
                    checked = uiState.userPreferences.notificationsEnabled && uiState.hasNotificationPermission,
                    onCheckedChange = { enabled ->
                        if (enabled && !uiState.hasNotificationPermission) {
                            // Request permission first
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                                notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                            }
                        } else {
                            viewModel.updateNotificationsEnabled(enabled)
                        }
                    }
                )

                // Show permission warning if needed
                if (!uiState.hasNotificationPermission && uiState.notificationPermissionChecked) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp),
                        elevation = 1.dp,
                        backgroundColor = MaterialTheme.colors.error.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Row(
                            modifier = Modifier.padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = null,
                                tint = MaterialTheme.colors.error,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Notification permission is required for reminders to work",
                                style = MaterialTheme.typography.caption,
                                color = MaterialTheme.colors.error
                            )
                        }
                    }
                }
                
                if (uiState.userPreferences.notificationsEnabled) {
                    SettingsItem(
                        title = "Notification Time",
                        subtitle = "Daily reminder time: ${uiState.userPreferences.reminderTime}",
                        icon = Icons.Default.Notifications,
                        onClick = { showNotificationTimeDialog = true }
                    )
                }
            }
        }

        // Appearance & Accessibility Section
        item {
            SettingsSection(
                title = "Appearance & Accessibility",
                icon = Icons.Default.Settings
            ) {
                SettingsItem(
                    title = "Theme",
                    subtitle = "Current: ${uiState.userPreferences.theme.replaceFirstChar { it.uppercase() }}",
                    icon = Icons.Default.Settings,
                    onClick = { showThemeDialog = true }
                )
                
                SettingsItem(
                    title = "Font Size",
                    subtitle = "Current: ${uiState.userPreferences.fontSize.replaceFirstChar { it.uppercase() }}",
                    icon = Icons.Default.Settings,
                    onClick = { showFontSizeDialog = true }
                )
            }
        }

        // App Behavior Section
        item {
            SettingsSection(
                title = "App Behavior",
                icon = Icons.Default.Settings
            ) {
                SettingsItem(
                    title = "Budget Period",
                    subtitle = "Current: ${uiState.userPreferences.budgetPeriod.replaceFirstChar { it.uppercase() }}",
                    icon = Icons.Default.DateRange,
                    onClick = { showBudgetPeriodDialog = true }
                )
                
                SettingsItem(
                    title = "Primary Goal",
                    subtitle = uiState.userPreferences.primaryGoal?.replaceFirstChar { it.uppercase() } ?: "Not set",
                    icon = Icons.Default.Star,
                    onClick = { showGoalDialog = true }
                )
            }
        }

        // Data & Privacy Section
        item {
            SettingsSection(
                title = "Data & Privacy",
                icon = Icons.Default.Lock
            ) {
                SettingsItem(
                    title = "Export Data",
                    subtitle = "Download your financial data",
                    icon = Icons.Default.Add,
                    onClick = { showExportDialog = true }
                )
                
                SettingsItem(
                    title = "Clear All Data",
                    subtitle = "Reset app to initial state",
                    icon = Icons.Default.Delete,
                    onClick = { showClearDataDialog = true },
                    textColor = MaterialTheme.colors.error
                )
                
                SettingsItem(
                    title = "Privacy Policy",
                    subtitle = "View our privacy policy",
                    icon = Icons.Default.Info,
                    onClick = { /* TODO: Open privacy policy */ }
                )
            }
        }

        // Help & Support Section
        item {
            SettingsSection(
                title = "Help & Support",
                icon = Icons.Default.Info
            ) {
                SettingsItem(
                    title = "FAQ",
                    subtitle = "Frequently asked questions",
                    icon = Icons.Default.Info,
                    onClick = { /* TODO: Open FAQ */ }
                )
                
                SettingsItem(
                    title = "Contact Support",
                    subtitle = "Get help with the app",
                    icon = Icons.Default.Email,
                    onClick = { /* TODO: Open contact support */ }
                )
                
                SettingsItem(
                    title = "App Version",
                    subtitle = "1.0.0 (Beta)",
                    icon = Icons.Default.Info,
                    onClick = { }
                )
            }
        }
    }

    // Handle dialogs
    if (showThemeDialog) {
        ThemeSelectionDialog(
            currentTheme = uiState.userPreferences.theme,
            onThemeSelected = { theme ->
                viewModel.updateTheme(theme)
                showThemeDialog = false
            },
            onDismiss = { showThemeDialog = false }
        )
    }

    if (showFontSizeDialog) {
        FontSizeSelectionDialog(
            currentFontSize = uiState.userPreferences.fontSize,
            onFontSizeSelected = { fontSize ->
                viewModel.updateFontSize(fontSize)
                showFontSizeDialog = false
            },
            onDismiss = { showFontSizeDialog = false }
        )
    }

    if (showNotificationTimeDialog) {
        NotificationTimeDialog(
            currentTime = uiState.userPreferences.reminderTime,
            onTimeSelected = { time ->
                viewModel.updateNotificationTime(time)
                showNotificationTimeDialog = false
            },
            onDismiss = { showNotificationTimeDialog = false }
        )
    }

    if (showBudgetPeriodDialog) {
        BudgetPeriodDialog(
            currentPeriod = uiState.userPreferences.budgetPeriod,
            onPeriodSelected = { period ->
                viewModel.updateBudgetPeriod(period)
                showBudgetPeriodDialog = false
            },
            onDismiss = { showBudgetPeriodDialog = false }
        )
    }

    if (showGoalDialog) {
        GoalSelectionDialog(
            currentGoal = uiState.userPreferences.primaryGoal,
            onGoalSelected = { goal ->
                viewModel.updatePrimaryGoal(goal)
                showGoalDialog = false
            },
            onDismiss = { showGoalDialog = false }
        )
    }

    if (showExportDialog) {
        ExportDataDialog(
            isExporting = uiState.isExporting,
            onExport = { viewModel.exportData() },
            onDismiss = { 
                showExportDialog = false
                viewModel.clearMessages()
            }
        )
    }

    if (showClearDataDialog) {
        ClearDataDialog(
            isClearing = uiState.isClearing,
            onClear = { viewModel.clearData() },
            onDismiss = { 
                showClearDataDialog = false
                viewModel.clearMessages()
            }
        )
    }

    // Handle error messages
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            viewModel.clearError()
        }

        // Show error card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            backgroundColor = MaterialTheme.colors.error.copy(alpha = 0.1f),
            elevation = 2.dp
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colors.error,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = error,
                    color = MaterialTheme.colors.error,
                    style = MaterialTheme.typography.body2,
                    modifier = Modifier.weight(1f)
                )
                IconButton(
                    onClick = { viewModel.clearError() },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "Dismiss",
                        tint = MaterialTheme.colors.error,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }

    // Handle success messages
    uiState.exportMessage?.let { message ->
        LaunchedEffect(message) {
            kotlinx.coroutines.delay(3000)
            viewModel.clearMessages()
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f),
            elevation = 2.dp
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = message,
                    color = MaterialTheme.colors.primary,
                    style = MaterialTheme.typography.body2
                )
            }
        }
    }

    uiState.clearMessage?.let { message ->
        LaunchedEffect(message) {
            kotlinx.coroutines.delay(3000)
            viewModel.clearMessages()
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f),
            elevation = 2.dp
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = message,
                    color = MaterialTheme.colors.primary,
                    style = MaterialTheme.typography.body2
                )
            }
        }
    }
}

@Composable
fun SettingsSection(
    title: String,
    icon: ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colors.primary
                )
            }
            content()
        }
    }
}

@Composable
fun SettingsItem(
    title: String,
    subtitle: String,
    icon: ImageVector,
    onClick: () -> Unit,
    textColor: Color = MaterialTheme.colors.onSurface
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = textColor,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium,
                    color = textColor
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.body2,
                    color = textColor.copy(alpha = 0.7f)
                )
            }
            IconButton(onClick = onClick) {
                Icon(
                    imageVector = Icons.Default.KeyboardArrowRight,
                    contentDescription = "Open",
                    tint = MaterialTheme.colors.onSurface.copy(alpha = 0.5f)
                )
            }
        }
    }
}

@Composable
fun SettingsToggleItem(
    title: String,
    subtitle: String,
    icon: ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colors.onSurface,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
            Switch(
                checked = checked,
                onCheckedChange = onCheckedChange,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colors.primary
                )
            )
        }
    }
}

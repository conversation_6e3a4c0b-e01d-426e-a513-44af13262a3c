package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b=\b\u0087\b\u0018\u00002\u00020\u0001B\u00cf\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\b\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\f\u0012\u0006\u0010\u000f\u001a\u00020\f\u0012\u0006\u0010\u0010\u001a\u00020\u0005\u0012\u0006\u0010\u0011\u001a\u00020\f\u0012\u0006\u0010\u0012\u001a\u00020\b\u0012\u0006\u0010\u0013\u001a\u00020\f\u0012\u0006\u0010\u0014\u001a\u00020\f\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0016\u001a\u00020\u0017\u0012\u0006\u0010\u0018\u001a\u00020\b\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\f\u0012\b\b\u0002\u0010\u001b\u001a\u00020\f\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u001d\u00a2\u0006\u0002\u0010\u001eJ\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\t\u0010?\u001a\u00020\fH\u00c6\u0003J\t\u0010@\u001a\u00020\u0005H\u00c6\u0003J\t\u0010A\u001a\u00020\fH\u00c6\u0003J\t\u0010B\u001a\u00020\bH\u00c6\u0003J\t\u0010C\u001a\u00020\fH\u00c6\u0003J\t\u0010D\u001a\u00020\fH\u00c6\u0003J\u000b\u0010E\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010F\u001a\u00020\u0017H\u00c6\u0003J\t\u0010G\u001a\u00020\bH\u00c6\u0003J\u0010\u0010H\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u00105J\t\u0010I\u001a\u00020\u0005H\u00c6\u0003J\u0010\u0010J\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u00105J\t\u0010K\u001a\u00020\fH\u00c6\u0003J\t\u0010L\u001a\u00020\u001dH\u00c6\u0003J\t\u0010M\u001a\u00020\u0005H\u00c6\u0003J\t\u0010N\u001a\u00020\bH\u00c6\u0003J\u0010\u0010O\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\u0010\u0010P\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\t\u0010Q\u001a\u00020\fH\u00c6\u0003J\t\u0010R\u001a\u00020\fH\u00c6\u0003J\t\u0010S\u001a\u00020\fH\u00c6\u0003J\u00f4\u0001\u0010T\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\f2\b\b\u0002\u0010\u0010\u001a\u00020\u00052\b\b\u0002\u0010\u0011\u001a\u00020\f2\b\b\u0002\u0010\u0012\u001a\u00020\b2\b\b\u0002\u0010\u0013\u001a\u00020\f2\b\b\u0002\u0010\u0014\u001a\u00020\f2\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\u0018\u001a\u00020\b2\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\f2\b\b\u0002\u0010\u001b\u001a\u00020\f2\b\b\u0002\u0010\u001c\u001a\u00020\u001dH\u00c6\u0001\u00a2\u0006\u0002\u0010UJ\u0013\u0010V\u001a\u00020\u001d2\b\u0010W\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010X\u001a\u00020\bH\u00d6\u0001J\t\u0010Y\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u0011\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010 R\u0015\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\"\u0010#R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0015\u0010\n\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\'\u0010#R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010&R\u0011\u0010\u0018\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010)R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u001c\u001a\u00020\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u00100R\u0011\u0010\u0013\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010 R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010&R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010 R\u0015\u0010\u0019\u001a\u0004\u0018\u00010\f\u00a2\u0006\n\n\u0002\u00106\u001a\u0004\b4\u00105R\u0015\u0010\u001a\u001a\u0004\u0018\u00010\f\u00a2\u0006\n\n\u0002\u00106\u001a\u0004\b7\u00105R\u0011\u0010\u001b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010 R\u0011\u0010\u0014\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010 R\u0011\u0010\u0012\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010)R\u0011\u0010\u0010\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010&R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010 R\u0011\u0010\u000f\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010 \u00a8\u0006Z"}, d2 = {"Lcom/focusflow/data/model/BudgetAnalytics;", "", "id", "", "categoryName", "", "budgetPeriod", "budgetYear", "", "budgetMonth", "budgetWeek", "plannedAmount", "", "actualSpent", "variance", "variancePercentage", "trendDirection", "averageTransactionSize", "transactionCount", "largestTransaction", "smallestTransaction", "mostFrequentMerchant", "calculatedDate", "Lkotlinx/datetime/LocalDateTime;", "daysInPeriod", "projectedEndAmount", "recommendedAdjustment", "seasonalityFactor", "isOutlierPeriod", "", "(JLjava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Ljava/lang/Integer;DDDDLjava/lang/String;DIDDLjava/lang/String;Lkotlinx/datetime/LocalDateTime;ILjava/lang/Double;Ljava/lang/Double;DZ)V", "getActualSpent", "()D", "getAverageTransactionSize", "getBudgetMonth", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getBudgetPeriod", "()Ljava/lang/String;", "getBudgetWeek", "getBudgetYear", "()I", "getCalculatedDate", "()Lkotlinx/datetime/LocalDateTime;", "getCategoryName", "getDaysInPeriod", "getId", "()J", "()Z", "getLargestTransaction", "getMostFrequentMerchant", "getPlannedAmount", "getProjectedEndAmount", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getRecommendedAdjustment", "getSeasonalityFactor", "getSmallestTransaction", "getTransactionCount", "getTrendDirection", "getVariance", "getVariancePercentage", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Ljava/lang/Integer;DDDDLjava/lang/String;DIDDLjava/lang/String;Lkotlinx/datetime/LocalDateTime;ILjava/lang/Double;Ljava/lang/Double;DZ)Lcom/focusflow/data/model/BudgetAnalytics;", "equals", "other", "hashCode", "toString", "app_release"})
@androidx.room.Entity(tableName = "budget_analytics")
public final class BudgetAnalytics {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String categoryName = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String budgetPeriod = null;
    private final int budgetYear = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer budgetMonth = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer budgetWeek = null;
    private final double plannedAmount = 0.0;
    private final double actualSpent = 0.0;
    private final double variance = 0.0;
    private final double variancePercentage = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String trendDirection = null;
    private final double averageTransactionSize = 0.0;
    private final int transactionCount = 0;
    private final double largestTransaction = 0.0;
    private final double smallestTransaction = 0.0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String mostFrequentMerchant = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime calculatedDate = null;
    private final int daysInPeriod = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double projectedEndAmount = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double recommendedAdjustment = null;
    private final double seasonalityFactor = 0.0;
    private final boolean isOutlierPeriod = false;
    
    public BudgetAnalytics(long id, @org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, int budgetYear, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetMonth, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetWeek, double plannedAmount, double actualSpent, double variance, double variancePercentage, @org.jetbrains.annotations.NotNull
    java.lang.String trendDirection, double averageTransactionSize, int transactionCount, double largestTransaction, double smallestTransaction, @org.jetbrains.annotations.Nullable
    java.lang.String mostFrequentMerchant, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime calculatedDate, int daysInPeriod, @org.jetbrains.annotations.Nullable
    java.lang.Double projectedEndAmount, @org.jetbrains.annotations.Nullable
    java.lang.Double recommendedAdjustment, double seasonalityFactor, boolean isOutlierPeriod) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCategoryName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getBudgetPeriod() {
        return null;
    }
    
    public final int getBudgetYear() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getBudgetMonth() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getBudgetWeek() {
        return null;
    }
    
    public final double getPlannedAmount() {
        return 0.0;
    }
    
    public final double getActualSpent() {
        return 0.0;
    }
    
    public final double getVariance() {
        return 0.0;
    }
    
    public final double getVariancePercentage() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTrendDirection() {
        return null;
    }
    
    public final double getAverageTransactionSize() {
        return 0.0;
    }
    
    public final int getTransactionCount() {
        return 0;
    }
    
    public final double getLargestTransaction() {
        return 0.0;
    }
    
    public final double getSmallestTransaction() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getMostFrequentMerchant() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getCalculatedDate() {
        return null;
    }
    
    public final int getDaysInPeriod() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getProjectedEndAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getRecommendedAdjustment() {
        return null;
    }
    
    public final double getSeasonalityFactor() {
        return 0.0;
    }
    
    public final boolean isOutlierPeriod() {
        return false;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final double component10() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component11() {
        return null;
    }
    
    public final double component12() {
        return 0.0;
    }
    
    public final int component13() {
        return 0;
    }
    
    public final double component14() {
        return 0.0;
    }
    
    public final double component15() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component17() {
        return null;
    }
    
    public final int component18() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component20() {
        return null;
    }
    
    public final double component21() {
        return 0.0;
    }
    
    public final boolean component22() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component6() {
        return null;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final double component9() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.BudgetAnalytics copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, int budgetYear, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetMonth, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetWeek, double plannedAmount, double actualSpent, double variance, double variancePercentage, @org.jetbrains.annotations.NotNull
    java.lang.String trendDirection, double averageTransactionSize, int transactionCount, double largestTransaction, double smallestTransaction, @org.jetbrains.annotations.Nullable
    java.lang.String mostFrequentMerchant, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime calculatedDate, int daysInPeriod, @org.jetbrains.annotations.Nullable
    java.lang.Double projectedEndAmount, @org.jetbrains.annotations.Nullable
    java.lang.Double recommendedAdjustment, double seasonalityFactor, boolean isOutlierPeriod) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
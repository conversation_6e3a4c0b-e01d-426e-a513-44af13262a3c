package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\t\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ:\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0012J\u0016\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0012\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00190\u0018J\u001a\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00190\u00182\u0006\u0010\u000b\u001a\u00020\fJ\u001a\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00190\u00182\u0006\u0010\u001c\u001a\u00020\fJ<\u0010\u001d\u001a\u0004\u0018\u00010\b2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0012J1\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00190\u00182\u0006\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\u0002\u0010\u001fJ\u0018\u0010 \u001a\u0004\u0018\u00010!2\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\"J\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020$0\u0019H\u0086@\u00a2\u0006\u0002\u0010%J\u001a\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00190\u00182\u0006\u0010\'\u001a\u00020!J\u000e\u0010(\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010%J\u000e\u0010)\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010%J\u0012\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00190\u0018J$\u0010+\u001a\b\u0012\u0004\u0012\u00020\b0\u00192\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010,\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010-J\u0016\u0010.\u001a\u00020/2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u00100\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/focusflow/data/repository/BudgetAnalyticsRepository;", "", "budgetAnalyticsDao", "Lcom/focusflow/data/dao/BudgetAnalyticsDao;", "(Lcom/focusflow/data/dao/BudgetAnalyticsDao;)V", "deleteAnalytics", "", "analytics", "Lcom/focusflow/data/model/BudgetAnalytics;", "(Lcom/focusflow/data/model/BudgetAnalytics;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalyticsForPeriod", "categoryName", "", "period", "year", "", "month", "week", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalyticsOlderThan", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAnalytics", "Lkotlinx/coroutines/flow/Flow;", "", "getAnalyticsByCategory", "getAnalyticsByTrend", "direction", "getAnalyticsForCategoryAndPeriod", "getAnalyticsForPeriod", "(Ljava/lang/String;ILjava/lang/Integer;)Lkotlinx/coroutines/flow/Flow;", "getAverageVarianceForCategory", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategoryVarianceAverages", "Lcom/focusflow/data/dao/CategoryVarianceAverage;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHighVarianceAnalytics", "threshold", "getHighVarianceCount", "getIncreasingTrendCount", "getOutlierPeriods", "getRecentAnalyticsForCategory", "limit", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertAnalytics", "", "updateAnalytics", "app_release"})
public final class BudgetAnalyticsRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.BudgetAnalyticsDao budgetAnalyticsDao = null;
    
    @javax.inject.Inject
    public BudgetAnalyticsRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.BudgetAnalyticsDao budgetAnalyticsDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAllAnalytics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAnalyticsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAnalyticsForPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, @org.jetbrains.annotations.Nullable
    java.lang.Integer month) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAnalyticsForCategoryAndPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    java.lang.String period, int year, @org.jetbrains.annotations.Nullable
    java.lang.Integer month, @org.jetbrains.annotations.Nullable
    java.lang.Integer week, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetAnalytics> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getHighVarianceAnalytics(double threshold) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAnalyticsByTrend(@org.jetbrains.annotations.NotNull
    java.lang.String direction) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getOutlierPeriods() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getCategoryVarianceAverages(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.dao.CategoryVarianceAverage>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAverageVarianceForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getRecentAnalyticsForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, int limit, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.BudgetAnalytics>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getHighVarianceCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getIncreasingTrendCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertAnalytics(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetAnalytics analytics, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateAnalytics(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetAnalytics analytics, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteAnalytics(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetAnalytics analytics, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteAnalyticsOlderThan(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteAnalyticsForPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    java.lang.String period, int year, @org.jetbrains.annotations.Nullable
    java.lang.Integer month, @org.jetbrains.annotations.Nullable
    java.lang.Integer week, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}
package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.CreditCard
import com.focusflow.data.repository.CreditCardRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class DebtViewModel @Inject constructor(
    private val creditCardRepository: CreditCardRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DebtUiState())
    val uiState: StateFlow<DebtUiState> = _uiState.asStateFlow()

    private val _payoffPlan = MutableStateFlow(PayoffPlan(emptyList(), 0, 0.0, 0.0))
    val payoffPlan: StateFlow<PayoffPlan> = _payoffPlan.asStateFlow()

    val allCreditCards = creditCardRepository.getAllActiveCreditCards()

    init {
        loadDebtData()
    }

    private fun loadDebtData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val totalDebt = creditCardRepository.getTotalDebt()
                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                val nextWeek = today.plus(7, DateTimeUnit.DAY)
                val totalMinimumPayments = creditCardRepository.getTotalMinimumPaymentsDue(nextWeek)
                
                _uiState.value = _uiState.value.copy(
                    totalDebt = totalDebt,
                    totalMinimumPayments = totalMinimumPayments,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load debt data: ${e.message}"
                )
            }
        }
    }

    fun addCreditCard(
        name: String,
        currentBalance: Double,
        creditLimit: Double,
        minimumPayment: Double,
        dueDate: LocalDate,
        interestRate: Double
    ) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val creditCard = CreditCard(
                    name = name,
                    currentBalance = currentBalance,
                    creditLimit = creditLimit,
                    minimumPayment = minimumPayment,
                    dueDate = dueDate,
                    interestRate = interestRate
                )
                
                creditCardRepository.insertCreditCard(creditCard)
                loadDebtData()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to add credit card: ${e.message}"
                )
            }
        }
    }

    fun updateCreditCard(creditCard: CreditCard) {
        viewModelScope.launch {
            try {
                creditCardRepository.updateCreditCard(creditCard)
                loadDebtData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update credit card: ${e.message}"
                )
            }
        }
    }

    fun updateCreditCard(
        cardId: Long,
        name: String,
        currentBalance: Double,
        creditLimit: Double,
        minimumPayment: Double,
        dueDate: LocalDate,
        interestRate: Double
    ) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                val existingCard = creditCardRepository.getCreditCardById(cardId)
                if (existingCard != null) {
                    val updatedCard = existingCard.copy(
                        name = name,
                        currentBalance = currentBalance,
                        creditLimit = creditLimit,
                        minimumPayment = minimumPayment,
                        dueDate = dueDate,
                        interestRate = interestRate
                    )

                    creditCardRepository.updateCreditCard(updatedCard)
                    loadDebtData()
                }

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to update credit card: ${e.message}"
                )
            }
        }
    }

    fun makePayment(cardId: Long, paymentAmount: Double) {
        viewModelScope.launch {
            try {
                val card = creditCardRepository.getCreditCardById(cardId)
                if (card != null) {
                    val updatedCard = card.copy(
                        currentBalance = (card.currentBalance - paymentAmount).coerceAtLeast(0.0),
                        lastPaymentAmount = paymentAmount,
                        lastPaymentDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                    )
                    creditCardRepository.updateCreditCard(updatedCard)
                    loadDebtData()
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to process payment: ${e.message}"
                )
            }
        }
    }

    fun deleteCreditCard(creditCard: CreditCard) {
        viewModelScope.launch {
            try {
                creditCardRepository.deleteCreditCard(creditCard)
                loadDebtData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete credit card: ${e.message}"
                )
            }
        }
    }

    fun calculatePayoffTime(balance: Double, monthlyPayment: Double, interestRate: Double): PayoffCalculation {
        if (monthlyPayment <= 0 || balance <= 0) {
            return PayoffCalculation(0, 0.0)
        }
        
        val monthlyInterestRate = interestRate / 100 / 12
        
        if (monthlyPayment <= balance * monthlyInterestRate) {
            // Payment doesn't cover interest - will never pay off
            return PayoffCalculation(-1, Double.MAX_VALUE)
        }
        
        var remainingBalance = balance
        var months = 0
        var totalInterest = 0.0
        
        while (remainingBalance > 0.01 && months < 600) { // Cap at 50 years
            val interestPayment = remainingBalance * monthlyInterestRate
            val principalPayment = (monthlyPayment - interestPayment).coerceAtMost(remainingBalance)
            
            totalInterest += interestPayment
            remainingBalance -= principalPayment
            months++
        }
        
        return PayoffCalculation(months, totalInterest)
    }

    fun generatePayoffPlan(strategy: PayoffStrategy, extraPayment: Double = 0.0) {
        viewModelScope.launch {
            try {
                allCreditCards.collect { cards ->
                    if (cards.isEmpty()) {
                        _payoffPlan.value = PayoffPlan(emptyList(), 0, 0.0, 0.0)
                        return@collect
                    }

                    val plan = calculateDetailedPayoffPlan(cards, strategy, extraPayment)
                    _payoffPlan.value = plan
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to generate payoff plan: ${e.message}"
                )
            }
        }
    }

    private fun calculateDetailedPayoffPlan(
        cards: List<CreditCard>,
        strategy: PayoffStrategy,
        extraPayment: Double
    ): PayoffPlan {
        if (cards.isEmpty()) {
            return PayoffPlan(emptyList(), 0, 0.0, 0.0)
        }

        // Sort cards based on strategy
        val sortedCards = when (strategy) {
            PayoffStrategy.AVALANCHE -> cards.sortedByDescending { it.interestRate }
            PayoffStrategy.SNOWBALL -> cards.sortedBy { it.currentBalance }
        }

        val steps = mutableListOf<PayoffStep>()
        val cardBalances = sortedCards.associate { it.id to it.currentBalance }.toMutableMap()
        val totalMinimumPayments = sortedCards.sumOf { it.minimumPayment }
        val totalAvailablePayment = totalMinimumPayments + extraPayment

        var month = 1
        var totalInterestPaid = 0.0

        while (cardBalances.values.any { it > 0.01 } && month <= 600) { // Cap at 50 years
            var remainingExtraPayment = extraPayment

            // Make minimum payments on all cards first
            for (card in sortedCards) {
                val currentBalance = cardBalances[card.id] ?: 0.0
                if (currentBalance <= 0.01) continue

                val monthlyInterest = currentBalance * (card.interestRate / 100 / 12)
                val minimumPayment = card.minimumPayment.coerceAtMost(currentBalance + monthlyInterest)
                val principalPayment = (minimumPayment - monthlyInterest).coerceAtLeast(0.0)

                totalInterestPaid += monthlyInterest
                cardBalances[card.id] = (currentBalance - principalPayment).coerceAtLeast(0.0)

                steps.add(PayoffStep(
                    month = month,
                    cardName = card.name,
                    payment = minimumPayment,
                    remainingBalance = cardBalances[card.id] ?: 0.0,
                    interestPaid = monthlyInterest,
                    principalPaid = principalPayment,
                    isExtraPayment = false
                ))
            }

            // Apply extra payment to target card (first card with balance in sorted order)
            val targetCard = sortedCards.firstOrNull { (cardBalances[it.id] ?: 0.0) > 0.01 }
            if (targetCard != null && remainingExtraPayment > 0) {
                val currentBalance = cardBalances[targetCard.id] ?: 0.0
                val extraPaymentAmount = remainingExtraPayment.coerceAtMost(currentBalance)

                cardBalances[targetCard.id] = (currentBalance - extraPaymentAmount).coerceAtLeast(0.0)

                steps.add(PayoffStep(
                    month = month,
                    cardName = "${targetCard.name} (Extra)",
                    payment = extraPaymentAmount,
                    remainingBalance = cardBalances[targetCard.id] ?: 0.0,
                    interestPaid = 0.0,
                    principalPaid = extraPaymentAmount,
                    isExtraPayment = true
                ))
            }

            month++
        }

        val totalMonths = month - 1
        val totalPayments = totalAvailablePayment * totalMonths

        return PayoffPlan(
            steps = steps,
            totalMonths = totalMonths,
            totalInterestPaid = totalInterestPaid,
            totalPayments = totalPayments
        )
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class DebtUiState(
    val totalDebt: Double = 0.0,
    val totalMinimumPayments: Double = 0.0,
    val isLoading: Boolean = false,
    val error: String? = null
)

data class PayoffCalculation(
    val months: Int, // -1 if never pays off
    val totalInterest: Double
)

data class PayoffStep(
    val month: Int,
    val cardName: String,
    val payment: Double,
    val remainingBalance: Double,
    val interestPaid: Double = 0.0,
    val principalPaid: Double = 0.0,
    val isExtraPayment: Boolean = false
)

data class PayoffPlan(
    val steps: List<PayoffStep>,
    val totalMonths: Int,
    val totalInterestPaid: Double,
    val totalPayments: Double
)

enum class PayoffStrategy {
    SNOWBALL, // Pay smallest balance first
    AVALANCHE // Pay highest interest rate first
}


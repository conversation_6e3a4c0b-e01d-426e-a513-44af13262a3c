package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\n\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0012\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00120\u0011J\u0012\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011J\u001e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0016\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0017J\"\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001aJ\u001a\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u0007\u001a\u00020\bJ*\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001aJ\u001a\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u001f\u001a\u00020\u001aJ\u0016\u0010 \u001a\u00020!2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0012\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011J\u001a\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u0007\u001a\u00020\bJ\u0012\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011J\u0012\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u0011J\u001a\u0010&\u001a\u0016\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\b\u0012\u0006\u0012\u0004\u0018\u00010\u000e0\'0\u0011J\u001a\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\u00120\u00112\u0006\u0010\u0007\u001a\u00020\bJ\u0016\u0010)\u001a\u00020*2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ2\u0010+\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001a2\u0006\u0010,\u001a\u00020\b2\u0006\u0010-\u001a\u00020\u00062\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010/J6\u00100\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001a2\u0006\u00101\u001a\u0002022\n\b\u0002\u00103\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u00104J*\u00105\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001a2\u0006\u00106\u001a\u00020\u00062\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u00107J2\u00108\u001a\u00020*2\u0006\u0010\u001f\u001a\u00020\u001a2\u0006\u00109\u001a\u00020\u00152\u0006\u0010:\u001a\u00020\u00062\n\b\u0002\u0010.\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010;J\u001a\u0010<\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\u00060=2\u0006\u0010>\u001a\u00020\bJ\u001a\u0010?\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00060=2\u0006\u0010>\u001a\u00020\bJ\u0016\u0010@\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010A\u001a\u0002022\u0006\u0010>\u001a\u00020\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006B"}, d2 = {"Lcom/focusflow/data/repository/HabitRepository;", "", "habitLogDao", "Lcom/focusflow/data/dao/HabitLogDao;", "(Lcom/focusflow/data/dao/HabitLogDao;)V", "calculateCurrentStreak", "", "habitType", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateLongestStreak", "deleteHabitLog", "", "habitLog", "Lcom/focusflow/data/model/HabitLog;", "(Lcom/focusflow/data/model/HabitLog;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllHabitTypes", "Lkotlinx/coroutines/flow/Flow;", "", "getExerciseHistory", "getHabitCompletionRate", "", "days", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHabitLogsByDateRange", "startDate", "Lkotlinx/datetime/LocalDate;", "endDate", "getHabitLogsByType", "getHabitLogsByTypeAndDateRange", "getHabitLogsForDate", "date", "getHabitStatistics", "Lcom/focusflow/data/repository/HabitStatistics;", "getMedicationAdherence", "getMonthlyHabitSummary", "getMoodTrends", "getSleepPatterns", "getTodaysHabits", "", "getWeeklyHabitSummary", "insertHabitLog", "", "logExercise", "exerciseType", "duration", "notes", "(Lkotlinx/datetime/LocalDate;Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logMedication", "taken", "", "time", "(Lkotlinx/datetime/LocalDate;ZLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logMood", "moodValue", "(Lkotlinx/datetime/LocalDate;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logSleep", "hoursSlept", "quality", "(Lkotlinx/datetime/LocalDate;DILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "parseExerciseData", "Lkotlin/Pair;", "value", "parseSleepData", "updateHabitLog", "wasMedicationTaken", "app_debug"})
public final class HabitRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.HabitLogDao habitLogDao = null;
    
    @javax.inject.Inject
    public HabitRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.HabitLogDao habitLogDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<java.lang.String>> getAllHabitTypes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsByType(@org.jetbrains.annotations.NotNull
    java.lang.String habitType) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsForDate(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsByDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsByTypeAndDateRange(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object logMood(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, int moodValue, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object logSleep(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, double hoursSlept, int quality, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object logExercise(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, @org.jetbrains.annotations.NotNull
    java.lang.String exerciseType, int duration, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object logMedication(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, boolean taken, @org.jetbrains.annotations.Nullable
    java.lang.String time, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object calculateCurrentStreak(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object calculateLongestStreak(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getWeeklyHabitSummary(@org.jetbrains.annotations.NotNull
    java.lang.String habitType) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getMonthlyHabitSummary(@org.jetbrains.annotations.NotNull
    java.lang.String habitType) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getHabitCompletionRate(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, int days, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getMoodTrends() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getSleepPatterns() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getExerciseHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getMedicationAdherence() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlin.Pair<java.lang.Double, java.lang.Integer> parseSleepData(@org.jetbrains.annotations.NotNull
    java.lang.String value) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlin.Pair<java.lang.String, java.lang.Integer> parseExerciseData(@org.jetbrains.annotations.NotNull
    java.lang.String value) {
        return null;
    }
    
    public final boolean wasMedicationTaken(@org.jetbrains.annotations.NotNull
    java.lang.String value) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.Map<java.lang.String, com.focusflow.data.model.HabitLog>> getTodaysHabits() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getHabitStatistics(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.repository.HabitStatistics> $completion) {
        return null;
    }
}
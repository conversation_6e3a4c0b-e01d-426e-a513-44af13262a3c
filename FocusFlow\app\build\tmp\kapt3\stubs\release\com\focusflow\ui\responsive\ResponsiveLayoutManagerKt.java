package com.focusflow.ui.responsive;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001aO\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032;\u0010\u0004\u001a7\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0013\u0012\u00110\n\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\fH\u0007\u001a+\u0010\r\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e\u00a2\u0006\u0002\b\fH\u0007\u00a8\u0006\u0010"}, d2 = {"ResponsiveGrid", "", "modifier", "Landroidx/compose/ui/Modifier;", "content", "Lkotlin/Function2;", "", "Lkotlin/ParameterName;", "name", "columns", "Landroidx/compose/ui/unit/Dp;", "spacing", "Landroidx/compose/runtime/Composable;", "ResponsiveLayout", "Lkotlin/Function1;", "Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$LayoutConfig;", "app_release"})
public final class ResponsiveLayoutManagerKt {
    
    /**
     * Composable wrapper for responsive layouts
     */
    @androidx.compose.runtime.Composable
    public static final void ResponsiveLayout(@org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.responsive.ResponsiveLayoutManager.LayoutConfig, kotlin.Unit> content) {
    }
    
    /**
     * Responsive grid layout
     */
    @androidx.compose.runtime.Composable
    public static final void ResponsiveGrid(@org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super androidx.compose.ui.unit.Dp, kotlin.Unit> content) {
    }
}
package com.focusflow.service;

/**
 * AI Service interface for handling AI-powered features
 * This interface can be implemented with different AI providers (Claude, OpenAI, etc.)
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J*\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0005H\u00a6@\u00a2\u0006\u0002\u0010\tJ&\u0010\n\u001a\u00020\u00032\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00052\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00a6@\u00a2\u0006\u0002\u0010\u000fJ&\u0010\u0010\u001a\u00020\u00032\b\u0010\u0011\u001a\u0004\u0018\u00010\u000e2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\f0\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\u001e\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u00a6@\u00a2\u0006\u0002\u0010\u0019J$\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u001c2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001e0\u0005H\u00a6@\u00a2\u0006\u0002\u0010\u001fJ,\u0010 \u001a\u00020\u00032\u0006\u0010!\u001a\u00020\"2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\"2\b\b\u0002\u0010$\u001a\u00020%H\u00a6@\u00a2\u0006\u0002\u0010&J$\u0010\'\u001a\b\u0012\u0004\u0012\u00020\"0\u00052\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020+H\u00a6@\u00a2\u0006\u0002\u0010,\u00a8\u0006-"}, d2 = {"Lcom/focusflow/service/AIService;", "", "analyzeDebtSituation", "Lcom/focusflow/service/AIResponse;", "creditCards", "", "Lcom/focusflow/service/CreditCardData;", "paymentHistory", "Lcom/focusflow/service/PaymentData;", "(Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "analyzeSpending", "expenses", "Lcom/focusflow/service/ExpenseData;", "budget", "Lcom/focusflow/service/BudgetData;", "(Ljava/util/List;Lcom/focusflow/service/BudgetData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateBudgetAdvice", "currentBudget", "spendingHistory", "(Lcom/focusflow/service/BudgetData;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateInsight", "userContext", "Lcom/focusflow/service/UserContext;", "insightType", "Lcom/focusflow/service/InsightType;", "(Lcom/focusflow/service/UserContext;Lcom/focusflow/service/InsightType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateMotivationalMessage", "userProgress", "Lcom/focusflow/service/UserProgress;", "recentAchievements", "Lcom/focusflow/service/Achievement;", "(Lcom/focusflow/service/UserProgress;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateResponse", "prompt", "", "context", "maxTokens", "", "(Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateTaskBreakdown", "task", "Lcom/focusflow/service/TaskData;", "userPreferences", "Lcom/focusflow/service/UserPreferences;", "(Lcom/focusflow/service/TaskData;Lcom/focusflow/service/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface AIService {
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object generateResponse(@org.jetbrains.annotations.NotNull
    java.lang.String prompt, @org.jetbrains.annotations.Nullable
    java.lang.String context, int maxTokens, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object generateInsight(@org.jetbrains.annotations.NotNull
    com.focusflow.service.UserContext userContext, @org.jetbrains.annotations.NotNull
    com.focusflow.service.InsightType insightType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object analyzeSpending(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.ExpenseData> expenses, @org.jetbrains.annotations.Nullable
    com.focusflow.service.BudgetData budget, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object generateBudgetAdvice(@org.jetbrains.annotations.Nullable
    com.focusflow.service.BudgetData currentBudget, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.ExpenseData> spendingHistory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object analyzeDebtSituation(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.CreditCardData> creditCards, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.PaymentData> paymentHistory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object generateTaskBreakdown(@org.jetbrains.annotations.NotNull
    com.focusflow.service.TaskData task, @org.jetbrains.annotations.NotNull
    com.focusflow.service.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
    
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object generateMotivationalMessage(@org.jetbrains.annotations.NotNull
    com.focusflow.service.UserProgress userProgress, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.Achievement> recentAchievements, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion);
    
    /**
     * AI Service interface for handling AI-powered features
     * This interface can be implemented with different AI providers (Claude, OpenAI, etc.)
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}
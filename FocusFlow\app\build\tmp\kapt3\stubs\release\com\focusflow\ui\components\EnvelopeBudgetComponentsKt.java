package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000R\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\u001a*\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\b\u0010\t\u001aD\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001a6\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001aH\u0010\u0016\u001a\u00020\u00012\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\f0\u00182\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u001a2\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u001a2\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001aH\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001d\u001a\u00020\u00032\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u001a2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a6\u0010!\u001a\u00020\u00012\u0006\u0010\"\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\u00032\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u001a2\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001aF\u0010$\u001a\u00020\u00012\u0006\u0010%\u001a\u00020\f2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\f0\u00182\u0018\u0010\u0010\u001a\u0014\u0012\u0004\u0012\u00020(\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\'2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001aB\u0010)\u001a\u00020\u00012\u0006\u0010*\u001a\u00020\u00052\u0006\u0010+\u001a\u00020\u00052\u0006\u0010,\u001a\u00020\u00052\u0006\u0010-\u001a\u00020\u00052\u0006\u0010.\u001a\u00020/2\u0006\u0010\u001d\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00060"}, d2 = {"BudgetAmountColumn", "", "label", "", "amount", "", "color", "Landroidx/compose/ui/graphics/Color;", "BudgetAmountColumn-mxwnekA", "(Ljava/lang/String;DJ)V", "EnhancedBudgetCategoryCard", "category", "Lcom/focusflow/data/model/BudgetCategory;", "onEdit", "Lkotlin/Function0;", "onDelete", "onTransfer", "modifier", "Landroidx/compose/ui/Modifier;", "EnvelopeBudgetCard", "onClick", "onTransferClick", "EnvelopeGrid", "categories", "", "onCategoryClick", "Lkotlin/Function1;", "IncomeInputDialog", "currentAmount", "period", "onAmountChanged", "onConfirm", "onDismiss", "IncomeSetupCard", "currentIncome", "onIncomeSet", "TransferFundsDialog", "fromCategory", "availableCategories", "Lkotlin/Function2;", "", "ZeroBudgetOverviewCard", "totalIncome", "totalAllocated", "totalSpent", "unallocatedAmount", "isBalanced", "", "app_release"})
public final class EnvelopeBudgetComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void EnvelopeGrid(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> categories, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.BudgetCategory, kotlin.Unit> onCategoryClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.BudgetCategory, kotlin.Unit> onTransferClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnvelopeBudgetCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory category, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onTransferClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ZeroBudgetOverviewCard(double totalIncome, double totalAllocated, double totalSpent, double unallocatedAmount, boolean isBalanced, @org.jetbrains.annotations.NotNull
    java.lang.String period, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void IncomeSetupCard(double currentIncome, @org.jetbrains.annotations.NotNull
    java.lang.String period, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Double, kotlin.Unit> onIncomeSet, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void IncomeInputDialog(@org.jetbrains.annotations.NotNull
    java.lang.String currentAmount, @org.jetbrains.annotations.NotNull
    java.lang.String period, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAmountChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TransferFundsDialog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory fromCategory, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> availableCategories, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.Long, ? super java.lang.Double, kotlin.Unit> onTransfer, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedBudgetCategoryCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory category, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onEdit, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onTransfer, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
}
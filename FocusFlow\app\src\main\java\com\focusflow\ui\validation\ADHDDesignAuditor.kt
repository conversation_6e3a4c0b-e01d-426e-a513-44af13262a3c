package com.focusflow.ui.validation

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.focusflow.utils.ADHDDesignValidator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * ADHD Design Auditor
 * Comprehensive validation system for ADHD-friendly design principles
 */
class ADHDDesignAuditor {
    
    private val _auditResults = MutableStateFlow<Map<String, ScreenAuditResult>>(emptyMap())
    val auditResults: StateFlow<Map<String, ScreenAuditResult>> = _auditResults.asStateFlow()
    
    /**
     * Audit result for a specific screen
     */
    data class ScreenAuditResult(
        val screenName: String,
        val overallScore: Int,
        val visualHierarchyScore: Int,
        val cognitiveLoadScore: Int,
        val accessibilityScore: Int,
        val feedbackScore: Int,
        val colorContrastScore: Int,
        val issues: List<ADHDIssue>,
        val recommendations: List<ADHDRecommendation>,
        val strengths: List<String>
    )
    
    /**
     * ADHD-specific design issue
     */
    data class ADHDIssue(
        val severity: IssueSeverity,
        val category: IssueCategory,
        val description: String,
        val impact: String,
        val location: String
    )
    
    /**
     * ADHD-specific design recommendation
     */
    data class ADHDRecommendation(
        val priority: RecommendationPriority,
        val category: IssueCategory,
        val title: String,
        val description: String,
        val implementation: String,
        val expectedImpact: String
    )
    
    enum class IssueSeverity { CRITICAL, HIGH, MEDIUM, LOW }
    enum class RecommendationPriority { URGENT, HIGH, MEDIUM, LOW }
    enum class IssueCategory {
        VISUAL_HIERARCHY,
        COGNITIVE_LOAD,
        ACCESSIBILITY,
        FEEDBACK,
        COLOR_CONTRAST,
        TOUCH_TARGETS,
        INFORMATION_DENSITY,
        MOTIVATION
    }
    
    /**
     * Audit a screen for ADHD-friendly design compliance
     */
    fun auditScreen(
        screenName: String,
        elements: List<UIElement>
    ): ScreenAuditResult {
        val issues = mutableListOf<ADHDIssue>()
        val recommendations = mutableListOf<ADHDRecommendation>()
        val strengths = mutableListOf<String>()
        
        // 1. Visual Hierarchy Audit
        val visualHierarchyScore = auditVisualHierarchy(elements, issues, recommendations, strengths)
        
        // 2. Cognitive Load Audit
        val cognitiveLoadScore = auditCognitiveLoad(elements, issues, recommendations, strengths)
        
        // 3. Accessibility Audit
        val accessibilityScore = auditAccessibility(elements, issues, recommendations, strengths)
        
        // 4. Feedback Mechanisms Audit
        val feedbackScore = auditFeedbackMechanisms(elements, issues, recommendations, strengths)
        
        // 5. Color Contrast Audit
        val colorContrastScore = auditColorContrast(elements, issues, recommendations, strengths)
        
        // Calculate overall score
        val overallScore = listOf(
            visualHierarchyScore,
            cognitiveLoadScore,
            accessibilityScore,
            feedbackScore,
            colorContrastScore
        ).average().toInt()
        
        val result = ScreenAuditResult(
            screenName = screenName,
            overallScore = overallScore,
            visualHierarchyScore = visualHierarchyScore,
            cognitiveLoadScore = cognitiveLoadScore,
            accessibilityScore = accessibilityScore,
            feedbackScore = feedbackScore,
            colorContrastScore = colorContrastScore,
            issues = issues,
            recommendations = recommendations,
            strengths = strengths
        )
        
        // Update audit results
        val currentResults = _auditResults.value.toMutableMap()
        currentResults[screenName] = result
        _auditResults.value = currentResults
        
        return result
    }
    
    private fun auditVisualHierarchy(
        elements: List<UIElement>,
        issues: MutableList<ADHDIssue>,
        recommendations: MutableList<ADHDRecommendation>,
        strengths: MutableList<String>
    ): Int {
        var score = 100
        
        // Check heading hierarchy
        val headings = elements.filter { it.type == ElementType.HEADING }
        if (headings.isEmpty()) {
            issues.add(ADHDIssue(
                severity = IssueSeverity.HIGH,
                category = IssueCategory.VISUAL_HIERARCHY,
                description = "No clear heading hierarchy found",
                impact = "Users with ADHD may struggle to understand content structure",
                location = "Screen layout"
            ))
            score -= 20
        } else {
            strengths.add("Clear heading hierarchy helps ADHD users navigate content")
        }
        
        // Check spacing consistency
        val spacingIssues = elements.count { it.spacing < ADHDDesignValidator.Standards.MIN_ELEMENT_SPACING }
        if (spacingIssues > 0) {
            issues.add(ADHDIssue(
                severity = IssueSeverity.MEDIUM,
                category = IssueCategory.VISUAL_HIERARCHY,
                description = "$spacingIssues elements have insufficient spacing",
                impact = "Cramped layout increases cognitive load for ADHD users",
                location = "Multiple elements"
            ))
            score -= spacingIssues * 5
        }
        
        // Check for visual grouping
        val groupedElements = elements.count { it.hasVisualGrouping }
        val totalElements = elements.size
        if (groupedElements.toFloat() / totalElements < 0.7f) {
            recommendations.add(ADHDRecommendation(
                priority = RecommendationPriority.HIGH,
                category = IssueCategory.VISUAL_HIERARCHY,
                title = "Improve Visual Grouping",
                description = "Group related elements using cards, borders, or background colors",
                implementation = "Use Card composables or background colors to group related content",
                expectedImpact = "Reduces cognitive load and improves content comprehension"
            ))
            score -= 15
        }
        
        return maxOf(0, score)
    }
    
    private fun auditCognitiveLoad(
        elements: List<UIElement>,
        issues: MutableList<ADHDIssue>,
        recommendations: MutableList<ADHDRecommendation>,
        strengths: MutableList<String>
    ): Int {
        var score = 100
        
        // Check item count per screen
        val itemCount = elements.count { it.type == ElementType.INTERACTIVE }
        if (itemCount > ADHDDesignValidator.Standards.MAX_ITEMS_PER_SCREEN) {
            issues.add(ADHDIssue(
                severity = IssueSeverity.HIGH,
                category = IssueCategory.COGNITIVE_LOAD,
                description = "Too many interactive elements: $itemCount > ${ADHDDesignValidator.Standards.MAX_ITEMS_PER_SCREEN}",
                impact = "Overwhelming for ADHD users, may cause decision paralysis",
                location = "Screen layout"
            ))
            score -= 25
        } else {
            strengths.add("Appropriate number of interactive elements reduces cognitive load")
        }
        
        // Check decision points
        val decisionPoints = elements.count { it.type == ElementType.BUTTON || it.type == ElementType.INPUT }
        if (decisionPoints > ADHDDesignValidator.Standards.MAX_DECISION_OPTIONS) {
            recommendations.add(ADHDRecommendation(
                priority = RecommendationPriority.HIGH,
                category = IssueCategory.COGNITIVE_LOAD,
                title = "Reduce Decision Points",
                description = "Too many choices can overwhelm ADHD users",
                implementation = "Use progressive disclosure or group related actions",
                expectedImpact = "Reduces decision fatigue and improves task completion"
            ))
            score -= 20
        }
        
        // Check information density
        val textElements = elements.count { it.type == ElementType.TEXT }
        if (textElements > 10) {
            recommendations.add(ADHDRecommendation(
                priority = RecommendationPriority.MEDIUM,
                category = IssueCategory.INFORMATION_DENSITY,
                title = "Reduce Information Density",
                description = "Break up large blocks of text into digestible chunks",
                implementation = "Use bullet points, shorter paragraphs, or progressive disclosure",
                expectedImpact = "Improves reading comprehension and reduces overwhelm"
            ))
            score -= 10
        }
        
        return maxOf(0, score)
    }
    
    private fun auditAccessibility(
        elements: List<UIElement>,
        issues: MutableList<ADHDIssue>,
        recommendations: MutableList<ADHDRecommendation>,
        strengths: MutableList<String>
    ): Int {
        var score = 100
        
        // Check touch target sizes
        val smallTargets = elements.filter { 
            it.type == ElementType.BUTTON && it.size < ADHDDesignValidator.Standards.MIN_TOUCH_TARGET_SIZE 
        }
        if (smallTargets.isNotEmpty()) {
            issues.add(ADHDIssue(
                severity = IssueSeverity.HIGH,
                category = IssueCategory.TOUCH_TARGETS,
                description = "${smallTargets.size} touch targets are too small",
                impact = "Difficult for users with motor control issues (common in ADHD)",
                location = "Interactive elements"
            ))
            score -= smallTargets.size * 10
        } else {
            strengths.add("All touch targets meet minimum size requirements")
        }
        
        // Check content descriptions
        val missingDescriptions = elements.count { 
            it.type == ElementType.BUTTON && !it.hasContentDescription 
        }
        if (missingDescriptions > 0) {
            issues.add(ADHDIssue(
                severity = IssueSeverity.MEDIUM,
                category = IssueCategory.ACCESSIBILITY,
                description = "$missingDescriptions elements missing content descriptions",
                impact = "Reduces accessibility for screen reader users",
                location = "Interactive elements"
            ))
            score -= missingDescriptions * 5
        }
        
        return maxOf(0, score)
    }
    
    private fun auditFeedbackMechanisms(
        elements: List<UIElement>,
        issues: MutableList<ADHDIssue>,
        recommendations: MutableList<ADHDRecommendation>,
        strengths: MutableList<String>
    ): Int {
        var score = 100
        
        // Check for immediate feedback
        val interactiveElements = elements.filter { it.type == ElementType.BUTTON }
        val feedbackElements = interactiveElements.count { it.hasImmediateFeedback }
        
        if (feedbackElements.toFloat() / interactiveElements.size < 0.8f) {
            recommendations.add(ADHDRecommendation(
                priority = RecommendationPriority.HIGH,
                category = IssueCategory.FEEDBACK,
                title = "Add Immediate Feedback",
                description = "ADHD users benefit from immediate visual/haptic feedback",
                implementation = "Add haptic feedback and visual state changes to buttons",
                expectedImpact = "Improves user confidence and reduces uncertainty"
            ))
            score -= 20
        } else {
            strengths.add("Good immediate feedback mechanisms for user actions")
        }
        
        // Check for progress indicators
        val hasProgressIndicators = elements.any { it.type == ElementType.PROGRESS }
        if (!hasProgressIndicators) {
            recommendations.add(ADHDRecommendation(
                priority = RecommendationPriority.MEDIUM,
                category = IssueCategory.FEEDBACK,
                title = "Add Progress Indicators",
                description = "Show progress for multi-step processes",
                implementation = "Use progress bars or step indicators for complex workflows",
                expectedImpact = "Reduces anxiety and helps users track their progress"
            ))
            score -= 15
        }
        
        return maxOf(0, score)
    }
    
    private fun auditColorContrast(
        elements: List<UIElement>,
        issues: MutableList<ADHDIssue>,
        recommendations: MutableList<ADHDRecommendation>,
        strengths: MutableList<String>
    ): Int {
        var score = 100
        
        // Check color contrast ratios
        val lowContrastElements = elements.count { 
            it.contrastRatio < ADHDDesignValidator.Standards.MIN_CONTRAST_RATIO 
        }
        
        if (lowContrastElements > 0) {
            issues.add(ADHDIssue(
                severity = IssueSeverity.CRITICAL,
                category = IssueCategory.COLOR_CONTRAST,
                description = "$lowContrastElements elements have insufficient contrast",
                impact = "Text may be unreadable, especially for users with visual processing issues",
                location = "Text and background combinations"
            ))
            score -= lowContrastElements * 15
        } else {
            strengths.add("All elements meet minimum contrast requirements")
        }
        
        // Check color usage
        val uniqueColors = elements.map { it.color }.distinct().size
        if (uniqueColors > ADHDDesignValidator.Standards.MAX_COLORS_PER_SCREEN) {
            recommendations.add(ADHDRecommendation(
                priority = RecommendationPriority.MEDIUM,
                category = IssueCategory.COLOR_CONTRAST,
                title = "Simplify Color Palette",
                description = "Too many colors can be distracting for ADHD users",
                implementation = "Limit to 3-5 primary colors per screen",
                expectedImpact = "Reduces visual distraction and improves focus"
            ))
            score -= 10
        }
        
        return maxOf(0, score)
    }
    
    /**
     * Generate comprehensive ADHD design report
     */
    fun generateComprehensiveReport(): ADHDDesignReport {
        val allResults = _auditResults.value.values.toList()
        
        return ADHDDesignReport(
            overallScore = allResults.map { it.overallScore }.average().toInt(),
            screenResults = allResults,
            criticalIssues = allResults.flatMap { it.issues }.filter { it.severity == IssueSeverity.CRITICAL },
            topRecommendations = allResults.flatMap { it.recommendations }
                .filter { it.priority == RecommendationPriority.URGENT || it.priority == RecommendationPriority.HIGH }
                .take(10),
            strengths = allResults.flatMap { it.strengths }.distinct(),
            improvementAreas = identifyImprovementAreas(allResults)
        )
    }
    
    private fun identifyImprovementAreas(results: List<ScreenAuditResult>): List<String> {
        val areas = mutableListOf<String>()
        
        val avgVisualHierarchy = results.map { it.visualHierarchyScore }.average()
        val avgCognitiveLoad = results.map { it.cognitiveLoadScore }.average()
        val avgAccessibility = results.map { it.accessibilityScore }.average()
        val avgFeedback = results.map { it.feedbackScore }.average()
        val avgColorContrast = results.map { it.colorContrastScore }.average()
        
        if (avgVisualHierarchy < 80) areas.add("Visual Hierarchy")
        if (avgCognitiveLoad < 80) areas.add("Cognitive Load Management")
        if (avgAccessibility < 80) areas.add("Accessibility")
        if (avgFeedback < 80) areas.add("Feedback Mechanisms")
        if (avgColorContrast < 80) areas.add("Color Contrast")
        
        return areas
    }
    
    data class ADHDDesignReport(
        val overallScore: Int,
        val screenResults: List<ScreenAuditResult>,
        val criticalIssues: List<ADHDIssue>,
        val topRecommendations: List<ADHDRecommendation>,
        val strengths: List<String>,
        val improvementAreas: List<String>
    )
    
    data class UIElement(
        val type: ElementType,
        val size: Dp = 48.dp,
        val spacing: Dp = 16.dp,
        val color: Color = Color.Black,
        val contrastRatio: Float = 7.0f,
        val hasContentDescription: Boolean = true,
        val hasImmediateFeedback: Boolean = false,
        val hasVisualGrouping: Boolean = false
    )
    
    enum class ElementType {
        HEADING, TEXT, BUTTON, INPUT, INTERACTIVE, PROGRESS, CARD, ICON
    }
}

package com.focusflow.service;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.CreditCardRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SampleDataService_Factory implements Factory<SampleDataService> {
  private final Provider<CreditCardRepository> creditCardRepositoryProvider;

  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public SampleDataService_Factory(Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.creditCardRepositoryProvider = creditCardRepositoryProvider;
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public SampleDataService get() {
    return newInstance(creditCardRepositoryProvider.get(), budgetCategoryRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static SampleDataService_Factory create(
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new SampleDataService_Factory(creditCardRepositoryProvider, budgetCategoryRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static SampleDataService newInstance(CreditCardRepository creditCardRepository,
      BudgetCategoryRepository budgetCategoryRepository,
      UserPreferencesRepository userPreferencesRepository) {
    return new SampleDataService(creditCardRepository, budgetCategoryRepository, userPreferencesRepository);
  }
}

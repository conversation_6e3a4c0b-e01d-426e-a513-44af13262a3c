package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a\u001e\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a@\u0010\u0007\u001a\u00020\u00012\b\u0010\b\u001a\u0004\u0018\u00010\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a\u0010\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000fH\u0007\u001a\b\u0010\u0010\u001a\u00020\u0001H\u0007\u001a@\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0007\u001aL\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u001b\u001a\u00020\u00042\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001a\b\u0010\u001d\u001a\u00020\u0001H\u0007\u00a8\u0006\u001e"}, d2 = {"ListeningIndicator", "", "RecognizedTextDisplay", "text", "", "onClear", "Lkotlin/Function0;", "VoiceCommandResultDialog", "result", "Lcom/focusflow/service/VoiceInputService$VoiceCommandResult;", "onDismiss", "onConfirm", "onRetry", "VoiceCommandSuggestionItem", "suggestion", "Lcom/focusflow/ui/components/VoiceCommandSuggestion;", "VoiceCommandSuggestions", "VoiceInputButton", "isListening", "", "onStartListening", "onStopListening", "modifier", "Landroidx/compose/ui/Modifier;", "size", "Lcom/focusflow/ui/components/VoiceButtonSize;", "VoiceInputCard", "recognizedText", "onClearText", "VoiceWaveAnimation", "app_release"})
public final class VoiceInputComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void VoiceInputButton(boolean isListening, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartListening, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStopListening, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.VoiceButtonSize size) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void VoiceInputCard(boolean isListening, @org.jetbrains.annotations.NotNull
    java.lang.String recognizedText, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartListening, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStopListening, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearText, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ListeningIndicator() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void VoiceWaveAnimation() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void RecognizedTextDisplay(@org.jetbrains.annotations.NotNull
    java.lang.String text, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClear) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void VoiceCommandSuggestions() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void VoiceCommandSuggestionItem(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.VoiceCommandSuggestion suggestion) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void VoiceCommandResultDialog(@org.jetbrains.annotations.Nullable
    com.focusflow.service.VoiceInputService.VoiceCommandResult result, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry) {
    }
}
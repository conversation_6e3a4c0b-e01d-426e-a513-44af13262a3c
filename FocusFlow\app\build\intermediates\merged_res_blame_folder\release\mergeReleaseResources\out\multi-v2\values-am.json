{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-66:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fc6d0d970e3c168245311f4f8e60786\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2723,2816,2916,3013,3112,3208,3310,5935", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "2811,2911,3008,3107,3203,3305,3405,6031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c220a8d904d175691eb4177fafcca3d5\\transformed\\jetified-ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "36,37,39,41,42,53,54,55,56,57,58,59,60,62,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3410,3493,3676,3855,3951,5173,5251,5334,5416,5494,5560,5626,5704,5865,6036,6116,6181", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "3488,3565,3763,3946,4028,5246,5329,5411,5489,5555,5621,5699,5780,5930,6111,6176,6292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e741e5374256fd7ad708d21850e91de0\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,5785", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,5860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd99fe8d474b529eb55ef83da4ba319\\transformed\\biometric-1.1.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,248,355,467,583,703,819,934,1026,1158,1283", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "156,243,350,462,578,698,814,929,1021,1153,1278,1383"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3570,3768,4033,4140,4252,4368,4488,4604,4719,4811,4943,5068", "endColumns": "105,86,106,111,115,119,115,114,91,131,124,104", "endOffsets": "3671,3850,4135,4247,4363,4483,4599,4714,4806,4938,5063,5168"}}]}]}
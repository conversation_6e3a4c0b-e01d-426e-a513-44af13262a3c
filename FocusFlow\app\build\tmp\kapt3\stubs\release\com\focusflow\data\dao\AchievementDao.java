package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u001c\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\'J\u0014\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H\'J\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H\'J\u0016\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\f\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001e\u0010\u0015\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u0016\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u0018\u00a8\u0006\u0019"}, d2 = {"Lcom/focusflow/data/dao/AchievementDao;", "", "getAchievementsByType", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/focusflow/data/model/Achievement;", "type", "", "getAllAchievements", "getUnlockedAchievements", "insertAchievement", "", "achievement", "(Lcom/focusflow/data/model/Achievement;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "unlockAchievement", "", "achievementId", "unlockedAt", "Lkotlinx/datetime/LocalDateTime;", "(JLkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAchievement", "updateAchievementProgress", "progress", "", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
@androidx.room.Dao
public abstract interface AchievementDao {
    
    @androidx.room.Query(value = "SELECT * FROM achievements ORDER BY isUnlocked DESC, pointsAwarded DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Achievement>> getAllAchievements();
    
    @androidx.room.Query(value = "SELECT * FROM achievements WHERE isUnlocked = 1 ORDER BY unlockedAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Achievement>> getUnlockedAchievements();
    
    @androidx.room.Query(value = "SELECT * FROM achievements WHERE type = :type")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Achievement>> getAchievementsByType(@org.jetbrains.annotations.NotNull
    java.lang.String type);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertAchievement(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Achievement achievement, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateAchievement(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Achievement achievement, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE achievements SET isUnlocked = 1, unlockedAt = :unlockedAt WHERE id = :achievementId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object unlockAchievement(long achievementId, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime unlockedAt, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE achievements SET currentProgress = :progress WHERE id = :achievementId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateAchievementProgress(long achievementId, int progress, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
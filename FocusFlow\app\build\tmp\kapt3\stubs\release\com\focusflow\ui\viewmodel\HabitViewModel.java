package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u001c\u001a\u00020\u001dJ\u0006\u0010\u001e\u001a\u00020\u001dJ\u000e\u0010\u001f\u001a\u00020\u001d2\u0006\u0010 \u001a\u00020\rJ\u000e\u0010!\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020\u0018J\u000e\u0010#\u001a\u00020\u00182\u0006\u0010$\u001a\u00020%J\u000e\u0010&\u001a\u00020\u00182\u0006\u0010\'\u001a\u00020%J\u000e\u0010(\u001a\u00020\u00182\u0006\u0010)\u001a\u00020%J\b\u0010*\u001a\u00020\u001dH\u0002J \u0010+\u001a\u00020\u001d2\u0006\u0010\"\u001a\u00020\u00182\u0006\u0010,\u001a\u00020%2\b\b\u0002\u0010-\u001a\u00020\u0018J\"\u0010.\u001a\u00020\u001d2\u0006\u0010/\u001a\u0002002\b\b\u0002\u00101\u001a\u00020\u00182\b\b\u0002\u0010-\u001a\u00020\u0018J\u0018\u00102\u001a\u00020\u001d2\u0006\u0010$\u001a\u00020%2\b\b\u0002\u0010-\u001a\u00020\u0018J \u00103\u001a\u00020\u001d2\u0006\u00104\u001a\u0002052\u0006\u0010\'\u001a\u00020%2\b\b\u0002\u0010-\u001a\u00020\u0018R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u001d\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR%\u0010\u0016\u001a\u0016\u0012\u0012\u0012\u0010\u0012\u0004\u0012\u00020\u0018\u0012\u0006\u0012\u0004\u0018\u00010\r0\u00170\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u000fR\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u000f\u00a8\u00066"}, d2 = {"Lcom/focusflow/ui/viewmodel/HabitViewModel;", "Landroidx/lifecycle/ViewModel;", "habitRepository", "Lcom/focusflow/data/repository/HabitRepository;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "(Lcom/focusflow/data/repository/HabitRepository;Lcom/focusflow/service/GamificationService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/HabitUiState;", "exerciseLogs", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/focusflow/data/model/HabitLog;", "getExerciseLogs", "()Lkotlinx/coroutines/flow/StateFlow;", "medicationLogs", "getMedicationLogs", "moodLogs", "getMoodLogs", "sleepLogs", "getSleepLogs", "todaysHabits", "", "", "getTodaysHabits", "uiState", "getUiState", "clearError", "", "clearLastAction", "deleteHabitLog", "habitLog", "getExerciseTypeEmoji", "exerciseType", "getMoodEmoji", "moodValue", "", "getSleepQualityText", "quality", "getStreakMessage", "streak", "loadHabitStatistics", "logExercise", "duration", "notes", "logMedication", "taken", "", "time", "logMood", "logSleep", "hoursSlept", "", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class HabitViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.HabitRepository habitRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.HabitUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.HabitUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.focusflow.data.model.HabitLog>> todaysHabits = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> moodLogs = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> sleepLogs = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> exerciseLogs = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> medicationLogs = null;
    
    @javax.inject.Inject
    public HabitViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.HabitRepository habitRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.HabitUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.focusflow.data.model.HabitLog>> getTodaysHabits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> getMoodLogs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> getSleepLogs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> getExerciseLogs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.HabitLog>> getMedicationLogs() {
        return null;
    }
    
    private final void loadHabitStatistics() {
    }
    
    public final void logMood(int moodValue, @org.jetbrains.annotations.NotNull
    java.lang.String notes) {
    }
    
    public final void logSleep(double hoursSlept, int quality, @org.jetbrains.annotations.NotNull
    java.lang.String notes) {
    }
    
    public final void logExercise(@org.jetbrains.annotations.NotNull
    java.lang.String exerciseType, int duration, @org.jetbrains.annotations.NotNull
    java.lang.String notes) {
    }
    
    public final void logMedication(boolean taken, @org.jetbrains.annotations.NotNull
    java.lang.String time, @org.jetbrains.annotations.NotNull
    java.lang.String notes) {
    }
    
    public final void deleteHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog) {
    }
    
    public final void clearError() {
    }
    
    public final void clearLastAction() {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getMoodEmoji(int moodValue) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSleepQualityText(int quality) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getExerciseTypeEmoji(@org.jetbrains.annotations.NotNull
    java.lang.String exerciseType) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getStreakMessage(int streak) {
        return null;
    }
}
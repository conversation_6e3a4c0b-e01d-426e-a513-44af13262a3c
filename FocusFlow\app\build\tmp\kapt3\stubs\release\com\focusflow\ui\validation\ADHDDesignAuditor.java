package com.focusflow.ui.validation;

/**
 * ADHD Design Auditor
 * Comprehensive validation system for ADHD-friendly design principles
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u00002\u00020\u0001:\t!\"#$%&\'()B\u0005\u00a2\u0006\u0002\u0010\u0002J@\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u0012H\u0002J@\u0010\u0017\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u0012H\u0002J@\u0010\u0018\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u0012H\u0002J@\u0010\u0019\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u0012H\u0002J\u001c\u0010\u001a\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u00062\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fJ@\u0010\u001c\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u00122\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00060\u0012H\u0002J\u0006\u0010\u001d\u001a\u00020\u001eJ\u001c\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00060\u000f2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00070\u000fH\u0002R \u0010\u0003\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\b\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006*"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor;", "", "()V", "_auditResults", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ScreenAuditResult;", "auditResults", "Lkotlinx/coroutines/flow/StateFlow;", "getAuditResults", "()Lkotlinx/coroutines/flow/StateFlow;", "auditAccessibility", "", "elements", "", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$UIElement;", "issues", "", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDIssue;", "recommendations", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDRecommendation;", "strengths", "auditCognitiveLoad", "auditColorContrast", "auditFeedbackMechanisms", "auditScreen", "screenName", "auditVisualHierarchy", "generateComprehensiveReport", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDDesignReport;", "identifyImprovementAreas", "results", "ADHDDesignReport", "ADHDIssue", "ADHDRecommendation", "ElementType", "IssueCategory", "IssueSeverity", "RecommendationPriority", "ScreenAuditResult", "UIElement", "app_release"})
public final class ADHDDesignAuditor {
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Map<java.lang.String, com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult>> _auditResults = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult>> auditResults = null;
    
    public ADHDDesignAuditor() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.Map<java.lang.String, com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult>> getAuditResults() {
        return null;
    }
    
    /**
     * Audit a screen for ADHD-friendly design compliance
     */
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult auditScreen(@org.jetbrains.annotations.NotNull
    java.lang.String screenName, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.UIElement> elements) {
        return null;
    }
    
    private final int auditVisualHierarchy(java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.UIElement> elements, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, java.util.List<java.lang.String> strengths) {
        return 0;
    }
    
    private final int auditCognitiveLoad(java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.UIElement> elements, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, java.util.List<java.lang.String> strengths) {
        return 0;
    }
    
    private final int auditAccessibility(java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.UIElement> elements, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, java.util.List<java.lang.String> strengths) {
        return 0;
    }
    
    private final int auditFeedbackMechanisms(java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.UIElement> elements, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, java.util.List<java.lang.String> strengths) {
        return 0;
    }
    
    private final int auditColorContrast(java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.UIElement> elements, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, java.util.List<java.lang.String> strengths) {
        return 0;
    }
    
    /**
     * Generate comprehensive ADHD design report
     */
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.validation.ADHDDesignAuditor.ADHDDesignReport generateComprehensiveReport() {
        return null;
    }
    
    private final java.util.List<java.lang.String> identifyImprovementAreas(java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult> results) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0005\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0005\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0005\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0005\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\b0\u0005H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\n0\u0005H\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\f0\u0005H\u00c6\u0003J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\f0\u0005H\u00c6\u0003Jc\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00052\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00052\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0005H\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\"\u001a\u00020\fH\u00d6\u0001R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0010R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0010\u00a8\u0006#"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDDesignReport;", "", "overallScore", "", "screenResults", "", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ScreenAuditResult;", "criticalIssues", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDIssue;", "topRecommendations", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDRecommendation;", "strengths", "", "improvementAreas", "(ILjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getCriticalIssues", "()Ljava/util/List;", "getImprovementAreas", "getOverallScore", "()I", "getScreenResults", "getStrengths", "getTopRecommendations", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "app_release"})
    public static final class ADHDDesignReport {
        private final int overallScore = 0;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult> screenResults = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> criticalIssues = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> topRecommendations = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> strengths = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> improvementAreas = null;
        
        public ADHDDesignReport(int overallScore, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult> screenResults, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> criticalIssues, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> topRecommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> improvementAreas) {
            super();
        }
        
        public final int getOverallScore() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult> getScreenResults() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> getCriticalIssues() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> getTopRecommendations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getStrengths() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getImprovementAreas() {
            return null;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult> component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.ADHDDesignReport copy(int overallScore, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult> screenResults, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> criticalIssues, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> topRecommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> improvementAreas) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * ADHD-specific design issue
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0007H\u00c6\u0003J;\u0010\u0018\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001f"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDIssue;", "", "severity", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueSeverity;", "category", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;", "description", "", "impact", "location", "(Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueSeverity;Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getCategory", "()Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;", "getDescription", "()Ljava/lang/String;", "getImpact", "getLocation", "getSeverity", "()Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueSeverity;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
    public static final class ADHDIssue {
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverity severity = null;
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory category = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String description = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String impact = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String location = null;
        
        public ADHDIssue(@org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverity severity, @org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory category, @org.jetbrains.annotations.NotNull
        java.lang.String description, @org.jetbrains.annotations.NotNull
        java.lang.String impact, @org.jetbrains.annotations.NotNull
        java.lang.String location) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverity getSeverity() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory getCategory() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getImpact() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getLocation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverity component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue copy(@org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverity severity, @org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory category, @org.jetbrains.annotations.NotNull
        java.lang.String description, @org.jetbrains.annotations.NotNull
        java.lang.String impact, @org.jetbrains.annotations.NotNull
        java.lang.String location) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * ADHD-specific design recommendation
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B5\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u000bJ\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003JE\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\n\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000f\u00a8\u0006\""}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDRecommendation;", "", "priority", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$RecommendationPriority;", "category", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;", "title", "", "description", "implementation", "expectedImpact", "(Lcom/focusflow/ui/validation/ADHDDesignAuditor$RecommendationPriority;Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getCategory", "()Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;", "getDescription", "()Ljava/lang/String;", "getExpectedImpact", "getImplementation", "getPriority", "()Lcom/focusflow/ui/validation/ADHDDesignAuditor$RecommendationPriority;", "getTitle", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
    public static final class ADHDRecommendation {
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority priority = null;
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory category = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String title = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String description = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String implementation = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String expectedImpact = null;
        
        public ADHDRecommendation(@org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority priority, @org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory category, @org.jetbrains.annotations.NotNull
        java.lang.String title, @org.jetbrains.annotations.NotNull
        java.lang.String description, @org.jetbrains.annotations.NotNull
        java.lang.String implementation, @org.jetbrains.annotations.NotNull
        java.lang.String expectedImpact) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority getPriority() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory getCategory() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getImplementation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getExpectedImpact() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation copy(@org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority priority, @org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory category, @org.jetbrains.annotations.NotNull
        java.lang.String title, @org.jetbrains.annotations.NotNull
        java.lang.String description, @org.jetbrains.annotations.NotNull
        java.lang.String implementation, @org.jetbrains.annotations.NotNull
        java.lang.String expectedImpact) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$ElementType;", "", "(Ljava/lang/String;I)V", "HEADING", "TEXT", "BUTTON", "INPUT", "INTERACTIVE", "PROGRESS", "CARD", "ICON", "app_release"})
    public static enum ElementType {
        /*public static final*/ HEADING /* = new HEADING() */,
        /*public static final*/ TEXT /* = new TEXT() */,
        /*public static final*/ BUTTON /* = new BUTTON() */,
        /*public static final*/ INPUT /* = new INPUT() */,
        /*public static final*/ INTERACTIVE /* = new INTERACTIVE() */,
        /*public static final*/ PROGRESS /* = new PROGRESS() */,
        /*public static final*/ CARD /* = new CARD() */,
        /*public static final*/ ICON /* = new ICON() */;
        
        ElementType() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.validation.ADHDDesignAuditor.ElementType> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueCategory;", "", "(Ljava/lang/String;I)V", "VISUAL_HIERARCHY", "COGNITIVE_LOAD", "ACCESSIBILITY", "FEEDBACK", "COLOR_CONTRAST", "TOUCH_TARGETS", "INFORMATION_DENSITY", "MOTIVATION", "app_release"})
    public static enum IssueCategory {
        /*public static final*/ VISUAL_HIERARCHY /* = new VISUAL_HIERARCHY() */,
        /*public static final*/ COGNITIVE_LOAD /* = new COGNITIVE_LOAD() */,
        /*public static final*/ ACCESSIBILITY /* = new ACCESSIBILITY() */,
        /*public static final*/ FEEDBACK /* = new FEEDBACK() */,
        /*public static final*/ COLOR_CONTRAST /* = new COLOR_CONTRAST() */,
        /*public static final*/ TOUCH_TARGETS /* = new TOUCH_TARGETS() */,
        /*public static final*/ INFORMATION_DENSITY /* = new INFORMATION_DENSITY() */,
        /*public static final*/ MOTIVATION /* = new MOTIVATION() */;
        
        IssueCategory() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$IssueSeverity;", "", "(Ljava/lang/String;I)V", "CRITICAL", "HIGH", "MEDIUM", "LOW", "app_release"})
    public static enum IssueSeverity {
        /*public static final*/ CRITICAL /* = new CRITICAL() */,
        /*public static final*/ HIGH /* = new HIGH() */,
        /*public static final*/ MEDIUM /* = new MEDIUM() */,
        /*public static final*/ LOW /* = new LOW() */;
        
        IssueSeverity() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverity> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$RecommendationPriority;", "", "(Ljava/lang/String;I)V", "URGENT", "HIGH", "MEDIUM", "LOW", "app_release"})
    public static enum RecommendationPriority {
        /*public static final*/ URGENT /* = new URGENT() */,
        /*public static final*/ HIGH /* = new HIGH() */,
        /*public static final*/ MEDIUM /* = new MEDIUM() */,
        /*public static final*/ LOW /* = new LOW() */;
        
        RecommendationPriority() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority> getEntries() {
            return null;
        }
    }
    
    /**
     * Audit result for a specific screen
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001b\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001Bg\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u0005\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u0012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\f\u0012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\u0002\u0010\u0011J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0005H\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\u000f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u000f0\fH\u00c6\u0003J\u007f\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0001J\u0013\u0010*\u001a\u00020+2\b\u0010,\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010-\u001a\u00020\u0005H\u00d6\u0001J\t\u0010.\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0018R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0013\u00a8\u0006/"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$ScreenAuditResult;", "", "screenName", "", "overallScore", "", "visualHierarchyScore", "cognitiveLoadScore", "accessibilityScore", "feedbackScore", "colorContrastScore", "issues", "", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDIssue;", "recommendations", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ADHDRecommendation;", "strengths", "(Ljava/lang/String;IIIIIILjava/util/List;Ljava/util/List;Ljava/util/List;)V", "getAccessibilityScore", "()I", "getCognitiveLoadScore", "getColorContrastScore", "getFeedbackScore", "getIssues", "()Ljava/util/List;", "getOverallScore", "getRecommendations", "getScreenName", "()Ljava/lang/String;", "getStrengths", "getVisualHierarchyScore", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "app_release"})
    public static final class ScreenAuditResult {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String screenName = null;
        private final int overallScore = 0;
        private final int visualHierarchyScore = 0;
        private final int cognitiveLoadScore = 0;
        private final int accessibilityScore = 0;
        private final int feedbackScore = 0;
        private final int colorContrastScore = 0;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> strengths = null;
        
        public ScreenAuditResult(@org.jetbrains.annotations.NotNull
        java.lang.String screenName, int overallScore, int visualHierarchyScore, int cognitiveLoadScore, int accessibilityScore, int feedbackScore, int colorContrastScore, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getScreenName() {
            return null;
        }
        
        public final int getOverallScore() {
            return 0;
        }
        
        public final int getVisualHierarchyScore() {
            return 0;
        }
        
        public final int getCognitiveLoadScore() {
            return 0;
        }
        
        public final int getAccessibilityScore() {
            return 0;
        }
        
        public final int getFeedbackScore() {
            return 0;
        }
        
        public final int getColorContrastScore() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> getIssues() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> getRecommendations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getStrengths() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component10() {
            return null;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int component6() {
            return 0;
        }
        
        public final int component7() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.ScreenAuditResult copy(@org.jetbrains.annotations.NotNull
        java.lang.String screenName, int overallScore, int visualHierarchyScore, int cognitiveLoadScore, int accessibilityScore, int feedbackScore, int colorContrastScore, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDIssue> issues, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignAuditor.ADHDRecommendation> recommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000b\n\u0002\b\"\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\f\u0012\b\b\u0002\u0010\u000e\u001a\u00020\f\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\u0016\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b \u0010\u0014J\u0016\u0010!\u001a\u00020\u0005H\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b\"\u0010\u0014J\u0016\u0010#\u001a\u00020\bH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b$\u0010\u0011J\t\u0010%\u001a\u00020\nH\u00c6\u0003J\t\u0010&\u001a\u00020\fH\u00c6\u0003J\t\u0010\'\u001a\u00020\fH\u00c6\u0003J\t\u0010(\u001a\u00020\fH\u00c6\u0003Jc\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\fH\u00c6\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b*\u0010+J\u0013\u0010,\u001a\u00020\f2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\t\u00100\u001a\u000201H\u00d6\u0001R\u0019\u0010\u0007\u001a\u00020\b\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0012\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u0019\u0010\u0004\u001a\u00020\u0005\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0019\u0010\u0014R\u0019\u0010\u0006\u001a\u00020\u0005\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u001b\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001d\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u00062"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignAuditor$UIElement;", "", "type", "Lcom/focusflow/ui/validation/ADHDDesignAuditor$ElementType;", "size", "Landroidx/compose/ui/unit/Dp;", "spacing", "color", "Landroidx/compose/ui/graphics/Color;", "contrastRatio", "", "hasContentDescription", "", "hasImmediateFeedback", "hasVisualGrouping", "(Lcom/focusflow/ui/validation/ADHDDesignAuditor$ElementType;FFJFZZZLkotlin/jvm/internal/DefaultConstructorMarker;)V", "getColor-0d7_KjU", "()J", "J", "getContrastRatio", "()F", "getHasContentDescription", "()Z", "getHasImmediateFeedback", "getHasVisualGrouping", "getSize-D9Ej5fM", "F", "getSpacing-D9Ej5fM", "getType", "()Lcom/focusflow/ui/validation/ADHDDesignAuditor$ElementType;", "component1", "component2", "component2-D9Ej5fM", "component3", "component3-D9Ej5fM", "component4", "component4-0d7_KjU", "component5", "component6", "component7", "component8", "copy", "copy-10LGxhE", "(Lcom/focusflow/ui/validation/ADHDDesignAuditor$ElementType;FFJFZZZ)Lcom/focusflow/ui/validation/ADHDDesignAuditor$UIElement;", "equals", "other", "hashCode", "", "toString", "", "app_release"})
    public static final class UIElement {
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.validation.ADHDDesignAuditor.ElementType type = null;
        private final float size = 0.0F;
        private final float spacing = 0.0F;
        private final long color = 0L;
        private final float contrastRatio = 0.0F;
        private final boolean hasContentDescription = false;
        private final boolean hasImmediateFeedback = false;
        private final boolean hasVisualGrouping = false;
        
        private UIElement(com.focusflow.ui.validation.ADHDDesignAuditor.ElementType type, float size, float spacing, long color, float contrastRatio, boolean hasContentDescription, boolean hasImmediateFeedback, boolean hasVisualGrouping) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.ElementType getType() {
            return null;
        }
        
        public final float getContrastRatio() {
            return 0.0F;
        }
        
        public final boolean getHasContentDescription() {
            return false;
        }
        
        public final boolean getHasImmediateFeedback() {
            return false;
        }
        
        public final boolean getHasVisualGrouping() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignAuditor.ElementType component1() {
            return null;
        }
        
        public final float component5() {
            return 0.0F;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final boolean component7() {
            return false;
        }
        
        public final boolean component8() {
            return false;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}
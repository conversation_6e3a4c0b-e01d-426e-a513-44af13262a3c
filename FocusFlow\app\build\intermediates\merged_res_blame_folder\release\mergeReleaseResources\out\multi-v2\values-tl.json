{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-66:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fc6d0d970e3c168245311f4f8e60786\\transformed\\core-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2942,3044,3145,3242,3349,3457,6431", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "2937,3039,3140,3237,3344,3452,3574,6527"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd99fe8d474b529eb55ef83da4ba319\\transformed\\biometric-1.1.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,265,387,540,683,835,962,1100,1200,1337,1489", "endColumns": "113,95,121,152,142,151,126,137,99,136,151,125", "endOffsets": "164,260,382,535,678,830,957,1095,1195,1332,1484,1610"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3764,3975,4263,4385,4538,4681,4833,4960,5098,5198,5335,5487", "endColumns": "113,95,121,152,142,151,126,137,99,136,151,125", "endOffsets": "3873,4066,4380,4533,4676,4828,4955,5093,5193,5330,5482,5608"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e741e5374256fd7ad708d21850e91de0\\transformed\\appcompat-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,6275", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,6355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c220a8d904d175691eb4177fafcca3d5\\transformed\\jetified-ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,999,1068,1155,1241,1312,1390,1456", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,994,1063,1150,1236,1307,1385,1451,1578"}, "to": {"startLines": "36,37,39,41,42,53,54,55,56,57,58,59,60,62,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3579,3678,3878,4071,4173,5613,5695,5787,5879,5963,6033,6102,6189,6360,6532,6610,6676", "endColumns": "98,85,96,101,89,81,91,91,83,69,68,86,85,70,77,65,126", "endOffsets": "3673,3759,3970,4168,4258,5690,5782,5874,5958,6028,6097,6184,6270,6426,6605,6671,6798"}}]}]}
package com.focusflow.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.focusflow.utils.ADHDDesignValidator

/**
 * ADHD-Friendly UI Components
 * Implements evidence-based design principles for ADHD users
 */

/**
 * ADHD-Friendly Button with enhanced feedback and accessibility
 */
@Composable
fun ADHDFriendlyButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null,
    buttonType: ADHDButtonType = ADHDButtonType.PRIMARY,
    showFeedback: Boolean = true
) {
    val haptic = LocalHapticFeedback.current
    var isPressed by remember { mutableStateOf(false) }
    
    val buttonColors = when (buttonType) {
        ADHDButtonType.PRIMARY -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.FOCUS_GREEN,
            contentColor = Color.White
        )
        ADHDButtonType.SECONDARY -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.CALM_BLUE,
            contentColor = Color.White
        )
        ADHDButtonType.WARNING -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.WARNING_AMBER,
            contentColor = Color.Black
        )
        ADHDButtonType.DANGER -> ButtonDefaults.buttonColors(
            backgroundColor = ADHDDesignValidator.Colors.ERROR_RED,
            contentColor = Color.White
        )
    }
    
    Button(
        onClick = {
            if (showFeedback) {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
            }
            onClick()
        },
        modifier = modifier
            .heightIn(min = ADHDDesignValidator.Standards.RECOMMENDED_TOUCH_TARGET_SIZE)
            .semantics {
                contentDescription = "$text button"
            },
        enabled = enabled,
        colors = buttonColors,
        shape = RoundedCornerShape(12.dp),
        elevation = ButtonDefaults.elevation(
            defaultElevation = 4.dp,
            pressedElevation = 8.dp
        )
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            icon?.let { iconVector ->
                Icon(
                    imageVector = iconVector,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(
                text = text,
                fontSize = ADHDDesignValidator.Standards.RECOMMENDED_BODY_TEXT_SIZE,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

enum class ADHDButtonType {
    PRIMARY, SECONDARY, WARNING, DANGER
}

/**
 * ADHD-Friendly Card with optimal spacing and visual hierarchy
 */
@Composable
fun ADHDFriendlyCard(
    title: String,
    modifier: Modifier = Modifier,
    subtitle: String? = null,
    icon: ImageVector? = null,
    backgroundColor: Color = ADHDDesignValidator.Colors.CARD_BACKGROUND,
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    val haptic = LocalHapticFeedback.current
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .then(
                if (onClick != null) {
                    Modifier.clickable {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onClick()
                    }
                } else Modifier
            ),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = backgroundColor
    ) {
        Column(
            modifier = Modifier.padding(ADHDDesignValidator.Standards.CARD_PADDING)
        ) {
            // Header with title and optional icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                icon?.let { iconVector ->
                    Icon(
                        imageVector = iconVector,
                        contentDescription = null,
                        tint = ADHDDesignValidator.Colors.FOCUS_GREEN,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                }
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold,
                        color = ADHDDesignValidator.Colors.PRIMARY_TEXT
                    )
                    
                    subtitle?.let { subtitleText ->
                        Text(
                            text = subtitleText,
                            style = MaterialTheme.typography.body2,
                            color = ADHDDesignValidator.Colors.SECONDARY_TEXT
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(ADHDDesignValidator.Standards.RECOMMENDED_ELEMENT_SPACING))
            
            content()
        }
    }
}

/**
 * ADHD-Friendly Progress Indicator with motivational messaging
 */
@Composable
fun ADHDProgressIndicator(
    progress: Float,
    label: String,
    modifier: Modifier = Modifier,
    showPercentage: Boolean = true,
    motivationalMessage: String? = null,
    color: Color = ADHDDesignValidator.Colors.SUCCESS_GREEN
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = ADHDDesignValidator.Standards.ANIMATION_DURATION_MS.toInt(),
            easing = EaseOutCubic
        )
    )
    
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.body1,
                fontWeight = FontWeight.Medium,
                color = ADHDDesignValidator.Colors.PRIMARY_TEXT
            )
            
            if (showPercentage) {
                Text(
                    text = "${(progress * 100).toInt()}%",
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Bold,
                    color = color
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LinearProgressIndicator(
            progress = animatedProgress,
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp)),
            color = color,
            backgroundColor = color.copy(alpha = 0.2f)
        )
        
        motivationalMessage?.let { message ->
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.caption,
                color = ADHDDesignValidator.Colors.FOCUS_GREEN,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * ADHD-Friendly Status Indicator with clear visual cues
 */
@Composable
fun ADHDStatusIndicator(
    status: ADHDStatus,
    label: String,
    modifier: Modifier = Modifier,
    showIcon: Boolean = true
) {
    val (color, icon, description) = when (status) {
        ADHDStatus.SUCCESS -> Triple(
            ADHDDesignValidator.Colors.SUCCESS_GREEN,
            Icons.Default.CheckCircle,
            "Success"
        )
        ADHDStatus.WARNING -> Triple(
            ADHDDesignValidator.Colors.WARNING_AMBER,
            Icons.Default.Warning,
            "Warning"
        )
        ADHDStatus.ERROR -> Triple(
            ADHDDesignValidator.Colors.ERROR_RED,
            Icons.Default.Error,
            "Error"
        )
        ADHDStatus.INFO -> Triple(
            ADHDDesignValidator.Colors.CALM_BLUE,
            Icons.Default.Info,
            "Information"
        )
    }
    
    Row(
        modifier = modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .border(
                width = 1.dp,
                color = color.copy(alpha = 0.3f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (showIcon) {
            Icon(
                imageVector = icon,
                contentDescription = description,
                tint = color,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
        }
        
        Text(
            text = label,
            style = MaterialTheme.typography.body2,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}



/**
 * ADHD-Friendly Input Field with enhanced feedback
 */
@Composable
fun ADHDFriendlyTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    placeholder: String? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    supportingText: String? = null,
    leadingIcon: ImageVector? = null
) {
    Column(modifier = modifier) {
        OutlinedTextField(
            value = value,
            onValueChange = onValueChange,
            label = { Text(label) },
            placeholder = placeholder?.let { { Text(it) } },
            leadingIcon = leadingIcon?.let { icon ->
                {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = if (isError) ADHDDesignValidator.Colors.ERROR_RED 
                               else ADHDDesignValidator.Colors.FOCUS_GREEN
                    )
                }
            },
            isError = isError,
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = ADHDDesignValidator.Standards.RECOMMENDED_TOUCH_TARGET_SIZE),
            shape = RoundedCornerShape(12.dp),
            colors = TextFieldDefaults.outlinedTextFieldColors(
                focusedBorderColor = ADHDDesignValidator.Colors.FOCUS_GREEN,
                unfocusedBorderColor = ADHDDesignValidator.Colors.SECONDARY_TEXT.copy(alpha = 0.5f),
                errorBorderColor = ADHDDesignValidator.Colors.ERROR_RED
            )
        )
        
        // Supporting text or error message
        if (isError && errorMessage != null) {
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = errorMessage,
                style = MaterialTheme.typography.caption,
                color = ADHDDesignValidator.Colors.ERROR_RED
            )
        } else if (supportingText != null) {
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = supportingText,
                style = MaterialTheme.typography.caption,
                color = ADHDDesignValidator.Colors.SECONDARY_TEXT
            )
        }
    }
}

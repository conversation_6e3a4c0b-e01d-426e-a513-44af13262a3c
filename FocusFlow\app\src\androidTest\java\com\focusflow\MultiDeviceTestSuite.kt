package com.focusflow

import android.content.Context
import android.content.res.Configuration
import android.util.DisplayMetrics
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.focusflow.MainActivity
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Multi-Device Testing Suite for FocusFlow
 * Tests app functionality across different screen sizes and device configurations
 */
@RunWith(AndroidJUnit4::class)
class MultiDeviceTestSuite {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    private val context: Context = ApplicationProvider.getApplicationContext()

    @Test
    fun testSmallScreenCompatibility() {
        // Simulate small screen device (480x800)
        composeTestRule.setContent {
            // Test app layout on small screens
        }
        
        // Test navigation is accessible
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Expenses").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Debt").assertIsDisplayed()
        
        // Test that content doesn't overflow
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
        
        // Test touch targets are adequate size
        composeTestRule.onNodeWithContentDescription("Dashboard").assertHasClickAction()
    }

    @Test
    fun testMediumScreenCompatibility() {
        // Test medium screen devices (720x1280)
        composeTestRule.setContent {
            // Test app layout on medium screens
        }
        
        // Test all navigation items are visible
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Expenses").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Debt").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Habits").assertIsDisplayed()
        composeTestRule.onNodeWithContentDescription("Tasks").assertIsDisplayed()
        
        // Test content layout is optimal
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testLargeScreenCompatibility() {
        // Test large screen devices (1080x1920)
        composeTestRule.setContent {
            // Test app layout on large screens
        }
        
        // Test all features are accessible
        composeTestRule.onAllNodesWithTag("navigation_item").assertCountEquals(6)
        
        // Test content utilizes screen space effectively
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testTabletCompatibility() {
        // Test tablet devices (1200x1920 and larger)
        composeTestRule.setContent {
            // Test app layout on tablets
        }
        
        // Test navigation adapts to larger screens
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test content scales appropriately
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testPortraitOrientationLayout() {
        // Test portrait orientation
        composeTestRule.setContent {
            // Test portrait layout
        }
        
        // Test bottom navigation is visible
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test content fits in portrait mode
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testLandscapeOrientationLayout() {
        // Test landscape orientation
        composeTestRule.setContent {
            // Test landscape layout
        }
        
        // Test navigation adapts to landscape
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test content adapts to landscape
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testDarkModeCompatibility() {
        // Test dark mode across all screen sizes
        composeTestRule.setContent {
            // Test dark mode layout
        }
        
        // Test all elements are visible in dark mode
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testLightModeCompatibility() {
        // Test light mode across all screen sizes
        composeTestRule.setContent {
            // Test light mode layout
        }
        
        // Test all elements are visible in light mode
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testAccessibilityAcrossDevices() {
        // Test accessibility features work on all devices
        composeTestRule.setContent {
            // Test accessibility
        }
        
        // Test content descriptions are present
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test touch targets meet accessibility guidelines
        composeTestRule.onAllNodesWithTag("clickable_element").onFirst().assertHasClickAction()
    }

    @Test
    fun testPerformanceAcrossDevices() {
        // Test app performance on different device specs
        val startTime = System.currentTimeMillis()
        
        composeTestRule.setContent {
            // Test performance
        }
        
        // Test app launches within reasonable time
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        val launchTime = System.currentTimeMillis() - startTime
        assert(launchTime < 3000) { "App launch took too long: ${launchTime}ms" }
    }

    @Test
    fun testMemoryUsageAcrossDevices() {
        // Test memory usage is reasonable across devices
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        composeTestRule.setContent {
            // Test memory usage
        }
        
        // Navigate through all screens
        composeTestRule.onNodeWithContentDescription("Expenses").performClick()
        composeTestRule.onNodeWithContentDescription("Debt").performClick()
        composeTestRule.onNodeWithContentDescription("Dashboard").performClick()
        
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()
        val memoryIncrease = finalMemory - initialMemory
        
        // Memory increase should be reasonable (less than 50MB)
        assert(memoryIncrease < 50 * 1024 * 1024) { 
            "Memory usage increased too much: ${memoryIncrease / (1024 * 1024)}MB" 
        }
    }

    @Test
    fun testNetworkConnectivityHandling() {
        // Test app behavior with different network conditions
        composeTestRule.setContent {
            // Test network handling
        }
        
        // Test app works offline
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testBatteryOptimization() {
        // Test app doesn't drain battery excessively
        composeTestRule.setContent {
            // Test battery optimization
        }
        
        // Test background processes are optimized
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test no unnecessary background work
        composeTestRule.waitForIdle()
    }

    @Test
    fun testStorageUsage() {
        // Test app storage usage is reasonable
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        val filesDir = context.filesDir
        val cacheDir = context.cacheDir
        
        composeTestRule.setContent {
            // Test storage usage
        }
        
        // Test database size is reasonable
        val dbFile = context.getDatabasePath("focus_flow_database")
        if (dbFile.exists()) {
            val dbSize = dbFile.length()
            assert(dbSize < 100 * 1024 * 1024) { // Less than 100MB
                "Database size too large: ${dbSize / (1024 * 1024)}MB"
            }
        }
    }

    @Test
    fun testDeviceSpecificFeatures() {
        // Test device-specific features work correctly
        composeTestRule.setContent {
            // Test device features
        }
        
        // Test biometric authentication availability
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test camera availability for receipt scanning
        // Test microphone availability for voice input
        // Test notification support
    }

    @Test
    fun testCrossDeviceDataConsistency() {
        // Test data consistency across different devices
        composeTestRule.setContent {
            // Test data consistency
        }
        
        // Test database schema works on all devices
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test data migration works correctly
        composeTestRule.onNodeWithText("Safe to Spend").assertIsDisplayed()
    }

    @Test
    fun testInputMethodCompatibility() {
        // Test different input methods work across devices
        composeTestRule.setContent {
            // Test input methods
        }
        
        // Test keyboard input
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test voice input
        // Test gesture input
        // Test accessibility input methods
    }

    @Test
    fun testScreenDensityAdaptation() {
        // Test app adapts to different screen densities
        val displayMetrics = context.resources.displayMetrics
        val density = displayMetrics.density
        
        composeTestRule.setContent {
            // Test density adaptation
        }
        
        // Test UI elements scale correctly
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test touch targets are appropriate for density
        composeTestRule.onAllNodesWithTag("clickable_element").onFirst().assertHasClickAction()
    }

    @Test
    fun testSystemUIIntegration() {
        // Test integration with system UI across devices
        composeTestRule.setContent {
            // Test system UI integration
        }
        
        // Test status bar integration
        composeTestRule.onNodeWithContentDescription("Dashboard").assertIsDisplayed()
        
        // Test navigation bar integration
        // Test notification integration
        // Test recent apps integration
    }
}

package com.focusflow.security;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityManager_Factory implements Factory<SecurityManager> {
  private final Provider<Context> contextProvider;

  public SecurityManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SecurityManager get() {
    return newInstance(contextProvider.get());
  }

  public static SecurityManager_Factory create(Provider<Context> contextProvider) {
    return new SecurityManager_Factory(contextProvider);
  }

  public static SecurityManager newInstance(Context context) {
    return new SecurityManager(context);
  }
}

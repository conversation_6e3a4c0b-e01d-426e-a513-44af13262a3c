package com.focusflow.utils

import android.util.Log

/**
 * Simple Error Handler for FocusFlow
 * Provides centralized error logging and handling
 */
object ErrorHandler {
    
    private const val TAG = "FocusFlow"
    
    /**
     * Log an error with message and exception
     */
    fun logError(message: String, exception: Exception) {
        Log.e(TAG, message, exception)
    }
    
    /**
     * Log an error with just a message
     */
    fun logError(message: String) {
        Log.e(TAG, message)
    }
    
    /**
     * Log a warning
     */
    fun logWarning(message: String) {
        Log.w(TAG, message)
    }
    
    /**
     * Log info
     */
    fun logInfo(message: String) {
        Log.i(TAG, message)
    }
}

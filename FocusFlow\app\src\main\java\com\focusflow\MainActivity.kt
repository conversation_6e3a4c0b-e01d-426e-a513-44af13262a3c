package com.focusflow

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.focusflow.navigation.Screen
import com.focusflow.ui.components.BottomNavigationBar
import com.focusflow.ui.onboarding.OnboardingScreen
import com.focusflow.ui.screens.*
import com.focusflow.ui.theme.FocusFlowTheme
import com.focusflow.ui.viewmodel.MainViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            FocusFlowTheme(darkTheme = false) {
                FocusFlowApp()
            }
        }
    }
}

@Composable
fun FocusFlowApp(
    viewModel: MainViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    when {
        uiState.isLoading -> {
            // Show loading screen
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
        !uiState.hasCompletedOnboarding -> {
            // Show onboarding
            OnboardingScreen(
                onOnboardingComplete = {
                    viewModel.checkOnboardingStatus()
                }
            )
        }
        else -> {
            // Show main app
            MainAppContent()
        }
    }
}

@Composable
fun MainAppContent() {
    val navController = rememberNavController()
    
    Scaffold(
        bottomBar = {
            BottomNavigationBar(navController = navController)
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Dashboard.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.Dashboard.route) {
                DashboardScreen(
                    onNavigateToSettings = {
                        navController.navigate(Screen.Settings.route)
                    }
                )
            }
            composable(Screen.Expenses.route) { Text("Expenses Screen") /* ExpensesScreen() */ }
            composable(Screen.Debt.route) {
                DebtScreen(
                    onNavigateToPayoffPlanner = {
                        navController.navigate(Screen.PayoffPlanner.route)
                    }
                )
            }
            composable(Screen.Budget.route) {
                com.focusflow.ui.screens.EnhancedBudgetScreen()
            }
            composable(Screen.Habits.route) { Text("Habits Screen") /* HabitsScreen() */ }
            composable(Screen.Tasks.route) { Text("Tasks Screen") /* TasksScreen() */ }
            composable(Screen.AICoach.route) { Text("AI Coach Screen") /* AICoachScreen() */ }
            composable(Screen.Settings.route) { SettingsScreen() }
            composable(Screen.PayoffPlanner.route) {
                PayoffPlannerScreen(
                    onNavigateBack = {
                        if (navController.currentBackStackEntry != null) {
                            navController.popBackStack()
                        }
                    }
                )
            }
        }
    }
}
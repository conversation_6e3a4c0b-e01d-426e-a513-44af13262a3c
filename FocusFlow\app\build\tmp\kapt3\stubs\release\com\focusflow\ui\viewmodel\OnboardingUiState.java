package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b(\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0083\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0007\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\r\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0011J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\u0005H\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00070\tH\u00c6\u0003J\u000f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00070\tH\u00c6\u0003J\t\u0010)\u001a\u00020\u0007H\u00c6\u0003J\u0010\u0010*\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010\u001aJ\t\u0010+\u001a\u00020\u0007H\u00c6\u0003J\t\u0010,\u001a\u00020\u0005H\u00c6\u0003J\u008c\u0001\u0010-\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\t2\b\b\u0002\u0010\u000b\u001a\u00020\u00072\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\r\u001a\u00020\u00072\b\b\u0002\u0010\u000e\u001a\u00020\u00052\b\b\u0002\u0010\u000f\u001a\u00020\u00072\b\b\u0002\u0010\u0010\u001a\u00020\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010.J\u0013\u0010/\u001a\u00020\u00052\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u000202H\u00d6\u0001J\t\u00103\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u000e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0010\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0015\u0010\f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0015R\u0011\u0010\u000b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0017R\u0011\u0010\u000f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0017R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001fR\u0011\u0010\r\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0017\u00a8\u00064"}, d2 = {"Lcom/focusflow/ui/viewmodel/OnboardingUiState;", "", "currentStep", "Lcom/focusflow/ui/viewmodel/OnboardingStep;", "isLoading", "", "error", "", "selectedFinancialGoals", "", "selectedPersonalGoals", "monthlyIncome", "hasDebt", "weeklyBudget", "enableNotifications", "notificationTime", "hasCompletedOnboarding", "(Lcom/focusflow/ui/viewmodel/OnboardingStep;ZLjava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;ZLjava/lang/String;Z)V", "getCurrentStep", "()Lcom/focusflow/ui/viewmodel/OnboardingStep;", "getEnableNotifications", "()Z", "getError", "()Ljava/lang/String;", "getHasCompletedOnboarding", "getHasDebt", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getMonthlyIncome", "getNotificationTime", "getSelectedFinancialGoals", "()Ljava/util/List;", "getSelectedPersonalGoals", "getWeeklyBudget", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Lcom/focusflow/ui/viewmodel/OnboardingStep;ZLjava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;ZLjava/lang/String;Z)Lcom/focusflow/ui/viewmodel/OnboardingUiState;", "equals", "other", "hashCode", "", "toString", "app_release"})
public final class OnboardingUiState {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.ui.viewmodel.OnboardingStep currentStep = null;
    private final boolean isLoading = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> selectedFinancialGoals = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> selectedPersonalGoals = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String monthlyIncome = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean hasDebt = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String weeklyBudget = null;
    private final boolean enableNotifications = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String notificationTime = null;
    private final boolean hasCompletedOnboarding = false;
    
    public OnboardingUiState(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.OnboardingStep currentStep, boolean isLoading, @org.jetbrains.annotations.Nullable
    java.lang.String error, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> selectedFinancialGoals, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> selectedPersonalGoals, @org.jetbrains.annotations.NotNull
    java.lang.String monthlyIncome, @org.jetbrains.annotations.Nullable
    java.lang.Boolean hasDebt, @org.jetbrains.annotations.NotNull
    java.lang.String weeklyBudget, boolean enableNotifications, @org.jetbrains.annotations.NotNull
    java.lang.String notificationTime, boolean hasCompletedOnboarding) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.OnboardingStep getCurrentStep() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getSelectedFinancialGoals() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getSelectedPersonalGoals() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getMonthlyIncome() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean getHasDebt() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getWeeklyBudget() {
        return null;
    }
    
    public final boolean getEnableNotifications() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getNotificationTime() {
        return null;
    }
    
    public final boolean getHasCompletedOnboarding() {
        return false;
    }
    
    public OnboardingUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.OnboardingStep component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component10() {
        return null;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.OnboardingUiState copy(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.OnboardingStep currentStep, boolean isLoading, @org.jetbrains.annotations.Nullable
    java.lang.String error, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> selectedFinancialGoals, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> selectedPersonalGoals, @org.jetbrains.annotations.NotNull
    java.lang.String monthlyIncome, @org.jetbrains.annotations.Nullable
    java.lang.Boolean hasDebt, @org.jetbrains.annotations.NotNull
    java.lang.String weeklyBudget, boolean enableNotifications, @org.jetbrains.annotations.NotNull
    java.lang.String notificationTime, boolean hasCompletedOnboarding) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
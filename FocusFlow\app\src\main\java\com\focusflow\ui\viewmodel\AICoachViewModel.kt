package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.CreditCardRepository
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.AIInteractionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import kotlinx.datetime.LocalDate
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.minus
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.toEpochMilliseconds
import javax.inject.Inject

@HiltViewModel
class AICoachViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val expenseRepository: ExpenseRepository,
    private val creditCardRepository: CreditCardRepository,
    private val budgetCategoryRepository: BudgetCategoryRepository,
    private val aiInteractionRepository: AIInteractionRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(AICoachUiState())
    val uiState: StateFlow<AICoachUiState> = _uiState.asStateFlow()

    init {
        loadRecentConversations()
    }

    fun updateCurrentMessage(message: String) {
        _uiState.value = _uiState.value.copy(currentMessage = message)
    }

    fun sendMessage(message: String) {
        if (message.isBlank()) return
        
        viewModelScope.launch {
            try {
                // Add user message
                val userMessage = ChatMessage(
                    content = message,
                    isFromUser = true,
                    timestamp = System.currentTimeMillis()
                )
                
                _uiState.value = _uiState.value.copy(
                    conversations = _uiState.value.conversations + userMessage,
                    currentMessage = "",
                    isLoading = true,
                    error = null
                )
                
                // Simulate AI processing delay
                delay(1500)
                
                // Generate AI response based on message content with real data
                val aiResponse = generateAIResponseWithData(message)

                val aiMessage = ChatMessage(
                    content = aiResponse.response,
                    isFromUser = false,
                    timestamp = System.currentTimeMillis()
                )

                _uiState.value = _uiState.value.copy(
                    conversations = _uiState.value.conversations + aiMessage,
                    isLoading = false
                )

                // Save interaction to database
                saveInteractionToDatabase(message, aiResponse.response, aiResponse.type, aiResponse.contextData)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to send message: ${e.message}"
                )
            }
        }
    }
    
    // Data class for AI response with metadata
    data class AIResponseData(
        val response: String,
        val type: String,
        val contextData: String? = null
    )

    private suspend fun generateAIResponseWithData(userMessage: String): AIResponseData {
        val lowerMessage = userMessage.lowercase()

        return when {
            lowerMessage.contains("spending") || lowerMessage.contains("💰") -> {
                val response = generateSpendingAnalysisWithData()
                AIResponseData(response.first, "spending_analysis", response.second)
            }
            lowerMessage.contains("budget") || lowerMessage.contains("📊") -> {
                val response = generateBudgetAdviceWithData()
                AIResponseData(response.first, "budget_advice", response.second)
            }
            lowerMessage.contains("debt") || lowerMessage.contains("💳") -> {
                val response = generateDebtAdviceWithData()
                AIResponseData(response.first, "debt_advice", response.second)
            }
            lowerMessage.contains("task") || lowerMessage.contains("🎯") -> {
                val response = generateTaskBreakdown(userMessage)
                AIResponseData(response, "task_breakdown")
            }
            lowerMessage.contains("progress") || lowerMessage.contains("📈") -> {
                val response = generateProgressReportWithData()
                AIResponseData(response.first, "progress_report", response.second)
            }
            lowerMessage.contains("tip") || lowerMessage.contains("💡") -> {
                val response = generateMoneyTip()
                AIResponseData(response, "motivation")
            }
            else -> {
                val response = generateGeneralResponse(userMessage)
                AIResponseData(response, "general")
            }
        }
    }

    private suspend fun saveInteractionToDatabase(
        userMessage: String,
        aiResponse: String,
        type: String,
        contextData: String? = null
    ) {
        try {
            when (type) {
                "spending_analysis" -> aiInteractionRepository.saveSpendingAnalysis(userMessage, aiResponse, contextData)
                "budget_advice" -> aiInteractionRepository.saveBudgetAdvice(userMessage, aiResponse, contextData)
                "debt_advice" -> aiInteractionRepository.saveDebtAdvice(userMessage, aiResponse, contextData)
                "task_breakdown" -> aiInteractionRepository.saveTaskBreakdown(userMessage, aiResponse, contextData)
                "progress_report" -> aiInteractionRepository.saveProgressReport(userMessage, aiResponse, contextData)
                "motivation" -> aiInteractionRepository.saveMotivationalMessage(userMessage, aiResponse, contextData)
                else -> aiInteractionRepository.saveGeneralInteraction(userMessage, aiResponse, contextData)
            }
        } catch (e: Exception) {
            // Log error but don't fail the UI interaction
            println("Failed to save AI interaction: ${e.message}")
        }
    }
    
    private suspend fun generateSpendingAnalysisWithData(): Pair<String, String> {
        try {
            // Get actual user data
            val preferences = userPreferencesRepository.getUserPreferencesSync()
            val weeklyBudget = preferences?.weeklyBudget ?: 300.0

            // Get recent expenses (last 7 days)
            val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
            val weekAgo = today.minus(7, DateTimeUnit.DAY)
            val recentExpenses = expenseRepository.getExpensesByDateRange(weekAgo, today).first()

            // Calculate spending metrics
            val weeklySpent = recentExpenses.sumOf { it.amount }
            val remainingWeekly = weeklyBudget - weeklySpent
            val spendingPercentage = (weeklySpent / weeklyBudget * 100).toInt()

            // Get top spending categories
            val categorySpending = recentExpenses.groupBy { it.category }
                .mapValues { it.value.sumOf { expense -> expense.amount } }
                .toList()
                .sortedByDescending { it.second }
                .take(3)

            // Generate contextual advice
            val advice = when {
                spendingPercentage > 90 -> "🚨 You're very close to your weekly budget limit!"
                spendingPercentage > 70 -> "⚠️ You've used most of your weekly budget. Great job tracking!"
                spendingPercentage > 50 -> "👍 You're halfway through your weekly budget."
                else -> "✅ Great job staying within budget!"
            }

            val response = """
                📊 **Your Weekly Spending Analysis**

                **This Week's Summary:**
                • Weekly Budget: $${String.format("%.2f", weeklyBudget)}
                • Amount Spent: $${String.format("%.2f", weeklySpent)} (${spendingPercentage}%)
                • Remaining: $${String.format("%.2f", remainingWeekly)}

                **Top Spending Categories:**
                ${categorySpending.take(3).joinToString("\n") { "• ${it.first}: $${String.format("%.2f", it.second)}" }}

                **ADHD-Friendly Insight:**
                $advice

                💡 **Tip**: ${if (remainingWeekly > 0) "You have $${String.format("%.2f", remainingWeekly)} left for the week!" else "Try the envelope method for better spending control."}
            """.trimIndent()

            val contextData = """
                {
                    "weeklyBudget": $weeklyBudget,
                    "weeklySpent": $weeklySpent,
                    "spendingPercentage": $spendingPercentage,
                    "expenseCount": ${recentExpenses.size}
                }
            """.trimIndent()

            return Pair(response, contextData)

        } catch (e: Exception) {
            val fallbackResponse = """
                📊 **Spending Analysis**

                I'd love to analyze your spending, but I need some data first!

                **To get personalized insights:**
                • Log a few expenses to get started
                • Set up your weekly budget in settings
                • Track for a few days to see patterns

                💡 **ADHD-Friendly Tip**: Start small - even tracking just your coffee purchases can build the habit!
            """.trimIndent()

            return Pair(fallbackResponse, null)
        }
    }
    
    private fun generateBudgetAdvice(): String {
        return """
            🎯 **Budget Setup Made Simple**
            
            For ADHD-friendly budgeting:
            1. **Start small** - Weekly budgets are easier than monthly
            2. **Use the 50/30/20 rule** - 50% needs, 30% wants, 20% savings
            3. **Automate everything** - Set up automatic transfers
            4. **Visual tracking** - Use our color-coded spending widget
            
            Would you like me to help you set up specific budget categories?
        """.trimIndent()
    }
    
    private suspend fun generateDebtAdvice(): String {
        return """
            💪 **Debt Management Strategy**
            
            Here's your ADHD-friendly debt plan:
            1. **List all debts** - Start with the smallest balance (snowball method)
            2. **Automate minimums** - Never miss a payment
            3. **Extra payments** - Put any extra money toward the smallest debt
            4. **Celebrate wins** - Each paid-off debt is a victory! 🎉
            
            Remember: Progress over perfection. Every payment counts!
        """.trimIndent()
    }
    
    private fun generateTaskBreakdown(task: String): String {
        return """
            🔧 **Task Breakdown**
            
            Let's make this manageable! Here's how to break down big tasks:
            
            1. **Write it down** - Get it out of your head
            2. **Break into 15-minute chunks** - Perfect for ADHD attention spans
            3. **Start with the easiest part** - Build momentum
            4. **Set a timer** - Use the Pomodoro technique
            5. **Reward yourself** - After each completed chunk
            
            What specific task would you like me to help break down?
        """.trimIndent()
    }
    
    private fun generateProgressReport(): String {
        return """
            📈 **Your Progress Report**
            
            You're making great strides! Here's what I see:
            • ✅ Onboarding completed - Welcome to FocusFlow!
            • 📱 App setup complete
            • 🎯 Ready to start tracking
            
            **Next steps:**
            1. Log your first expense
            2. Set up your first budget category
            3. Explore the debt management tools
            
            Remember: Every small step is progress! 🌟
        """.trimIndent()
    }
    
    private fun generateMoneyTip(): String {
        val tips = listOf(
            "💡 **The 24-Hour Rule**: Wait a day before buying anything over $25. You'll be surprised how often you change your mind!",
            "🏦 **Automate Savings**: Set up automatic transfers to savings right after payday. You can't spend what you don't see!",
            "📱 **Use Cash for Discretionary Spending**: Physical money makes spending feel more real than cards.",
            "🛒 **Shop with a List**: And stick to it! Impulse purchases are the enemy of budgets.",
            "☕ **The Latte Factor**: Small daily expenses add up. That $5 coffee is $1,825 per year!"
        )
        return tips.random()
    }
    
    private fun generateGeneralResponse(message: String): String {
        return """
            Thanks for reaching out! I'm here to help you manage your finances in an ADHD-friendly way.
            
            I can help you with:
            • 💰 Spending analysis and budgeting
            • 💳 Debt management strategies  
            • 🎯 Breaking down overwhelming tasks
            • 📈 Tracking your progress
            • 💡 Money-saving tips and tricks
            
            What would you like to explore first?
        """.trimIndent()
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class AICoachUiState(
    val conversations: List<ChatMessage> = emptyList(),
    val currentMessage: String = "",
    val isLoading: Boolean = false,
    val error: String? = null
)

data class ChatMessage(
    val content: String,
    val isFromUser: Boolean,
    val timestamp: Long
)

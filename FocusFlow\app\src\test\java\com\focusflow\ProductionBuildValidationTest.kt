package com.focusflow

import org.junit.Test
import org.junit.Assert.*
import java.io.File

/**
 * Production Build Validation Tests
 * Validates that production builds meet quality and performance standards
 */
class ProductionBuildValidationTest {

    @Test
    fun `PRODUCTION BUILD TEST 1 - Debug APK exists and has reasonable size`() {
        val apkPath = "app/build/outputs/apk/debug/app-debug.apk"
        val apkFile = File(apkPath)
        
        assertTrue("Debug APK should exist", apkFile.exists())
        
        val apkSizeBytes = apkFile.length()
        val apkSizeMB = apkSizeBytes / (1024 * 1024)
        
        // APK should be reasonable size (less than 150MB for debug)
        assertTrue("Debug APK size should be reasonable: ${apkSizeMB}MB", apkSizeMB < 150)
        
        // APK should not be too small (indicates missing components)
        assertTrue("Debug APK should not be too small: ${apkSizeMB}MB", apkSizeMB > 10)
        
        println("Debug APK size: ${apkSizeMB}MB")
    }

    @Test
    fun `PRODUCTION BUILD TEST 2 - Build configuration validation`() {
        // Test that build variants are properly configured
        val buildTypes = listOf("debug", "staging", "release")
        
        buildTypes.forEach { buildType ->
            // Validate build type exists in configuration
            assertTrue("Build type $buildType should be configured", true)
        }
        
        // Test that signing configurations exist
        assertTrue("Debug signing config should exist", true)
        
        // Test that ProGuard rules are configured
        val proguardFile = File("app/proguard-rules.pro")
        assertTrue("ProGuard rules file should exist", proguardFile.exists())
        
        val proguardContent = proguardFile.readText()
        assertTrue("ProGuard should keep model classes", 
            proguardContent.contains("com.focusflow.data.model"))
        assertTrue("ProGuard should keep security classes", 
            proguardContent.contains("com.focusflow.security"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 3 - Multi-device support validation`() {
        // Test that multi-device configurations are present
        val manifestFile = File("app/src/main/AndroidManifest.xml")
        assertTrue("AndroidManifest.xml should exist", manifestFile.exists())
        
        val manifestContent = manifestFile.readText()
        
        // Test screen support
        assertTrue("Should support different screen sizes", 
            manifestContent.contains("android:screenOrientation"))
        
        // Test permission declarations
        assertTrue("Should declare internet permission", 
            manifestContent.contains("android.permission.INTERNET"))
        assertTrue("Should declare biometric permission", 
            manifestContent.contains("android.permission.USE_BIOMETRIC"))
        
        // Test network security config
        assertTrue("Should have network security config", 
            manifestContent.contains("network_security_config"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 4 - Resource optimization validation`() {
        // Test that resources are properly configured
        val buildGradleFile = File("app/build.gradle")
        assertTrue("build.gradle should exist", buildGradleFile.exists())
        
        val buildGradleContent = buildGradleFile.readText()
        
        // Test resource configurations
        assertTrue("Should have resource configurations", 
            buildGradleContent.contains("resConfigs"))
        
        // Test ABI filters
        assertTrue("Should have ABI filters", 
            buildGradleContent.contains("abiFilters"))
        
        // Test minification
        assertTrue("Should enable minification for release", 
            buildGradleContent.contains("minifyEnabled true"))
        
        // Test resource shrinking
        assertTrue("Should enable resource shrinking", 
            buildGradleContent.contains("shrinkResources true"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 5 - Security configuration validation`() {
        // Test network security configuration
        val networkSecurityFile = File("app/src/main/res/xml/network_security_config.xml")
        assertTrue("Network security config should exist", networkSecurityFile.exists())
        
        val networkSecurityContent = networkSecurityFile.readText()
        
        // Test certificate pinning
        assertTrue("Should have certificate pinning", 
            networkSecurityContent.contains("pin-set"))
        
        // Test cleartext traffic blocking
        assertTrue("Should block cleartext traffic", 
            networkSecurityContent.contains("cleartextTrafficPermitted=\"false\""))
        
        // Test ProGuard security rules
        val proguardFile = File("app/proguard-rules.pro")
        val proguardContent = proguardFile.readText()
        
        assertTrue("Should keep security classes", 
            proguardContent.contains("com.focusflow.security"))
        assertTrue("Should remove logging in release", 
            proguardContent.contains("android.util.Log"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 6 - Performance optimization validation`() {
        val buildGradleFile = File("app/build.gradle")
        val buildGradleContent = buildGradleFile.readText()
        
        // Test compilation optimizations
        assertTrue("Should use Java 11", 
            buildGradleContent.contains("JavaVersion.VERSION_11"))
        
        // Test Kotlin optimizations
        assertTrue("Should set Kotlin JVM target", 
            buildGradleContent.contains("jvmTarget = '11'"))
        
        // Test Compose optimizations
        assertTrue("Should enable Compose", 
            buildGradleContent.contains("compose true"))
        
        // Test R8 optimizations
        assertTrue("Should use R8 optimization", 
            buildGradleContent.contains("proguard-android-optimize.txt"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 7 - Dependency validation`() {
        val buildGradleFile = File("app/build.gradle")
        val buildGradleContent = buildGradleFile.readText()
        
        // Test essential dependencies
        assertTrue("Should include Compose BOM", 
            buildGradleContent.contains("compose-bom"))
        assertTrue("Should include Hilt", 
            buildGradleContent.contains("hilt-android"))
        assertTrue("Should include Room", 
            buildGradleContent.contains("room-runtime"))
        assertTrue("Should include Security Crypto", 
            buildGradleContent.contains("security-crypto"))
        
        // Test version consistency
        assertTrue("Should use consistent Compose version", 
            buildGradleContent.contains("compose_version"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 8 - Testing framework validation`() {
        val buildGradleFile = File("app/build.gradle")
        val buildGradleContent = buildGradleFile.readText()
        
        // Test unit testing dependencies
        assertTrue("Should include JUnit", 
            buildGradleContent.contains("junit:junit"))
        assertTrue("Should include Truth", 
            buildGradleContent.contains("truth"))
        assertTrue("Should include Coroutines Test", 
            buildGradleContent.contains("kotlinx-coroutines-test"))
        
        // Test instrumented testing dependencies
        assertTrue("Should include Espresso", 
            buildGradleContent.contains("espresso-core"))
        assertTrue("Should include Compose UI Test", 
            buildGradleContent.contains("ui-test-junit4"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 9 - Accessibility validation`() {
        // Test that accessibility features are configured
        val manifestFile = File("app/src/main/AndroidManifest.xml")
        val manifestContent = manifestFile.readText()
        
        // Test accessibility service declaration
        assertTrue("Should support RTL", 
            manifestContent.contains("android:supportsRtl=\"true\""))
        
        // Test that ADHD-friendly components exist
        val adhdComponentsFile = File("app/src/main/java/com/focusflow/ui/components/ADHDFriendlyComponents.kt")
        assertTrue("ADHD-friendly components should exist", adhdComponentsFile.exists())
        
        val adhdComponentsContent = adhdComponentsFile.readText()
        assertTrue("Should have ADHD-friendly button", 
            adhdComponentsContent.contains("ADHDFriendlyButton"))
        assertTrue("Should have ADHD-friendly card", 
            adhdComponentsContent.contains("ADHDFriendlyCard"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 10 - Responsive design validation`() {
        // Test that responsive layout manager exists
        val responsiveLayoutFile = File("app/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt")
        assertTrue("Responsive layout manager should exist", responsiveLayoutFile.exists())
        
        val responsiveLayoutContent = responsiveLayoutFile.readText()
        
        // Test screen size support
        assertTrue("Should support compact screens", 
            responsiveLayoutContent.contains("COMPACT"))
        assertTrue("Should support medium screens", 
            responsiveLayoutContent.contains("MEDIUM"))
        assertTrue("Should support expanded screens", 
            responsiveLayoutContent.contains("EXPANDED"))
        
        // Test orientation support
        assertTrue("Should support portrait orientation", 
            responsiveLayoutContent.contains("PORTRAIT"))
        assertTrue("Should support landscape orientation", 
            responsiveLayoutContent.contains("LANDSCAPE"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 11 - Multi-device testing validation`() {
        // Test that multi-device test suite exists
        val multiDeviceTestFile = File("app/src/androidTest/java/com/focusflow/MultiDeviceTestSuite.kt")
        assertTrue("Multi-device test suite should exist", multiDeviceTestFile.exists())
        
        val multiDeviceTestContent = multiDeviceTestFile.readText()
        
        // Test device compatibility tests
        assertTrue("Should test small screen compatibility", 
            multiDeviceTestContent.contains("testSmallScreenCompatibility"))
        assertTrue("Should test tablet compatibility", 
            multiDeviceTestContent.contains("testTabletCompatibility"))
        assertTrue("Should test performance across devices", 
            multiDeviceTestContent.contains("testPerformanceAcrossDevices"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 12 - Build script validation`() {
        // Test that production build script exists
        val buildScriptFile = File("scripts/build-production.sh")
        assertTrue("Production build script should exist", buildScriptFile.exists())
        
        val buildScriptContent = buildScriptFile.readText()
        
        // Test build script functionality
        assertTrue("Should check prerequisites", 
            buildScriptContent.contains("check_prerequisites"))
        assertTrue("Should run tests", 
            buildScriptContent.contains("run_unit_tests"))
        assertTrue("Should build release APK", 
            buildScriptContent.contains("build_release_apk"))
        assertTrue("Should analyze APK", 
            buildScriptContent.contains("analyze_apk"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 13 - Device testing matrix validation`() {
        // Test that device testing matrix exists
        val deviceMatrixFile = File("device-testing/device-matrix.json")
        assertTrue("Device testing matrix should exist", deviceMatrixFile.exists())
        
        val deviceMatrixContent = deviceMatrixFile.readText()
        
        // Test device categories
        assertTrue("Should include budget phones", 
            deviceMatrixContent.contains("budget_phones"))
        assertTrue("Should include tablets", 
            deviceMatrixContent.contains("tablets"))
        assertTrue("Should include foldable devices", 
            deviceMatrixContent.contains("foldable_devices"))
        
        // Test Android version coverage
        assertTrue("Should support Android 7.0+", 
            deviceMatrixContent.contains("Android 7.0"))
        assertTrue("Should support latest Android", 
            deviceMatrixContent.contains("Android 14"))
    }

    @Test
    fun `PRODUCTION BUILD TEST 14 - Version and metadata validation`() {
        val buildGradleFile = File("app/build.gradle")
        val buildGradleContent = buildGradleFile.readText()
        
        // Test version configuration
        assertTrue("Should have version code", 
            buildGradleContent.contains("versionCode"))
        assertTrue("Should have version name", 
            buildGradleContent.contains("versionName"))
        
        // Test target SDK
        assertTrue("Should target latest SDK", 
            buildGradleContent.contains("targetSdk 34"))
        
        // Test minimum SDK
        assertTrue("Should support minimum SDK 24", 
            buildGradleContent.contains("minSdk 24"))
        
        // Test application ID
        assertTrue("Should have application ID", 
            buildGradleContent.contains("applicationId \"com.focusflow\""))
    }

    @Test
    fun `PRODUCTION BUILD TEST 15 - File structure validation`() {
        // Test essential directories exist
        assertTrue("Main source directory should exist", 
            File("app/src/main").exists())
        assertTrue("Test directory should exist", 
            File("app/src/test").exists())
        assertTrue("Android test directory should exist", 
            File("app/src/androidTest").exists())
        
        // Test essential files exist
        assertTrue("MainActivity should exist", 
            File("app/src/main/java/com/focusflow/MainActivity.kt").exists())
        assertTrue("Application class should exist", 
            File("app/src/main/java/com/focusflow/FocusFlowApplication.kt").exists())
        
        // Test resource directories
        assertTrue("Layout resources should exist", 
            File("app/src/main/res").exists())
        assertTrue("Values resources should exist", 
            File("app/src/main/res/values").exists())
    }
}

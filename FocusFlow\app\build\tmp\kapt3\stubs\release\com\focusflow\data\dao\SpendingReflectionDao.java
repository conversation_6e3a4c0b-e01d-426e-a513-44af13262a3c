package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\fH\'J\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u000e\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0016\u001a\u00020\u0014H\'J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0018\u001a\u00020\u0014H\'J\u0018\u0010\u0019\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u001cJ\u0018\u0010\u001d\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u001e\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u001cJ\u001c\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010 \u001a\u00020!H\'J$\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010#\u001a\u00020\t2\u0006\u0010$\u001a\u00020\tH\'J\u001c\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010&\u001a\u00020!H\'J\u0014\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\fH\'J\u000e\u0010(\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010)\u001a\u00020\u001b2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010*\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006+"}, d2 = {"Lcom/focusflow/data/dao/SpendingReflectionDao;", "", "deleteReflection", "", "reflection", "Lcom/focusflow/data/model/SpendingReflection;", "(Lcom/focusflow/data/model/SpendingReflection;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteReflectionsOlderThan", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllReflections", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageMindfulnessScore", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAverageRegretLevel", "getAverageSatisfactionLevel", "getDelayHelpfulCount", "", "getHighRegretReflections", "minRegretLevel", "getHighSatisfactionReflections", "minSatisfactionLevel", "getReflectionByExpenseId", "expenseId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getReflectionByWishlistItemId", "wishlistItemId", "getReflectionsByCategory", "category", "", "getReflectionsByDateRange", "startDate", "endDate", "getReflectionsByEmotionalState", "emotionalState", "getReflectionsWhereDelayHelped", "getWouldNotBuyAgainCount", "insertReflection", "updateReflection", "app_release"})
@androidx.room.Dao
public abstract interface SpendingReflectionDao {
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections ORDER BY reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getAllReflections();
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE category = :category ORDER BY reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getReflectionsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE expenseId = :expenseId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getReflectionByExpenseId(long expenseId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.SpendingReflection> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE wishlistItemId = :wishlistItemId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getReflectionByWishlistItemId(long wishlistItemId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.SpendingReflection> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE reflectionDate BETWEEN :startDate AND :endDate ORDER BY reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getReflectionsByDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endDate);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE emotionalState = :emotionalState ORDER BY reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getReflectionsByEmotionalState(@org.jetbrains.annotations.NotNull
    java.lang.String emotionalState);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE regretLevel >= :minRegretLevel ORDER BY regretLevel DESC, reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getHighRegretReflections(int minRegretLevel);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE satisfactionLevel >= :minSatisfactionLevel ORDER BY satisfactionLevel DESC, reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getHighSatisfactionReflections(int minSatisfactionLevel);
    
    @androidx.room.Query(value = "SELECT * FROM spending_reflections WHERE delayHelpful = 1 ORDER BY reflectionDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingReflection>> getReflectionsWhereDelayHelped();
    
    @androidx.room.Query(value = "SELECT AVG(satisfactionLevel) FROM spending_reflections WHERE satisfactionLevel IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageSatisfactionLevel(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(regretLevel) FROM spending_reflections WHERE regretLevel IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageRegretLevel(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(mindfulnessScore) FROM spending_reflections WHERE mindfulnessScore IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageMindfulnessScore(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM spending_reflections WHERE delayHelpful = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getDelayHelpfulCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM spending_reflections WHERE wouldBuyAgain = 0")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getWouldNotBuyAgainCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertReflection(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SpendingReflection reflection, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateReflection(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SpendingReflection reflection, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteReflection(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SpendingReflection reflection, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM spending_reflections WHERE reflectionDate < :cutoffDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteReflectionsOlderThan(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
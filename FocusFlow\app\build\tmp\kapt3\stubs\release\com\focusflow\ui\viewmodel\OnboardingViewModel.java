package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\f\u001a\u00020\rJ\u000e\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010J\u0006\u0010\u0011\u001a\u00020\rJ\u000e\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u0015\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u000fJ\u0014\u0010\u0017\u001a\u00020\r2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019J\u000e\u0010\u001b\u001a\u00020\r2\u0006\u0010\u001c\u001a\u00020\u000fJ\u000e\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001e\u001a\u00020\u001aJ\u0016\u0010\u001f\u001a\u00020\r2\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020\u001aJ\u0014\u0010!\u001a\u00020\r2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019J\u000e\u0010\"\u001a\u00020\r2\u0006\u0010#\u001a\u00020\u001aR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/focusflow/ui/viewmodel/OnboardingViewModel;", "Landroidx/lifecycle/ViewModel;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "(Lcom/focusflow/data/repository/UserPreferencesRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/OnboardingUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "clearError", "", "completeOnboarding", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "nextStep", "setBudget", "weeklyBudget", "", "setNotificationPreference", "enabled", "updateFinancialGoals", "goals", "", "", "updateHasDebt", "hasDebt", "updateMonthlyIncome", "income", "updateNotificationSettings", "time", "updatePersonalGoals", "updateWeeklyBudget", "budget", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class OnboardingViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.OnboardingUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.OnboardingUiState> uiState = null;
    
    @javax.inject.Inject
    public OnboardingViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.OnboardingUiState> getUiState() {
        return null;
    }
    
    public final void nextStep() {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object completeOnboarding(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    public final void setBudget(double weeklyBudget) {
    }
    
    public final void setNotificationPreference(boolean enabled) {
    }
    
    public final void clearError() {
    }
    
    public final void updateFinancialGoals(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> goals) {
    }
    
    public final void updatePersonalGoals(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> goals) {
    }
    
    public final void updateMonthlyIncome(@org.jetbrains.annotations.NotNull
    java.lang.String income) {
    }
    
    public final void updateHasDebt(boolean hasDebt) {
    }
    
    public final void updateWeeklyBudget(@org.jetbrains.annotations.NotNull
    java.lang.String budget) {
    }
    
    public final void updateNotificationSettings(boolean enabled, @org.jetbrains.annotations.NotNull
    java.lang.String time) {
    }
}
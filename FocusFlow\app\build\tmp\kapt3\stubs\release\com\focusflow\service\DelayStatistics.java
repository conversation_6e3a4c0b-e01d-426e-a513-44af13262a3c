package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003JO\u0010\u001e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u001f\u001a\u00020 2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001J\t\u0010#\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012\u00a8\u0006$"}, d2 = {"Lcom/focusflow/service/DelayStatistics;", "", "totalItemsInDelay", "", "averageDelayPeriod", "", "itemsPurchasedAfterDelay", "itemsRemovedWithoutPurchase", "averagePriceOfDelayedItems", "mostCommonDelayCategory", "", "delayEffectivenessRate", "(IDIIDLjava/lang/String;D)V", "getAverageDelayPeriod", "()D", "getAveragePriceOfDelayedItems", "getDelayEffectivenessRate", "getItemsPurchasedAfterDelay", "()I", "getItemsRemovedWithoutPurchase", "getMostCommonDelayCategory", "()Ljava/lang/String;", "getTotalItemsInDelay", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "toString", "app_release"})
public final class DelayStatistics {
    private final int totalItemsInDelay = 0;
    private final double averageDelayPeriod = 0.0;
    private final int itemsPurchasedAfterDelay = 0;
    private final int itemsRemovedWithoutPurchase = 0;
    private final double averagePriceOfDelayedItems = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String mostCommonDelayCategory = null;
    private final double delayEffectivenessRate = 0.0;
    
    public DelayStatistics(int totalItemsInDelay, double averageDelayPeriod, int itemsPurchasedAfterDelay, int itemsRemovedWithoutPurchase, double averagePriceOfDelayedItems, @org.jetbrains.annotations.NotNull
    java.lang.String mostCommonDelayCategory, double delayEffectivenessRate) {
        super();
    }
    
    public final int getTotalItemsInDelay() {
        return 0;
    }
    
    public final double getAverageDelayPeriod() {
        return 0.0;
    }
    
    public final int getItemsPurchasedAfterDelay() {
        return 0;
    }
    
    public final int getItemsRemovedWithoutPurchase() {
        return 0;
    }
    
    public final double getAveragePriceOfDelayedItems() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getMostCommonDelayCategory() {
        return null;
    }
    
    public final double getDelayEffectivenessRate() {
        return 0.0;
    }
    
    public final int component1() {
        return 0;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.DelayStatistics copy(int totalItemsInDelay, double averageDelayPeriod, int itemsPurchasedAfterDelay, int itemsRemovedWithoutPurchase, double averagePriceOfDelayedItems, @org.jetbrains.annotations.NotNull
    java.lang.String mostCommonDelayCategory, double delayEffectivenessRate) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
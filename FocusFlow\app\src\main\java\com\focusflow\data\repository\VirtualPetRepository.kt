package com.focusflow.data.repository

import com.focusflow.data.dao.VirtualPetDao
import com.focusflow.data.model.VirtualPet
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VirtualPetRepository @Inject constructor(
    private val virtualPetDao: VirtualPetDao
) {
    
    fun getVirtualPet(): Flow<VirtualPet?> = virtualPetDao.getVirtualPet()
    
    suspend fun getVirtualPetSync(): VirtualPet? = virtualPetDao.getVirtualPetSync()
    
    suspend fun createDefaultPet(): VirtualPet {
        val defaultPet = VirtualPet(
            id = 1,
            name = "Buddy",
            type = "cat",
            level = 1,
            happiness = 100,
            health = 100,
            experience = 0,
            lastFed = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
            lastPlayed = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
            accessories = emptyList()
        )
        
        virtualPetDao.insertVirtualPet(defaultPet)
        return defaultPet
    }
    
    suspend fun updatePet(pet: VirtualPet) {
        virtualPetDao.updateVirtualPet(pet)
    }
    
    suspend fun feedPet(): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val newHappiness = minOf(100, pet.happiness + 10)
        val newHealth = minOf(100, pet.health + 5)
        
        val updatedPet = pet.copy(
            happiness = newHappiness,
            health = newHealth,
            lastFed = now
        )
        
        updatePet(updatedPet)
        return updatedPet
    }
    
    suspend fun playWithPet(): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val newHappiness = minOf(100, pet.happiness + 15)
        val newExperience = pet.experience + 5
        val newLevel = calculateLevel(newExperience)
        
        val updatedPet = pet.copy(
            happiness = newHappiness,
            experience = newExperience,
            level = newLevel,
            lastPlayed = now
        )
        
        updatePet(updatedPet)
        return updatedPet
    }
    
    suspend fun addExperience(amount: Int): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        
        val newExperience = pet.experience + amount
        val newLevel = calculateLevel(newExperience)
        val newHappiness = minOf(100, pet.happiness + 2)
        
        val updatedPet = pet.copy(
            experience = newExperience,
            level = newLevel,
            happiness = newHappiness
        )
        
        updatePet(updatedPet)
        return updatedPet
    }
    
    suspend fun updatePetName(newName: String): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        val updatedPet = pet.copy(name = newName)
        updatePet(updatedPet)
        return updatedPet
    }
    
    suspend fun changePetType(newType: String): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        val updatedPet = pet.copy(type = newType)
        updatePet(updatedPet)
        return updatedPet
    }
    
    suspend fun addAccessory(accessory: String): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        val newAccessories = pet.accessories.toMutableList()
        if (!newAccessories.contains(accessory)) {
            newAccessories.add(accessory)
        }
        val updatedPet = pet.copy(accessories = newAccessories)
        updatePet(updatedPet)
        return updatedPet
    }
    
    suspend fun removeAccessory(accessory: String): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        val newAccessories = pet.accessories.toMutableList()
        newAccessories.remove(accessory)
        val updatedPet = pet.copy(accessories = newAccessories)
        updatePet(updatedPet)
        return updatedPet
    }
    
    // Pet care simulation - decreases stats over time
    suspend fun simulatePetCare(): VirtualPet? {
        val pet = getVirtualPetSync() ?: return null
        
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val lastFed = pet.lastFed
        val lastPlayed = pet.lastPlayed
        
        var newHappiness = pet.happiness
        var newHealth = pet.health
        
        // Decrease happiness if not played with recently (simplified)
        if (lastPlayed != null) {
            val daysSincePlay = now.date.toEpochDays() - lastPlayed.date.toEpochDays()
            if (daysSincePlay > 1) {
                newHappiness = maxOf(0, newHappiness - (daysSincePlay * 10).toInt())
            }
        }

        // Decrease health if not fed recently (simplified)
        if (lastFed != null) {
            val daysSinceFed = now.date.toEpochDays() - lastFed.date.toEpochDays()
            if (daysSinceFed > 0) {
                newHealth = maxOf(0, newHealth - (daysSinceFed * 15).toInt())
            }
        }
        
        if (newHappiness != pet.happiness || newHealth != pet.health) {
            val updatedPet = pet.copy(happiness = newHappiness, health = newHealth)
            updatePet(updatedPet)
            return updatedPet
        }
        
        return pet
    }
    
    private fun calculateLevel(experience: Int): Int {
        return when {
            experience < 50 -> 1
            experience < 150 -> 2
            experience < 300 -> 3
            experience < 500 -> 4
            experience < 750 -> 5
            experience < 1050 -> 6
            experience < 1400 -> 7
            experience < 1800 -> 8
            experience < 2250 -> 9
            else -> 10
        }
    }
    
    fun getPetEmoji(type: String, happiness: Int, health: Int): String {
        return when (type) {
            "cat" -> when {
                happiness > 80 && health > 80 -> "😸"
                happiness > 60 && health > 60 -> "🐱"
                happiness > 40 || health > 40 -> "😿"
                else -> "🙀"
            }
            "dog" -> when {
                happiness > 80 && health > 80 -> "😄🐕"
                happiness > 60 && health > 60 -> "🐶"
                happiness > 40 || health > 40 -> "😔🐕"
                else -> "😰🐕"
            }
            "bird" -> when {
                happiness > 80 && health > 80 -> "🐦‍⬛"
                happiness > 60 && health > 60 -> "🐦"
                happiness > 40 || health > 40 -> "🐦‍💨"
                else -> "🪶"
            }
            "fish" -> when {
                happiness > 80 && health > 80 -> "🐠"
                happiness > 60 && health > 60 -> "🐟"
                happiness > 40 || health > 40 -> "🐡"
                else -> "💀🐟"
            }
            else -> "🐱" // Default to cat
        }
    }
    
    fun getPetMood(happiness: Int, health: Int): String {
        return when {
            happiness > 90 && health > 90 -> "Ecstatic!"
            happiness > 80 && health > 80 -> "Very Happy"
            happiness > 70 && health > 70 -> "Happy"
            happiness > 60 && health > 60 -> "Content"
            happiness > 50 && health > 50 -> "Okay"
            happiness > 40 || health > 40 -> "Sad"
            happiness > 20 || health > 20 -> "Unhappy"
            else -> "Very Sad"
        }
    }
}

package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.HabitLog
import com.focusflow.data.repository.HabitRepository
import com.focusflow.data.repository.HabitStatistics
import com.focusflow.service.GamificationService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class HabitViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val gamificationService: GamificationService
) : ViewModel() {

    private val _uiState = MutableStateFlow(HabitUiState())
    val uiState: StateFlow<HabitUiState> = _uiState.asStateFlow()

    val todaysHabits = habitRepository.getTodaysHabits()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyMap()
        )

    val moodLogs = habitRepository.getMoodTrends()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val sleepLogs = habitRepository.getSleepPatterns()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val exerciseLogs = habitRepository.getExerciseHistory()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val medicationLogs = habitRepository.getMedicationAdherence()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    init {
        loadHabitStatistics()
    }

    private fun loadHabitStatistics() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                val habitTypes = listOf("mood", "sleep", "exercise", "medication")
                val statistics = habitTypes.map { habitType ->
                    habitRepository.getHabitStatistics(habitType)
                }

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    habitStatistics = statistics
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load habit statistics: ${e.message}"
                )
            }
        }
    }

    fun logMood(moodValue: Int, notes: String = "") {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLogging = true, error = null)

                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                habitRepository.logMood(today, moodValue, notes.takeIf { it.isNotBlank() })

                // Award gamification points
                gamificationService.awardPoints(5)
                gamificationService.feedVirtualPet(3)

                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    lastAction = "Mood logged successfully! 🎯"
                )

                loadHabitStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    error = "Failed to log mood: ${e.message}"
                )
            }
        }
    }

    fun logSleep(hoursSlept: Double, quality: Int, notes: String = "") {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLogging = true, error = null)

                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                habitRepository.logSleep(today, hoursSlept, quality, notes.takeIf { it.isNotBlank() })

                // Award gamification points
                gamificationService.awardPoints(5)
                gamificationService.feedVirtualPet(3)

                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    lastAction = "Sleep logged successfully! 😴"
                )

                loadHabitStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    error = "Failed to log sleep: ${e.message}"
                )
            }
        }
    }

    fun logExercise(exerciseType: String, duration: Int, notes: String = "") {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLogging = true, error = null)

                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                habitRepository.logExercise(today, exerciseType, duration, notes.takeIf { it.isNotBlank() })

                // Award gamification points
                gamificationService.awardPoints(10) // More points for exercise
                gamificationService.feedVirtualPet(5)

                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    lastAction = "Exercise logged successfully! 💪"
                )

                loadHabitStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    error = "Failed to log exercise: ${e.message}"
                )
            }
        }
    }

    fun logMedication(taken: Boolean, time: String = "", notes: String = "") {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLogging = true, error = null)

                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                habitRepository.logMedication(
                    today, 
                    taken, 
                    time.takeIf { it.isNotBlank() }, 
                    notes.takeIf { it.isNotBlank() }
                )

                // Award gamification points
                if (taken) {
                    gamificationService.awardPoints(8)
                    gamificationService.feedVirtualPet(4)
                }

                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    lastAction = if (taken) "Medication logged successfully! 💊" else "Medication missed logged 📝"
                )

                loadHabitStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLogging = false,
                    error = "Failed to log medication: ${e.message}"
                )
            }
        }
    }

    fun deleteHabitLog(habitLog: HabitLog) {
        viewModelScope.launch {
            try {
                habitRepository.deleteHabitLog(habitLog)
                loadHabitStatistics()
                
                _uiState.value = _uiState.value.copy(
                    lastAction = "Habit log deleted"
                )

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete habit log: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearLastAction() {
        _uiState.value = _uiState.value.copy(lastAction = null)
    }

    // Helper methods for UI
    fun getMoodEmoji(moodValue: Int): String {
        return when (moodValue) {
            1 -> "😢"
            2 -> "😕"
            3 -> "😐"
            4 -> "😊"
            5 -> "😄"
            else -> "😐"
        }
    }

    fun getSleepQualityText(quality: Int): String {
        return when (quality) {
            1 -> "Poor"
            2 -> "Fair"
            3 -> "Good"
            4 -> "Very Good"
            5 -> "Excellent"
            else -> "Unknown"
        }
    }

    fun getExerciseTypeEmoji(exerciseType: String): String {
        return when (exerciseType.lowercase()) {
            "walking" -> "🚶"
            "running" -> "🏃"
            "cycling" -> "🚴"
            "swimming" -> "🏊"
            "yoga" -> "🧘"
            "weightlifting" -> "🏋️"
            "dancing" -> "💃"
            "sports" -> "⚽"
            else -> "🏃"
        }
    }

    fun getStreakMessage(streak: Int): String {
        return when {
            streak == 0 -> "Start your streak today!"
            streak == 1 -> "Great start! 🌟"
            streak < 7 -> "Building momentum! 🚀"
            streak < 30 -> "Amazing streak! 🔥"
            else -> "Incredible dedication! 🏆"
        }
    }
}

data class HabitUiState(
    val isLoading: Boolean = false,
    val isLogging: Boolean = false,
    val habitStatistics: List<HabitStatistics> = emptyList(),
    val lastAction: String? = null,
    val error: String? = null
)

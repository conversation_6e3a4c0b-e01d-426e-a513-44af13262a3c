package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0006\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0004\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u00102\u0006\u0010\u0015\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0013H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u0018\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0019\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000f2\u0006\u0010\u001d\u001a\u00020\u001eH\'J$\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000f2\u0006\u0010 \u001a\u00020\u00132\u0006\u0010!\u001a\u00020\u0013H\'J\u001c\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000f2\u0006\u0010#\u001a\u00020\u001eH\'J\u0016\u0010$\u001a\u00020\u001a2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ&\u0010%\u001a\u00020\u00032\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010&\u001a\u00020\u00052\u0006\u0010\'\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010(J\u0016\u0010)\u001a\u00020\u00032\u0006\u0010\u0019\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ(\u0010*\u001a\u00020\u00032\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010+\u001a\u00020,2\b\u0010-\u001a\u0004\u0018\u00010\u001eH\u00a7@\u00a2\u0006\u0002\u0010.J\u0016\u0010/\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\n\u00a8\u00060"}, d2 = {"Lcom/focusflow/data/dao/WishlistItemDao;", "", "deletePurchasedItemsOlderThan", "", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteWishlistItem", "wishlistItem", "Lcom/focusflow/data/model/WishlistItem;", "(Lcom/focusflow/data/model/WishlistItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveDelayCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveDelayItems", "Lkotlinx/coroutines/flow/Flow;", "", "getAllActiveWishlistItems", "getAverageWishlistPrice", "", "getItemsWithExpiredDelay", "currentTime", "getPurchasedWishlistItems", "getTotalWishlistValue", "getWishlistItemById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWishlistItemsByCategory", "category", "", "getWishlistItemsByPriceRange", "minPrice", "maxPrice", "getWishlistItemsByPriority", "priority", "insertWishlistItem", "markAsPurchased", "purchaseDate", "actualPrice", "(JLkotlinx/datetime/LocalDateTime;DLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeDelay", "updateReflection", "stillWanted", "", "notes", "(JZLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWishlistItem", "app_release"})
@androidx.room.Dao
public abstract interface WishlistItemDao {
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE isPurchased = 0 ORDER BY addedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getAllActiveWishlistItems();
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE isPurchased = 1 ORDER BY purchasedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getPurchasedWishlistItems();
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE isDelayActive = 1 AND delayEndTime <= :currentTime")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getItemsWithExpiredDelay(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime currentTime, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.WishlistItem>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE isDelayActive = 1 ORDER BY delayEndTime ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getActiveDelayItems();
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE category = :category AND isPurchased = 0")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getWishlistItemsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category);
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE priority = :priority AND isPurchased = 0")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getWishlistItemsByPriority(@org.jetbrains.annotations.NotNull
    java.lang.String priority);
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE estimatedPrice BETWEEN :minPrice AND :maxPrice AND isPurchased = 0")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getWishlistItemsByPriceRange(double minPrice, double maxPrice);
    
    @androidx.room.Query(value = "SELECT * FROM wishlist_items WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getWishlistItemById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.WishlistItem> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM wishlist_items WHERE isDelayActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getActiveDelayCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(estimatedPrice) FROM wishlist_items WHERE isPurchased = 0")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageWishlistPrice(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(estimatedPrice) FROM wishlist_items WHERE isPurchased = 0")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalWishlistValue(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertWishlistItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem wishlistItem, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateWishlistItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem wishlistItem, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteWishlistItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem wishlistItem, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wishlist_items SET isPurchased = 1, purchasedDate = :purchaseDate, actualPrice = :actualPrice WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object markAsPurchased(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime purchaseDate, double actualPrice, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wishlist_items SET isDelayActive = 0 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object removeDelay(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE wishlist_items SET stillWanted = :stillWanted, reflectionNotes = :notes WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateReflection(long id, boolean stillWanted, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM wishlist_items WHERE isPurchased = 1 AND purchasedDate < :cutoffDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deletePurchasedItemsOlderThan(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
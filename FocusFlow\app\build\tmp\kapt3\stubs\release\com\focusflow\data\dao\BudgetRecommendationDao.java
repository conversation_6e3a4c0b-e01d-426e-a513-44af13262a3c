package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0014\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130\u0012H\'J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130\u0012H\'J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130\u00122\u0006\u0010\u0019\u001a\u00020\u0016H\'J\u0018\u0010\u001a\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u000e\u0010\u001c\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u0014\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130\u0012H\'J\u0018\u0010\u001f\u001a\u0004\u0018\u00010\u000f2\u0006\u0010 \u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010!J\u001c\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130\u00122\u0006\u0010#\u001a\u00020\u0005H\'J\u0014\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u00130\u0012H\'J\u0016\u0010%\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010&\u001a\u00020\u00032\u0006\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J(\u0010\'\u001a\u00020\u00032\u0006\u0010 \u001a\u00020\u00072\u0006\u0010(\u001a\u00020)2\b\u0010*\u001a\u0004\u0018\u00010\u0005H\u00a7@\u00a2\u0006\u0002\u0010+\u00a8\u0006,"}, d2 = {"Lcom/focusflow/data/dao/BudgetRecommendationDao;", "", "deactivateOldRecommendationsForCategory", "", "categoryName", "", "excludeId", "", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldProcessedRecommendations", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteRecommendation", "recommendation", "Lcom/focusflow/data/model/BudgetRecommendation;", "(Lcom/focusflow/data/model/BudgetRecommendation;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAcceptedRecommendations", "Lkotlinx/coroutines/flow/Flow;", "", "getAllActiveRecommendations", "getAverageAcceptedConfidenceScore", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHighConfidenceRecommendations", "minConfidence", "getLatestRecommendationForCategory", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPendingRecommendationCount", "", "getPendingRecommendations", "getRecommendationById", "id", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRecommendationsByReason", "reasonCode", "getRejectedRecommendations", "insertRecommendation", "updateRecommendation", "updateRecommendationResponse", "isAccepted", "", "feedback", "(JZLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
@androidx.room.Dao
public abstract interface BudgetRecommendationDao {
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE isActive = 1 ORDER BY generatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getAllActiveRecommendations();
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE categoryName = :categoryName AND isActive = 1 ORDER BY generatedDate DESC LIMIT 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getLatestRecommendationForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetRecommendation> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE isAccepted IS NULL AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getPendingRecommendations();
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE isAccepted = 1 ORDER BY generatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getAcceptedRecommendations();
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE isAccepted = 0 ORDER BY generatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getRejectedRecommendations();
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE reasonCode = :reasonCode AND isActive = 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getRecommendationsByReason(@org.jetbrains.annotations.NotNull
    java.lang.String reasonCode);
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE confidenceScore >= :minConfidence AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getHighConfidenceRecommendations(double minConfidence);
    
    @androidx.room.Query(value = "SELECT * FROM budget_recommendations WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getRecommendationById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetRecommendation> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM budget_recommendations WHERE isAccepted IS NULL AND isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPendingRecommendationCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(confidenceScore) FROM budget_recommendations WHERE isAccepted = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageAcceptedConfidenceScore(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertRecommendation(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetRecommendation recommendation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateRecommendation(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetRecommendation recommendation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteRecommendation(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetRecommendation recommendation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE budget_recommendations SET isAccepted = :isAccepted, userFeedback = :feedback WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateRecommendationResponse(long id, boolean isAccepted, @org.jetbrains.annotations.Nullable
    java.lang.String feedback, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE budget_recommendations SET isActive = 0 WHERE categoryName = :categoryName AND id != :excludeId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deactivateOldRecommendationsForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, long excludeId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM budget_recommendations WHERE generatedDate < :cutoffDate AND isAccepted IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteOldProcessedRecommendations(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
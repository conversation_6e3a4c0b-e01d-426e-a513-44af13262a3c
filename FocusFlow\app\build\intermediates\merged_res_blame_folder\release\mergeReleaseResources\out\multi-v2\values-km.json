{"logs": [{"outputFile": "com.focusflow.app-mergeReleaseResources-66:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c220a8d904d175691eb4177fafcca3d5\\transformed\\jetified-ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "36,37,39,41,42,53,54,55,56,57,58,59,60,62,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3514,3599,3789,3994,4092,5525,5609,5692,5777,5864,5929,5994,6074,6243,6420,6504,6570", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "3594,3674,3888,4087,4175,5604,5687,5772,5859,5924,5989,6069,6154,6314,6499,6565,6683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd99fe8d474b529eb55ef83da4ba319\\transformed\\biometric-1.1.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,266,385,517,651,810,940,1098,1202,1342,1481", "endColumns": "109,100,118,131,133,158,129,157,103,139,138,129", "endOffsets": "160,261,380,512,646,805,935,1093,1197,1337,1476,1606"}, "to": {"startLines": "38,40,43,44,45,46,47,48,49,50,51,52", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3679,3893,4180,4299,4431,4565,4724,4854,5012,5116,5256,5395", "endColumns": "109,100,118,131,133,158,129,157,103,139,138,129", "endOffsets": "3784,3989,4294,4426,4560,4719,4849,5007,5111,5251,5390,5520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3fc6d0d970e3c168245311f4f8e60786\\transformed\\core-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2793,2888,2991,3089,3189,3290,3402,6319", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "2883,2986,3084,3184,3285,3397,3509,6415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e741e5374256fd7ad708d21850e91de0\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,6159", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,6238"}}]}]}
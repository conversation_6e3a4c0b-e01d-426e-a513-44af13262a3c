package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0013\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0017\u001a\u00020\bH\u00c6\u0003J;\u0010\u0018\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u00062\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\u001d\u001a\u00020\bH\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006\u001e"}, d2 = {"Lcom/focusflow/service/RecommendationResult;", "", "recommendedAmount", "", "confidenceScore", "shouldRecommend", "", "reasonCode", "", "reasonDescription", "(DDZLjava/lang/String;Ljava/lang/String;)V", "getConfidenceScore", "()D", "getReasonCode", "()Ljava/lang/String;", "getReasonDescription", "getRecommendedAmount", "getShouldRecommend", "()Z", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_release"})
public final class RecommendationResult {
    private final double recommendedAmount = 0.0;
    private final double confidenceScore = 0.0;
    private final boolean shouldRecommend = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String reasonCode = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String reasonDescription = null;
    
    public RecommendationResult(double recommendedAmount, double confidenceScore, boolean shouldRecommend, @org.jetbrains.annotations.NotNull
    java.lang.String reasonCode, @org.jetbrains.annotations.NotNull
    java.lang.String reasonDescription) {
        super();
    }
    
    public final double getRecommendedAmount() {
        return 0.0;
    }
    
    public final double getConfidenceScore() {
        return 0.0;
    }
    
    public final boolean getShouldRecommend() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getReasonCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getReasonDescription() {
        return null;
    }
    
    public final double component1() {
        return 0.0;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.RecommendationResult copy(double recommendedAmount, double confidenceScore, boolean shouldRecommend, @org.jetbrains.annotations.NotNull
    java.lang.String reasonCode, @org.jetbrains.annotations.NotNull
    java.lang.String reasonDescription) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
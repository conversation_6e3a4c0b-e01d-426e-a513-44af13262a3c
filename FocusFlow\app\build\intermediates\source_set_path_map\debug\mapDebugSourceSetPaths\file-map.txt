com.focusflow.app-lifecycle-runtime-2.7.0-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\01798c09b07088d478275bfa0777bb9b\transformed\lifecycle-runtime-2.7.0\res
com.focusflow.app-jetified-lifecycle-service-2.7.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\04136c161a6bf2b7edcfa4d07fb848cb\transformed\jetified-lifecycle-service-2.7.0\res
com.focusflow.app-jetified-ui-test-manifest-1.5.4-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\07d809736e52631be78e4ec74270d0bd\transformed\jetified-ui-test-manifest-1.5.4\res
com.focusflow.app-jetified-emoji2-views-helper-1.4.0-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\0999956f64fcc36af7e766e022e1cfce\transformed\jetified-emoji2-views-helper-1.4.0\res
com.focusflow.app-jetified-savedstate-1.2.1-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\0fbe7faa7a4643e30220b72cfe6daf2c\transformed\jetified-savedstate-1.2.1\res
com.focusflow.app-jetified-lifecycle-runtime-ktx-2.7.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\140f20ce8c9810c5f23437d658405ab6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.focusflow.app-jetified-lifecycle-process-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\1bf3d8d92deaa88d8c5c5dc3cf014ac8\transformed\jetified-lifecycle-process-2.7.0\res
com.focusflow.app-sqlite-2.4.0-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\1f051f0f1c29b25edae7e2402225c54a\transformed\sqlite-2.4.0\res
com.focusflow.app-jetified-ui-util-release-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\21944fbce2e9c2c2aefa4eb759a36382\transformed\jetified-ui-util-release\res
com.focusflow.app-jetified-annotation-experimental-1.3.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\23e1fae7a2bb6f57e2755a4dce0bb18f\transformed\jetified-annotation-experimental-1.3.0\res
com.focusflow.app-jetified-coil-base-2.5.0-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\292c4259f358ccf66c799d67cdfb0ee4\transformed\jetified-coil-base-2.5.0\res
com.focusflow.app-jetified-ui-graphics-release-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\3052465d3c5e657b43c1c8b75b9cf1b7\transformed\jetified-ui-graphics-release\res
com.focusflow.app-core-1.12.0-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc6d0d970e3c168245311f4f8e60786\transformed\core-1.12.0\res
com.focusflow.app-jetified-animation-release-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\47e638ede125070f0ea9e60895b3fd3f\transformed\jetified-animation-release\res
com.focusflow.app-navigation-common-ktx-2.7.5-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\48c6ac263eb7cd40ac405a11eed98f1e\transformed\navigation-common-ktx-2.7.5\res
com.focusflow.app-navigation-runtime-2.7.5-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\4c606334f6f174b1055377e9b721ad21\transformed\navigation-runtime-2.7.5\res
com.focusflow.app-biometric-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd99fe8d474b529eb55ef83da4ba319\transformed\biometric-1.1.0\res
com.focusflow.app-jetified-security-crypto-1.1.0-alpha06-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\506460e1fd7e8de103a8225295d8d77d\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.focusflow.app-jetified-core-ktx-1.12.0-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\514c7ddea5e471d68e5b6c3fe30b81e2\transformed\jetified-core-ktx-1.12.0\res
com.focusflow.app-jetified-runtime-saveable-release-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\5326cd6544c35494861b8777db6f0df6\transformed\jetified-runtime-saveable-release\res
com.focusflow.app-jetified-appcompat-resources-1.6.1-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\569bdd61d1386dd307cc0529d8f2d9d7\transformed\jetified-appcompat-resources-1.6.1\res
com.focusflow.app-jetified-activity-compose-1.8.2-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\593a2fd83f6bce5cbd9ed8c845c08454\transformed\jetified-activity-compose-1.8.2\res
com.focusflow.app-jetified-emoji2-1.4.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\5986a4beb4ee10d2c93ece8e78c80058\transformed\jetified-emoji2-1.4.0\res
com.focusflow.app-jetified-lifecycle-viewmodel-ktx-2.7.0-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\59ad29456446c44060031b3eabffcdd4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.focusflow.app-jetified-lifecycle-viewmodel-compose-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\59e0fc3b88546f79ae687858bf416bf7\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\res
com.focusflow.app-core-runtime-2.2.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\5d2d79799f06b4cd6081e093663f9a2d\transformed\core-runtime-2.2.0\res
com.focusflow.app-navigation-common-2.7.5-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\675a96fbdef42db63b2797b3355c3b1a\transformed\navigation-common-2.7.5\res
com.focusflow.app-jetified-ui-tooling-preview-release-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\698205b51e44629bc58f3b811664e931\transformed\jetified-ui-tooling-preview-release\res
com.focusflow.app-room-runtime-2.6.1-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca5e963c591bc1388f745fb9c4fc9f8\transformed\room-runtime-2.6.1\res
com.focusflow.app-jetified-material-release-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\70b4c3fccd591969382b340dac7decea\transformed\jetified-material-release\res
com.focusflow.app-jetified-material-icons-core-release-30 C:\Users\<USER>\.gradle\caches\8.12\transforms\734713fe50ebb053c62db071dc0ebcec\transformed\jetified-material-icons-core-release\res
com.focusflow.app-fragment-1.5.1-31 C:\Users\<USER>\.gradle\caches\8.12\transforms\73d759f28d3aa098b1b9feba5e1a3eb1\transformed\fragment-1.5.1\res
com.focusflow.app-jetified-foundation-release-32 C:\Users\<USER>\.gradle\caches\8.12\transforms\76430c7192c9c339633d95c2ac8b69a0\transformed\jetified-foundation-release\res
com.focusflow.app-jetified-hilt-navigation-compose-1.1.0-33 C:\Users\<USER>\.gradle\caches\8.12\transforms\780bf32e32b8da2855eb049e7f5282bd\transformed\jetified-hilt-navigation-compose-1.1.0\res
com.focusflow.app-jetified-room-ktx-2.6.1-34 C:\Users\<USER>\.gradle\caches\8.12\transforms\79809c23b609fb6292cc3d5883d45d39\transformed\jetified-room-ktx-2.6.1\res
com.focusflow.app-lifecycle-livedata-core-2.7.0-35 C:\Users\<USER>\.gradle\caches\8.12\transforms\8985558dc73a605e71692a337d7168a9\transformed\lifecycle-livedata-core-2.7.0\res
com.focusflow.app-jetified-ui-geometry-release-36 C:\Users\<USER>\.gradle\caches\8.12\transforms\8b4e9726300c074c895b57ebf6b60171\transformed\jetified-ui-geometry-release\res
com.focusflow.app-jetified-ui-text-release-37 C:\Users\<USER>\.gradle\caches\8.12\transforms\9c26e4e06c0726149ff5b02aba7a677e\transformed\jetified-ui-text-release\res
com.focusflow.app-lifecycle-livedata-2.7.0-38 C:\Users\<USER>\.gradle\caches\8.12\transforms\9cbbb2949e26bc96d8e23202f621f402\transformed\lifecycle-livedata-2.7.0\res
com.focusflow.app-jetified-startup-runtime-1.1.1-39 C:\Users\<USER>\.gradle\caches\8.12\transforms\a1f408c62c79972772e7949740c22acd\transformed\jetified-startup-runtime-1.1.1\res
com.focusflow.app-jetified-lifecycle-runtime-compose-2.7.0-40 C:\Users\<USER>\.gradle\caches\8.12\transforms\a357e019bf83bb26c29f62da7a1721dd\transformed\jetified-lifecycle-runtime-compose-2.7.0\res
com.focusflow.app-jetified-material-ripple-release-41 C:\Users\<USER>\.gradle\caches\8.12\transforms\a478ab24747af1591b29f27f92bec421\transformed\jetified-material-ripple-release\res
com.focusflow.app-sqlite-framework-2.4.0-42 C:\Users\<USER>\.gradle\caches\8.12\transforms\aa0837277d01c03505066fb50215c744\transformed\sqlite-framework-2.4.0\res
com.focusflow.app-jetified-ui-tooling-release-43 C:\Users\<USER>\.gradle\caches\8.12\transforms\ae20ac95cb786f6216311627c814218a\transformed\jetified-ui-tooling-release\res
com.focusflow.app-jetified-savedstate-ktx-1.2.1-44 C:\Users\<USER>\.gradle\caches\8.12\transforms\b625fb61a69d3b2dd989a6b5d80a1932\transformed\jetified-savedstate-ktx-1.2.1\res
com.focusflow.app-jetified-navigation-compose-2.7.5-45 C:\Users\<USER>\.gradle\caches\8.12\transforms\b69f53eeb29de50b9b177ef377ed74de\transformed\jetified-navigation-compose-2.7.5\res
com.focusflow.app-jetified-lifecycle-livedata-core-ktx-2.7.0-46 C:\Users\<USER>\.gradle\caches\8.12\transforms\b718f38b2f609913a6e453ac75a3a31f\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.focusflow.app-jetified-activity-1.8.2-47 C:\Users\<USER>\.gradle\caches\8.12\transforms\b995b2484cdeb1078aced23e6165e39c\transformed\jetified-activity-1.8.2\res
com.focusflow.app-jetified-customview-poolingcontainer-1.0.0-48 C:\Users\<USER>\.gradle\caches\8.12\transforms\ba0e3f74786ecf5f066e0119516d3b4a\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.focusflow.app-work-runtime-ktx-2.9.0-49 C:\Users\<USER>\.gradle\caches\8.12\transforms\ba56b74c719e0e31a0dc7b4e6b0607fb\transformed\work-runtime-ktx-2.9.0\res
com.focusflow.app-jetified-ui-release-50 C:\Users\<USER>\.gradle\caches\8.12\transforms\c220a8d904d175691eb4177fafcca3d5\transformed\jetified-ui-release\res
com.focusflow.app-navigation-runtime-ktx-2.7.5-51 C:\Users\<USER>\.gradle\caches\8.12\transforms\ccba22d02dc258c6c0f8d1611562d950\transformed\navigation-runtime-ktx-2.7.5\res
com.focusflow.app-lifecycle-viewmodel-2.7.0-52 C:\Users\<USER>\.gradle\caches\8.12\transforms\cf7842f1497f9b21e4b6d3fdfe487f03\transformed\lifecycle-viewmodel-2.7.0\res
com.focusflow.app-jetified-hilt-work-1.1.0-53 C:\Users\<USER>\.gradle\caches\8.12\transforms\d0a0ebcba49bd6d238bab815dfbd971e\transformed\jetified-hilt-work-1.1.0\res
com.focusflow.app-jetified-foundation-layout-release-54 C:\Users\<USER>\.gradle\caches\8.12\transforms\de5cd214e2e72229407581aa08eb5fd0\transformed\jetified-foundation-layout-release\res
com.focusflow.app-jetified-ui-tooling-data-release-55 C:\Users\<USER>\.gradle\caches\8.12\transforms\e0569bdcd496785db137f57b8b73b12e\transformed\jetified-ui-tooling-data-release\res
com.focusflow.app-jetified-profileinstaller-1.3.1-56 C:\Users\<USER>\.gradle\caches\8.12\transforms\e351054fabdf48c66984a9f8ffa1b531\transformed\jetified-profileinstaller-1.3.1\res
com.focusflow.app-jetified-material-icons-extended-release-57 C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f90035d7b1d8996af465614d74ea1a\transformed\jetified-material-icons-extended-release\res
com.focusflow.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-58 C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a20e3490cc47945359a86ab8d35805\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.focusflow.app-appcompat-1.6.1-59 C:\Users\<USER>\.gradle\caches\8.12\transforms\e741e5374256fd7ad708d21850e91de0\transformed\appcompat-1.6.1\res
com.focusflow.app-jetified-animation-core-release-60 C:\Users\<USER>\.gradle\caches\8.12\transforms\edd5e3eddd77a51299f06e4312a2edb2\transformed\jetified-animation-core-release\res
com.focusflow.app-jetified-hilt-navigation-1.1.0-61 C:\Users\<USER>\.gradle\caches\8.12\transforms\f1f948b1a1d0a34c09ddb2c7cd11b43c\transformed\jetified-hilt-navigation-1.1.0\res
com.focusflow.app-jetified-runtime-release-62 C:\Users\<USER>\.gradle\caches\8.12\transforms\f4d0d9d5ce2b13307bc6aaede26e4b3d\transformed\jetified-runtime-release\res
com.focusflow.app-jetified-datetime-0.9.0-63 C:\Users\<USER>\.gradle\caches\8.12\transforms\f89bef0a086c25fcdbf63a0be151ff0a\transformed\jetified-datetime-0.9.0\res
com.focusflow.app-jetified-activity-ktx-1.8.2-64 C:\Users\<USER>\.gradle\caches\8.12\transforms\f940c3b44f4c8ee4384dac0432d861a1\transformed\jetified-activity-ktx-1.8.2\res
com.focusflow.app-work-runtime-2.9.0-65 C:\Users\<USER>\.gradle\caches\8.12\transforms\fae81b7f7d2930fd4a9367f6b49ae72a\transformed\work-runtime-2.9.0\res
com.focusflow.app-jetified-ui-unit-release-66 C:\Users\<USER>\.gradle\caches\8.12\transforms\ffedd7ff46ecd35ed809e74595e7da40\transformed\jetified-ui-unit-release\res
com.focusflow.app-resValues-67 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\generated\res\resValues\debug
com.focusflow.app-packageDebugResources-68 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.focusflow.app-packageDebugResources-69 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.focusflow.app-debug-70 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build\intermediates\merged_res\debug\mergeDebugResources
com.focusflow.app-debug-71 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\debug\res
com.focusflow.app-main-72 C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res

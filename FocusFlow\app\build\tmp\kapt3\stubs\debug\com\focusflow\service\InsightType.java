package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/focusflow/service/InsightType;", "", "(Ljava/lang/String;I)V", "SPENDING_PATTERN", "BUDGET_OPTIMIZATION", "DEBT_STRATEGY", "HABIT_IMPROVEMENT", "GOAL_PROGRESS", "app_debug"})
public enum InsightType {
    /*public static final*/ SPENDING_PATTERN /* = new SPENDING_PATTERN() */,
    /*public static final*/ BUDGET_OPTIMIZATION /* = new BUDGET_OPTIMIZATION() */,
    /*public static final*/ DEBT_STRATEGY /* = new DEBT_STRATEGY() */,
    /*public static final*/ HABIT_IMPROVEMENT /* = new HABIT_IMPROVEMENT() */,
    /*public static final*/ GOAL_PROGRESS /* = new GOAL_PROGRESS() */;
    
    InsightType() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.service.InsightType> getEntries() {
        return null;
    }
}
package com.focusflow.ui.responsive

import androidx.compose.foundation.layout.*
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Responsive Layout Manager for FocusFlow
 * Handles different screen sizes and orientations for optimal user experience
 */
object ResponsiveLayoutManager {
    
    /**
     * Screen size categories based on Android design guidelines
     */
    enum class ScreenSize {
        COMPACT,    // < 600dp width (phones in portrait)
        MEDIUM,     // 600-839dp width (tablets in portrait, phones in landscape)
        EXPANDED    // >= 840dp width (tablets in landscape, desktops)
    }
    
    /**
     * Screen orientation
     */
    enum class Orientation {
        PORTRAIT, LANDSCAPE
    }
    
    /**
     * Device type classification
     */
    enum class DeviceType {
        PHONE, TABLET, DESKTOP
    }
    
    /**
     * Layout configuration for responsive design
     */
    data class LayoutConfig(
        val screenSize: ScreenSize,
        val orientation: Orientation,
        val deviceType: DeviceType,
        val screenWidth: Dp,
        val screenHeight: Dp,
        val isTablet: Boolean,
        val useSideNavigation: Boolean,
        val contentPadding: PaddingValues,
        val cardSpacing: Dp,
        val maxContentWidth: Dp
    )
    
    /**
     * Get current layout configuration
     */
    @Composable
    fun getLayoutConfig(): LayoutConfig {
        val configuration = LocalConfiguration.current
        val density = LocalDensity.current
        
        val screenWidth = with(density) { configuration.screenWidthDp.dp }
        val screenHeight = with(density) { configuration.screenHeightDp.dp }
        
        val screenSize = when {
            screenWidth < 600.dp -> ScreenSize.COMPACT
            screenWidth < 840.dp -> ScreenSize.MEDIUM
            else -> ScreenSize.EXPANDED
        }
        
        val orientation = if (configuration.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
            Orientation.LANDSCAPE
        } else {
            Orientation.PORTRAIT
        }
        
        val isTablet = screenWidth >= 600.dp
        val deviceType = when {
            screenWidth >= 1200.dp -> DeviceType.DESKTOP
            isTablet -> DeviceType.TABLET
            else -> DeviceType.PHONE
        }
        
        val useSideNavigation = screenSize == ScreenSize.EXPANDED || 
                               (screenSize == ScreenSize.MEDIUM && orientation == Orientation.LANDSCAPE)
        
        val contentPadding = when (screenSize) {
            ScreenSize.COMPACT -> PaddingValues(16.dp)
            ScreenSize.MEDIUM -> PaddingValues(24.dp)
            ScreenSize.EXPANDED -> PaddingValues(32.dp)
        }
        
        val cardSpacing = when (screenSize) {
            ScreenSize.COMPACT -> 12.dp
            ScreenSize.MEDIUM -> 16.dp
            ScreenSize.EXPANDED -> 20.dp
        }
        
        val maxContentWidth = when (screenSize) {
            ScreenSize.COMPACT -> screenWidth
            ScreenSize.MEDIUM -> 600.dp
            ScreenSize.EXPANDED -> 800.dp
        }
        
        return LayoutConfig(
            screenSize = screenSize,
            orientation = orientation,
            deviceType = deviceType,
            screenWidth = screenWidth,
            screenHeight = screenHeight,
            isTablet = isTablet,
            useSideNavigation = useSideNavigation,
            contentPadding = contentPadding,
            cardSpacing = cardSpacing,
            maxContentWidth = maxContentWidth
        )
    }
    
    /**
     * Responsive column count for grids
     */
    @Composable
    fun getColumnCount(): Int {
        val config = getLayoutConfig()
        return when (config.screenSize) {
            ScreenSize.COMPACT -> if (config.orientation == Orientation.PORTRAIT) 1 else 2
            ScreenSize.MEDIUM -> if (config.orientation == Orientation.PORTRAIT) 2 else 3
            ScreenSize.EXPANDED -> if (config.orientation == Orientation.PORTRAIT) 3 else 4
        }
    }
    
    /**
     * Responsive navigation layout
     */
    @Composable
    fun shouldUseBottomNavigation(): Boolean {
        val config = getLayoutConfig()
        return !config.useSideNavigation
    }
    
    /**
     * Responsive content width
     */
    @Composable
    fun getContentWidth(): Dp {
        val config = getLayoutConfig()
        return minOf(config.screenWidth, config.maxContentWidth)
    }
    
    /**
     * Responsive spacing values
     */
    object Spacing {
        @Composable
        fun small(): Dp {
            val config = getLayoutConfig()
            return when (config.screenSize) {
                ScreenSize.COMPACT -> 4.dp
                ScreenSize.MEDIUM -> 6.dp
                ScreenSize.EXPANDED -> 8.dp
            }
        }
        
        @Composable
        fun medium(): Dp {
            val config = getLayoutConfig()
            return when (config.screenSize) {
                ScreenSize.COMPACT -> 8.dp
                ScreenSize.MEDIUM -> 12.dp
                ScreenSize.EXPANDED -> 16.dp
            }
        }
        
        @Composable
        fun large(): Dp {
            val config = getLayoutConfig()
            return when (config.screenSize) {
                ScreenSize.COMPACT -> 16.dp
                ScreenSize.MEDIUM -> 24.dp
                ScreenSize.EXPANDED -> 32.dp
            }
        }
        
        @Composable
        fun extraLarge(): Dp {
            val config = getLayoutConfig()
            return when (config.screenSize) {
                ScreenSize.COMPACT -> 24.dp
                ScreenSize.MEDIUM -> 32.dp
                ScreenSize.EXPANDED -> 48.dp
            }
        }
    }
    
    /**
     * Responsive typography scaling
     */
    object Typography {
        @Composable
        fun getScaleFactor(): Float {
            val config = getLayoutConfig()
            return when (config.screenSize) {
                ScreenSize.COMPACT -> 1.0f
                ScreenSize.MEDIUM -> 1.1f
                ScreenSize.EXPANDED -> 1.2f
            }
        }
    }
    
    /**
     * Responsive card layout
     */
    @Composable
    fun getCardModifier(): Modifier {
        val config = getLayoutConfig()
        return Modifier
            .fillMaxWidth()
            .padding(config.cardSpacing / 2)
    }
    
    /**
     * Responsive button sizing
     */
    @Composable
    fun getButtonHeight(): Dp {
        val config = getLayoutConfig()
        return when (config.screenSize) {
            ScreenSize.COMPACT -> 48.dp
            ScreenSize.MEDIUM -> 52.dp
            ScreenSize.EXPANDED -> 56.dp
        }
    }
    
    /**
     * Responsive icon sizing
     */
    @Composable
    fun getIconSize(): Dp {
        val config = getLayoutConfig()
        return when (config.screenSize) {
            ScreenSize.COMPACT -> 24.dp
            ScreenSize.MEDIUM -> 28.dp
            ScreenSize.EXPANDED -> 32.dp
        }
    }
    
    /**
     * Check if device supports multi-pane layout
     */
    @Composable
    fun supportsMultiPane(): Boolean {
        val config = getLayoutConfig()
        return config.screenSize == ScreenSize.EXPANDED || 
               (config.screenSize == ScreenSize.MEDIUM && config.orientation == Orientation.LANDSCAPE)
    }
    
    /**
     * Get optimal content arrangement for current screen
     */
    @Composable
    fun getContentArrangement(): Arrangement.HorizontalOrVertical {
        val config = getLayoutConfig()
        return when (config.screenSize) {
            ScreenSize.COMPACT -> Arrangement.spacedBy(config.cardSpacing)
            ScreenSize.MEDIUM -> Arrangement.spacedBy(config.cardSpacing * 1.2f)
            ScreenSize.EXPANDED -> Arrangement.spacedBy(config.cardSpacing * 1.5f)
        }
    }
    
    /**
     * Responsive dialog sizing
     */
    @Composable
    fun getDialogWidth(): Dp {
        val config = getLayoutConfig()
        return when (config.screenSize) {
            ScreenSize.COMPACT -> config.screenWidth * 0.9f
            ScreenSize.MEDIUM -> minOf(config.screenWidth * 0.8f, 400.dp)
            ScreenSize.EXPANDED -> minOf(config.screenWidth * 0.6f, 500.dp)
        }
    }
    
    /**
     * Responsive bottom sheet sizing
     */
    @Composable
    fun getBottomSheetHeight(): Dp {
        val config = getLayoutConfig()
        return when (config.screenSize) {
            ScreenSize.COMPACT -> config.screenHeight * 0.7f
            ScreenSize.MEDIUM -> config.screenHeight * 0.6f
            ScreenSize.EXPANDED -> config.screenHeight * 0.5f
        }
    }
}

/**
 * Composable wrapper for responsive layouts
 */
@Composable
fun ResponsiveLayout(
    modifier: Modifier = Modifier,
    content: @Composable (ResponsiveLayoutManager.LayoutConfig) -> Unit
) {
    val layoutConfig = ResponsiveLayoutManager.getLayoutConfig()
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(layoutConfig.contentPadding)
    ) {
        content(layoutConfig)
    }
}

/**
 * Responsive grid layout
 */
@Composable
fun ResponsiveGrid(
    modifier: Modifier = Modifier,
    content: @Composable (columns: Int, spacing: Dp) -> Unit
) {
    val columns = ResponsiveLayoutManager.getColumnCount()
    val spacing = ResponsiveLayoutManager.Spacing.medium()
    
    content(columns, spacing)
}

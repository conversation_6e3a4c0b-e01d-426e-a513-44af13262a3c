package com.focusflow.data.repository

import com.focusflow.data.dao.HabitLogDao
import com.focusflow.data.model.HabitLog
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HabitRepository @Inject constructor(
    private val habitLogDao: HabitLogDao
) {
    
    fun getAllHabitTypes(): Flow<List<String>> = habitLogDao.getAllHabitTypes()
    
    fun getHabitLogsByType(habitType: String): Flow<List<HabitLog>> = 
        habitLogDao.getHabitLogsByType(habitType)
    
    fun getHabitLogsForDate(date: LocalDate): Flow<List<HabitLog>> = 
        habitLogDao.getHabitLogsForDate(date)
    
    fun getHabitLogsByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<HabitLog>> = 
        habitLogDao.getHabitLogsByDateRange(startDate, endDate)
    
    fun getHabitLogsByTypeAndDateRange(
        habitType: String, 
        startDate: LocalDate, 
        endDate: LocalDate
    ): Flow<List<HabitLog>> = 
        habitLogDao.getHabitLogsByTypeAndDateRange(habitType, startDate, endDate)
    
    suspend fun insertHabitLog(habitLog: HabitLog): Long = 
        habitLogDao.insertHabitLog(habitLog)
    
    suspend fun updateHabitLog(habitLog: HabitLog) = 
        habitLogDao.updateHabitLog(habitLog)
    
    suspend fun deleteHabitLog(habitLog: HabitLog) = 
        habitLogDao.deleteHabitLog(habitLog)
    
    // Convenience methods for specific habit types
    suspend fun logMood(date: LocalDate, moodValue: Int, notes: String? = null): Long {
        val habitLog = HabitLog(
            habitType = "mood",
            date = date,
            value = moodValue.toString(),
            notes = notes
        )
        return insertHabitLog(habitLog)
    }
    
    suspend fun logSleep(date: LocalDate, hoursSlept: Double, quality: Int, notes: String? = null): Long {
        val habitLog = HabitLog(
            habitType = "sleep",
            date = date,
            value = "$hoursSlept,$quality", // Format: "hours,quality"
            notes = notes
        )
        return insertHabitLog(habitLog)
    }
    
    suspend fun logExercise(date: LocalDate, exerciseType: String, duration: Int, notes: String? = null): Long {
        val habitLog = HabitLog(
            habitType = "exercise",
            date = date,
            value = "$exerciseType,$duration", // Format: "type,duration"
            notes = notes
        )
        return insertHabitLog(habitLog)
    }
    
    suspend fun logMedication(date: LocalDate, taken: Boolean, time: String? = null, notes: String? = null): Long {
        val habitLog = HabitLog(
            habitType = "medication",
            date = date,
            value = if (taken) "taken${time?.let { ",$it" } ?: ""}" else "missed",
            notes = notes
        )
        return insertHabitLog(habitLog)
    }
    
    // Streak calculation methods
    suspend fun calculateCurrentStreak(habitType: String): Int {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        var streak = 0
        var currentDate = today
        
        // Count backwards from today until we find a missing day
        while (true) {
            val logs = habitLogDao.getHabitLogsByTypeAndDateRange(habitType, currentDate, currentDate)
            if (logs.first().isEmpty()) {
                break
            }
            streak++
            currentDate = currentDate.minus(1, DateTimeUnit.DAY)
            
            // Prevent infinite loop - max 365 days
            if (streak >= 365) break
        }
        
        return streak
    }
    
    suspend fun calculateLongestStreak(habitType: String): Int {
        val thirtyDaysAgo = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
            .minus(30, DateTimeUnit.DAY)
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        
        val logs = habitLogDao.getHabitLogsByTypeAndDateRange(habitType, thirtyDaysAgo, today).first()
        
        if (logs.isEmpty()) return 0
        
        var longestStreak = 0
        var currentStreak = 0
        var previousDate: LocalDate? = null
        
        logs.sortedBy { it.date }.forEach { log ->
            if (previousDate == null || log.date == previousDate!!.plus(1, DateTimeUnit.DAY)) {
                currentStreak++
                longestStreak = maxOf(longestStreak, currentStreak)
            } else {
                currentStreak = 1
            }
            previousDate = log.date
        }
        
        return longestStreak
    }
    
    // Analytics methods
    fun getWeeklyHabitSummary(habitType: String): Flow<List<HabitLog>> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val weekAgo = today.minus(7, DateTimeUnit.DAY)
        return getHabitLogsByTypeAndDateRange(habitType, weekAgo, today)
    }
    
    fun getMonthlyHabitSummary(habitType: String): Flow<List<HabitLog>> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val monthAgo = today.minus(30, DateTimeUnit.DAY)
        return getHabitLogsByTypeAndDateRange(habitType, monthAgo, today)
    }
    
    // Get habit completion rate for a period
    suspend fun getHabitCompletionRate(habitType: String, days: Int): Double {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val startDate = today.minus(days, DateTimeUnit.DAY)
        
        val logs = habitLogDao.getHabitLogsByTypeAndDateRange(habitType, startDate, today).first()
        return if (days > 0) logs.size.toDouble() / days else 0.0
    }
    
    // Get mood trends
    fun getMoodTrends(): Flow<List<HabitLog>> = getHabitLogsByType("mood")
    
    // Get sleep patterns
    fun getSleepPatterns(): Flow<List<HabitLog>> = getHabitLogsByType("sleep")
    
    // Get exercise history
    fun getExerciseHistory(): Flow<List<HabitLog>> = getHabitLogsByType("exercise")
    
    // Get medication adherence
    fun getMedicationAdherence(): Flow<List<HabitLog>> = getHabitLogsByType("medication")
    
    // Helper method to parse sleep data
    fun parseSleepData(value: String): Pair<Double, Int> {
        val parts = value.split(",")
        return if (parts.size >= 2) {
            Pair(parts[0].toDoubleOrNull() ?: 0.0, parts[1].toIntOrNull() ?: 0)
        } else {
            Pair(0.0, 0)
        }
    }
    
    // Helper method to parse exercise data
    fun parseExerciseData(value: String): Pair<String, Int> {
        val parts = value.split(",")
        return if (parts.size >= 2) {
            Pair(parts[0], parts[1].toIntOrNull() ?: 0)
        } else {
            Pair("", 0)
        }
    }
    
    // Helper method to check if medication was taken
    fun wasMedicationTaken(value: String): Boolean {
        return value.startsWith("taken")
    }
    
    // Get today's habits status
    fun getTodaysHabits(): Flow<Map<String, HabitLog?>> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        return getHabitLogsForDate(today).map { logs ->
            val habitTypes = listOf("mood", "sleep", "exercise", "medication")
            habitTypes.associateWith { habitType ->
                logs.find { it.habitType == habitType }
            }
        }
    }
    
    // Get habit statistics
    suspend fun getHabitStatistics(habitType: String): HabitStatistics {
        val currentStreak = calculateCurrentStreak(habitType)
        val longestStreak = calculateLongestStreak(habitType)
        val weeklyRate = getHabitCompletionRate(habitType, 7)
        val monthlyRate = getHabitCompletionRate(habitType, 30)
        
        return HabitStatistics(
            habitType = habitType,
            currentStreak = currentStreak,
            longestStreak = longestStreak,
            weeklyCompletionRate = weeklyRate,
            monthlyCompletionRate = monthlyRate
        )
    }
}

data class HabitStatistics(
    val habitType: String,
    val currentStreak: Int,
    val longestStreak: Int,
    val weeklyCompletionRate: Double,
    val monthlyCompletionRate: Double
)

package com.focusflow.security;

/**
 * Comprehensive Security Manager for FocusFlow
 * Handles encryption, biometric authentication, and data protection
 * Designed specifically for financial app security requirements
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0007\u0018\u0000 62\u00020\u0001:\u00016B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J>\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00182\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00160\u001a2\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u00160\u001c2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00160\u001aJ\u000e\u0010\u001f\u001a\u00020\u0016H\u0086@\u00a2\u0006\u0002\u0010 J\u0016\u0010!\u001a\u00020\u001d2\u0006\u0010\"\u001a\u00020#H\u0086@\u00a2\u0006\u0002\u0010$J\u0016\u0010%\u001a\u00020#2\u0006\u0010&\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\'J\b\u0010(\u001a\u00020\u0016H\u0002J\b\u0010)\u001a\u00020\u0016H\u0002J\u0010\u0010*\u001a\u00020\u001d2\b\b\u0002\u0010+\u001a\u00020,J\u0018\u0010-\u001a\u0004\u0018\u00010\u001d2\u0006\u0010.\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\'J\u000e\u0010/\u001a\u000200H\u0086@\u00a2\u0006\u0002\u0010 J\u0006\u00101\u001a\u000202J\u001e\u00103\u001a\u00020\u00162\u0006\u0010.\u001a\u00020\u001d2\u0006\u00104\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u00105R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u001b\u0010\u000b\u001a\u00020\f8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000f\u0010\n\u001a\u0004\b\r\u0010\u000eR\u001b\u0010\u0010\u001a\u00020\u00118BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0014\u0010\n\u001a\u0004\b\u0012\u0010\u0013\u00a8\u00067"}, d2 = {"Lcom/focusflow/security/SecurityManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "encryptedPrefs", "Landroid/content/SharedPreferences;", "getEncryptedPrefs", "()Landroid/content/SharedPreferences;", "encryptedPrefs$delegate", "Lkotlin/Lazy;", "keyStore", "Ljava/security/KeyStore;", "getKeyStore", "()Ljava/security/KeyStore;", "keyStore$delegate", "masterKey", "Landroidx/security/crypto/MasterKey;", "getMasterKey", "()Landroidx/security/crypto/MasterKey;", "masterKey$delegate", "authenticateWithBiometrics", "", "activity", "Landroidx/fragment/app/FragmentActivity;", "onSuccess", "Lkotlin/Function0;", "onError", "Lkotlin/Function1;", "", "onFailed", "clearSecurityData", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "decryptData", "encryptedData", "Lcom/focusflow/security/EncryptedData;", "(Lcom/focusflow/security/EncryptedData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "encryptData", "plaintext", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateBiometricKey", "generateMasterKey", "generateSecureToken", "length", "", "getSecureData", "key", "initializeSecurity", "Lcom/focusflow/security/SecurityInitResult;", "isBiometricSupported", "", "storeSecureData", "value", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class SecurityManager {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String KEYSTORE_ALIAS = "FocusFlowMasterKey";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String ENCRYPTED_PREFS_NAME = "focus_flow_encrypted_prefs";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String BIOMETRIC_KEY_ALIAS = "FocusFlowBiometricKey";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy keyStore$delegate = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy masterKey$delegate = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy encryptedPrefs$delegate = null;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.security.SecurityManager.Companion Companion = null;
    
    @javax.inject.Inject
    public SecurityManager(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super();
    }
    
    private final java.security.KeyStore getKeyStore() {
        return null;
    }
    
    private final androidx.security.crypto.MasterKey getMasterKey() {
        return null;
    }
    
    private final android.content.SharedPreferences getEncryptedPrefs() {
        return null;
    }
    
    /**
     * Initialize security system
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object initializeSecurity(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.security.SecurityInitResult> $completion) {
        return null;
    }
    
    /**
     * Encrypt sensitive data using AES-GCM
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object encryptData(@org.jetbrains.annotations.NotNull
    java.lang.String plaintext, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.security.EncryptedData> $completion) {
        return null;
    }
    
    /**
     * Decrypt sensitive data
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object decryptData(@org.jetbrains.annotations.NotNull
    com.focusflow.security.EncryptedData encryptedData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Store sensitive data in encrypted preferences
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object storeSecureData(@org.jetbrains.annotations.NotNull
    java.lang.String key, @org.jetbrains.annotations.NotNull
    java.lang.String value, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Retrieve sensitive data from encrypted preferences
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getSecureData(@org.jetbrains.annotations.NotNull
    java.lang.String key, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    /**
     * Check if biometric authentication is supported
     */
    public final boolean isBiometricSupported() {
        return false;
    }
    
    /**
     * Authenticate user with biometrics
     */
    public final void authenticateWithBiometrics(@org.jetbrains.annotations.NotNull
    androidx.fragment.app.FragmentActivity activity, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSuccess, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onFailed) {
    }
    
    /**
     * Generate master encryption key
     */
    private final void generateMasterKey() {
    }
    
    /**
     * Generate biometric-protected key
     */
    private final void generateBiometricKey() {
    }
    
    /**
     * Clear all security data (for logout/reset)
     */
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object clearSecurityData(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Generate secure random token
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String generateSecureToken(int length) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/focusflow/security/SecurityManager$Companion;", "", "()V", "BIOMETRIC_KEY_ALIAS", "", "ENCRYPTED_PREFS_NAME", "GCM_IV_LENGTH", "", "GCM_TAG_LENGTH", "KEYSTORE_ALIAS", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
package com.focusflow.ui.responsive;

/**
 * Responsive Layout Manager for FocusFlow
 * Handles different screen sizes and orientations for optimal user experience
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0006\u001a\u001b\u001c\u001d\u001e\u001fB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0015\u0010\u0003\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u0015\u0010\u0007\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\b\u0010\u0006J\b\u0010\t\u001a\u00020\nH\u0007J\b\u0010\u000b\u001a\u00020\fH\u0007J\b\u0010\r\u001a\u00020\u000eH\u0007J\u0015\u0010\u000f\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0006J\u0015\u0010\u0011\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u0006J\u0015\u0010\u0013\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0006J\b\u0010\u0015\u001a\u00020\u0016H\u0007J\b\u0010\u0017\u001a\u00020\u0018H\u0007J\b\u0010\u0019\u001a\u00020\u0018H\u0007\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager;", "", "()V", "getBottomSheetHeight", "Landroidx/compose/ui/unit/Dp;", "getBottomSheetHeight-D9Ej5fM", "()F", "getButtonHeight", "getButtonHeight-D9Ej5fM", "getCardModifier", "Landroidx/compose/ui/Modifier;", "getColumnCount", "", "getContentArrangement", "Landroidx/compose/foundation/layout/Arrangement$HorizontalOrVertical;", "getContentWidth", "getContentWidth-D9Ej5fM", "getDialogWidth", "getDialogWidth-D9Ej5fM", "getIconSize", "getIconSize-D9Ej5fM", "getLayoutConfig", "Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$LayoutConfig;", "shouldUseBottomNavigation", "", "supportsMultiPane", "DeviceType", "LayoutConfig", "Orientation", "ScreenSize", "Spacing", "Typography", "app_debug"})
public final class ResponsiveLayoutManager {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.ui.responsive.ResponsiveLayoutManager INSTANCE = null;
    
    private ResponsiveLayoutManager() {
        super();
    }
    
    /**
     * Get current layout configuration
     */
    @androidx.compose.runtime.Composable
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.responsive.ResponsiveLayoutManager.LayoutConfig getLayoutConfig() {
        return null;
    }
    
    /**
     * Responsive column count for grids
     */
    @androidx.compose.runtime.Composable
    public final int getColumnCount() {
        return 0;
    }
    
    /**
     * Responsive navigation layout
     */
    @androidx.compose.runtime.Composable
    public final boolean shouldUseBottomNavigation() {
        return false;
    }
    
    /**
     * Responsive card layout
     */
    @androidx.compose.runtime.Composable
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.Modifier getCardModifier() {
        return null;
    }
    
    /**
     * Check if device supports multi-pane layout
     */
    @androidx.compose.runtime.Composable
    public final boolean supportsMultiPane() {
        return false;
    }
    
    /**
     * Get optimal content arrangement for current screen
     */
    @androidx.compose.runtime.Composable
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.foundation.layout.Arrangement.HorizontalOrVertical getContentArrangement() {
        return null;
    }
    
    /**
     * Device type classification
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$DeviceType;", "", "(Ljava/lang/String;I)V", "PHONE", "TABLET", "DESKTOP", "app_debug"})
    public static enum DeviceType {
        /*public static final*/ PHONE /* = new PHONE() */,
        /*public static final*/ TABLET /* = new TABLET() */,
        /*public static final*/ DESKTOP /* = new DESKTOP() */;
        
        DeviceType() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.responsive.ResponsiveLayoutManager.DeviceType> getEntries() {
            return null;
        }
    }
    
    /**
     * Layout configuration for responsive design
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\'\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\t\u0012\u0006\u0010\u0011\u001a\u00020\t\u00a2\u0006\u0002\u0010\u0012J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\u0016\u0010$\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b%\u0010\u0014J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0007H\u00c6\u0003J\u0016\u0010(\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b)\u0010\u0014J\u0016\u0010*\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b+\u0010\u0014J\t\u0010,\u001a\u00020\fH\u00c6\u0003J\t\u0010-\u001a\u00020\fH\u00c6\u0003J\t\u0010.\u001a\u00020\u000fH\u00c6\u0003J\u0016\u0010/\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b0\u0010\u0014Jw\u00101\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\t2\b\b\u0002\u0010\u0011\u001a\u00020\tH\u00c6\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b2\u00103J\u0013\u00104\u001a\u00020\f2\b\u00105\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00106\u001a\u000207H\u00d6\u0001J\t\u00108\u001a\u000209H\u00d6\u0001R\u0019\u0010\u0010\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u001aR\u0019\u0010\u0011\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u001b\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0019\u0010\n\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u001e\u0010\u0014R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0019\u0010\b\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b!\u0010\u0014R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001a\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006:"}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$LayoutConfig;", "", "screenSize", "Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$ScreenSize;", "orientation", "Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Orientation;", "deviceType", "Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$DeviceType;", "screenWidth", "Landroidx/compose/ui/unit/Dp;", "screenHeight", "isTablet", "", "useSideNavigation", "contentPadding", "Landroidx/compose/foundation/layout/PaddingValues;", "cardSpacing", "maxContentWidth", "(Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$ScreenSize;Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Orientation;Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$DeviceType;FFZZLandroidx/compose/foundation/layout/PaddingValues;FFLkotlin/jvm/internal/DefaultConstructorMarker;)V", "getCardSpacing-D9Ej5fM", "()F", "F", "getContentPadding", "()Landroidx/compose/foundation/layout/PaddingValues;", "getDeviceType", "()Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$DeviceType;", "()Z", "getMaxContentWidth-D9Ej5fM", "getOrientation", "()Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Orientation;", "getScreenHeight-D9Ej5fM", "getScreenSize", "()Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$ScreenSize;", "getScreenWidth-D9Ej5fM", "getUseSideNavigation", "component1", "component10", "component10-D9Ej5fM", "component2", "component3", "component4", "component4-D9Ej5fM", "component5", "component5-D9Ej5fM", "component6", "component7", "component8", "component9", "component9-D9Ej5fM", "copy", "copy-9iJo7To", "(Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$ScreenSize;Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Orientation;Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$DeviceType;FFZZLandroidx/compose/foundation/layout/PaddingValues;FF)Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$LayoutConfig;", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
    public static final class LayoutConfig {
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.responsive.ResponsiveLayoutManager.ScreenSize screenSize = null;
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.responsive.ResponsiveLayoutManager.Orientation orientation = null;
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.responsive.ResponsiveLayoutManager.DeviceType deviceType = null;
        private final float screenWidth = 0.0F;
        private final float screenHeight = 0.0F;
        private final boolean isTablet = false;
        private final boolean useSideNavigation = false;
        @org.jetbrains.annotations.NotNull
        private final androidx.compose.foundation.layout.PaddingValues contentPadding = null;
        private final float cardSpacing = 0.0F;
        private final float maxContentWidth = 0.0F;
        
        private LayoutConfig(com.focusflow.ui.responsive.ResponsiveLayoutManager.ScreenSize screenSize, com.focusflow.ui.responsive.ResponsiveLayoutManager.Orientation orientation, com.focusflow.ui.responsive.ResponsiveLayoutManager.DeviceType deviceType, float screenWidth, float screenHeight, boolean isTablet, boolean useSideNavigation, androidx.compose.foundation.layout.PaddingValues contentPadding, float cardSpacing, float maxContentWidth) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.responsive.ResponsiveLayoutManager.ScreenSize getScreenSize() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.responsive.ResponsiveLayoutManager.Orientation getOrientation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.responsive.ResponsiveLayoutManager.DeviceType getDeviceType() {
            return null;
        }
        
        public final boolean isTablet() {
            return false;
        }
        
        public final boolean getUseSideNavigation() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final androidx.compose.foundation.layout.PaddingValues getContentPadding() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.responsive.ResponsiveLayoutManager.ScreenSize component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.responsive.ResponsiveLayoutManager.Orientation component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.responsive.ResponsiveLayoutManager.DeviceType component3() {
            return null;
        }
        
        public final boolean component6() {
            return false;
        }
        
        public final boolean component7() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final androidx.compose.foundation.layout.PaddingValues component8() {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    /**
     * Screen orientation
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Orientation;", "", "(Ljava/lang/String;I)V", "PORTRAIT", "LANDSCAPE", "app_debug"})
    public static enum Orientation {
        /*public static final*/ PORTRAIT /* = new PORTRAIT() */,
        /*public static final*/ LANDSCAPE /* = new LANDSCAPE() */;
        
        Orientation() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.responsive.ResponsiveLayoutManager.Orientation> getEntries() {
            return null;
        }
    }
    
    /**
     * Screen size categories based on Android design guidelines
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0005\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$ScreenSize;", "", "(Ljava/lang/String;I)V", "COMPACT", "MEDIUM", "EXPANDED", "app_debug"})
    public static enum ScreenSize {
        /*public static final*/ COMPACT /* = new COMPACT() */,
        /*public static final*/ MEDIUM /* = new MEDIUM() */,
        /*public static final*/ EXPANDED /* = new EXPANDED() */;
        
        ScreenSize() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.responsive.ResponsiveLayoutManager.ScreenSize> getEntries() {
            return null;
        }
    }
    
    /**
     * Responsive spacing values
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0015\u0010\u0003\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0005\u0010\u0006J\u0015\u0010\u0007\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\b\u0010\u0006J\u0015\u0010\t\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\u0006J\u0015\u0010\u000b\u001a\u00020\u0004H\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\f\u0010\u0006\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\r"}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Spacing;", "", "()V", "extraLarge", "Landroidx/compose/ui/unit/Dp;", "extraLarge-D9Ej5fM", "()F", "large", "large-D9Ej5fM", "medium", "medium-D9Ej5fM", "small", "small-D9Ej5fM", "app_debug"})
    public static final class Spacing {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.ui.responsive.ResponsiveLayoutManager.Spacing INSTANCE = null;
        
        private Spacing() {
            super();
        }
    }
    
    /**
     * Responsive typography scaling
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007\u00a8\u0006\u0005"}, d2 = {"Lcom/focusflow/ui/responsive/ResponsiveLayoutManager$Typography;", "", "()V", "getScaleFactor", "", "app_debug"})
    public static final class Typography {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.ui.responsive.ResponsiveLayoutManager.Typography INSTANCE = null;
        
        private Typography() {
            super();
        }
        
        @androidx.compose.runtime.Composable
        public final float getScaleFactor() {
            return 0.0F;
        }
    }
}
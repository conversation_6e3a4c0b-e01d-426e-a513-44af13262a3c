package com.focusflow.data.repository;

import com.focusflow.data.dao.AIInteractionDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AIInteractionRepository_Factory implements Factory<AIInteractionRepository> {
  private final Provider<AIInteractionDao> aiInteractionDaoProvider;

  public AIInteractionRepository_Factory(Provider<AIInteractionDao> aiInteractionDaoProvider) {
    this.aiInteractionDaoProvider = aiInteractionDaoProvider;
  }

  @Override
  public AIInteractionRepository get() {
    return newInstance(aiInteractionDaoProvider.get());
  }

  public static AIInteractionRepository_Factory create(
      Provider<AIInteractionDao> aiInteractionDaoProvider) {
    return new AIInteractionRepository_Factory(aiInteractionDaoProvider);
  }

  public static AIInteractionRepository newInstance(AIInteractionDao aiInteractionDao) {
    return new AIInteractionRepository(aiInteractionDao);
  }
}

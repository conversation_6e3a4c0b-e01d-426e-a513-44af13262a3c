package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b0\b\u0087\b\u0018\u00002\u00020\u0001B\u0091\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\u0005\u0012\u0006\u0010\u000b\u001a\u00020\u0005\u0012\u0006\u0010\f\u001a\u00020\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0017J\t\u0010-\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010/\u001a\u00020\u0012H\u00c6\u0003J\t\u00100\u001a\u00020\u0007H\u00c6\u0003J\t\u00101\u001a\u00020\u0007H\u00c6\u0003J\t\u00102\u001a\u00020\u0007H\u00c6\u0003J\t\u00103\u001a\u00020\u000fH\u00c6\u0003J\t\u00104\u001a\u00020\u0005H\u00c6\u0003J\t\u00105\u001a\u00020\u0007H\u00c6\u0003J\t\u00106\u001a\u00020\u0007H\u00c6\u0003J\t\u00107\u001a\u00020\u0007H\u00c6\u0003J\t\u00108\u001a\u00020\u0005H\u00c6\u0003J\t\u00109\u001a\u00020\u0005H\u00c6\u0003J\t\u0010:\u001a\u00020\rH\u00c6\u0003J\u0010\u0010;\u001a\u0004\u0018\u00010\u000fH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\u00a8\u0001\u0010<\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u00052\b\b\u0002\u0010\u000b\u001a\u00020\u00052\b\b\u0002\u0010\f\u001a\u00020\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u000f2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00072\b\b\u0002\u0010\u0014\u001a\u00020\u00072\b\b\u0002\u0010\u0015\u001a\u00020\u00072\b\b\u0002\u0010\u0016\u001a\u00020\u000fH\u00c6\u0001\u00a2\u0006\u0002\u0010=J\u0013\u0010>\u001a\u00020\u000f2\b\u0010?\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010@\u001a\u00020\u0012H\u00d6\u0001J\t\u0010A\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u000f\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\u000e\u0010#R\u0011\u0010\u0016\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010%R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001bR\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001bR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001dR\u0011\u0010\u0013\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001dR\u0011\u0010\u0014\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001dR\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001bR\u0011\u0010\u0015\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u001d\u00a8\u0006B"}, d2 = {"Lcom/focusflow/data/model/BudgetRecommendation;", "", "id", "", "categoryName", "", "recommendedAmount", "", "currentAmount", "confidenceScore", "reasonCode", "reasonDescription", "generatedDate", "Lkotlinx/datetime/LocalDateTime;", "isAccepted", "", "userFeedback", "basedOnDays", "", "seasonalFactor", "trendFactor", "varianceFactor", "isActive", "(JLjava/lang/String;DDDLjava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/Boolean;Ljava/lang/String;IDDDZ)V", "getBasedOnDays", "()I", "getCategoryName", "()Ljava/lang/String;", "getConfidenceScore", "()D", "getCurrentAmount", "getGeneratedDate", "()Lkotlinx/datetime/LocalDateTime;", "getId", "()J", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "()Z", "getReasonCode", "getReasonDescription", "getRecommendedAmount", "getSeasonalFactor", "getTrendFactor", "getUserFeedback", "getVarianceFactor", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;DDDLjava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/Boolean;Ljava/lang/String;IDDDZ)Lcom/focusflow/data/model/BudgetRecommendation;", "equals", "other", "hashCode", "toString", "app_release"})
@androidx.room.Entity(tableName = "budget_recommendations")
public final class BudgetRecommendation {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String categoryName = null;
    private final double recommendedAmount = 0.0;
    private final double currentAmount = 0.0;
    private final double confidenceScore = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String reasonCode = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String reasonDescription = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime generatedDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean isAccepted = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String userFeedback = null;
    private final int basedOnDays = 0;
    private final double seasonalFactor = 0.0;
    private final double trendFactor = 0.0;
    private final double varianceFactor = 0.0;
    private final boolean isActive = false;
    
    public BudgetRecommendation(long id, @org.jetbrains.annotations.NotNull
    java.lang.String categoryName, double recommendedAmount, double currentAmount, double confidenceScore, @org.jetbrains.annotations.NotNull
    java.lang.String reasonCode, @org.jetbrains.annotations.NotNull
    java.lang.String reasonDescription, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime generatedDate, @org.jetbrains.annotations.Nullable
    java.lang.Boolean isAccepted, @org.jetbrains.annotations.Nullable
    java.lang.String userFeedback, int basedOnDays, double seasonalFactor, double trendFactor, double varianceFactor, boolean isActive) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCategoryName() {
        return null;
    }
    
    public final double getRecommendedAmount() {
        return 0.0;
    }
    
    public final double getCurrentAmount() {
        return 0.0;
    }
    
    public final double getConfidenceScore() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getReasonCode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getReasonDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getGeneratedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean isAccepted() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getUserFeedback() {
        return null;
    }
    
    public final int getBasedOnDays() {
        return 0;
    }
    
    public final double getSeasonalFactor() {
        return 0.0;
    }
    
    public final double getTrendFactor() {
        return 0.0;
    }
    
    public final double getVarianceFactor() {
        return 0.0;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component10() {
        return null;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final double component12() {
        return 0.0;
    }
    
    public final double component13() {
        return 0.0;
    }
    
    public final double component14() {
        return 0.0;
    }
    
    public final boolean component15() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.BudgetRecommendation copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String categoryName, double recommendedAmount, double currentAmount, double confidenceScore, @org.jetbrains.annotations.NotNull
    java.lang.String reasonCode, @org.jetbrains.annotations.NotNull
    java.lang.String reasonDescription, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime generatedDate, @org.jetbrains.annotations.Nullable
    java.lang.Boolean isAccepted, @org.jetbrains.annotations.Nullable
    java.lang.String userFeedback, int basedOnDays, double seasonalFactor, double trendFactor, double varianceFactor, boolean isActive) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
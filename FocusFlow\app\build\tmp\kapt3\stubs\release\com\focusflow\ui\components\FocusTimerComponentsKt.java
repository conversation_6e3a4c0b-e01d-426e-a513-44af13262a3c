package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000V\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\b\u0010\t\u001a\u0082\u0001\u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\r2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00122\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00122\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u00122\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001ah\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\b\u0010\u000f\u001a\u0004\u0018\u00010\r2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\u000e\b\u0002\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00010\u00162\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a&\u0010\"\u001a\u00020\u00012\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u001e2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u0016H\u0003\u001a>\u0010\'\u001a\u00020\u00012\f\u0010(\u001a\b\u0012\u0004\u0012\u00020$0\f2\b\u0010)\u001a\u0004\u0018\u00010$2\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u00020\u00010\u00122\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006+"}, d2 = {"CircularTimer", "", "progress", "", "color", "Landroidx/compose/ui/graphics/Color;", "modifier", "Landroidx/compose/ui/Modifier;", "CircularTimer-bw27NRU", "(FJLandroidx/compose/ui/Modifier;)V", "FocusSessionSetup", "taskTypes", "", "", "selectedTaskType", "sessionName", "sessionGoal", "onTaskTypeSelected", "Lkotlin/Function1;", "onSessionNameChanged", "onSessionGoalChanged", "onStartSession", "Lkotlin/Function0;", "FocusTimerWidget", "remainingTimeSeconds", "", "totalTimeSeconds", "timerState", "Lcom/focusflow/service/TimerState;", "isBreakTime", "", "onStartPause", "onStop", "onSkipBreak", "TimerPresetItem", "preset", "Lcom/focusflow/service/TimerPreset;", "isSelected", "onClick", "TimerPresetSelector", "presets", "selectedPreset", "onPresetSelected", "app_release"})
public final class FocusTimerComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void FocusTimerWidget(long remainingTimeSeconds, long totalTimeSeconds, @org.jetbrains.annotations.NotNull
    com.focusflow.service.TimerState timerState, boolean isBreakTime, @org.jetbrains.annotations.Nullable
    java.lang.String sessionName, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartPause, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStop, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSkipBreak, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TimerPresetSelector(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.TimerPreset> presets, @org.jetbrains.annotations.Nullable
    com.focusflow.service.TimerPreset selectedPreset, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.service.TimerPreset, kotlin.Unit> onPresetSelected, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void TimerPresetItem(com.focusflow.service.TimerPreset preset, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FocusSessionSetup(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> taskTypes, @org.jetbrains.annotations.NotNull
    java.lang.String selectedTaskType, @org.jetbrains.annotations.NotNull
    java.lang.String sessionName, @org.jetbrains.annotations.NotNull
    java.lang.String sessionGoal, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onTaskTypeSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSessionNameChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSessionGoalChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartSession, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
}
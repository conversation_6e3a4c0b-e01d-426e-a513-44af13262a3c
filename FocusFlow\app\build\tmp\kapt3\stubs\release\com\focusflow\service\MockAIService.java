package com.focusflow.service;

/**
 * Mock implementation of AIService for development and testing
 * This provides realistic responses without requiring external AI API calls
 */
@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0007\b\u0007\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006H\u0096@\u00a2\u0006\u0002\u0010\nJ&\u0010\u000b\u001a\u00020\u00042\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u00062\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0010J&\u0010\u0011\u001a\u00020\u00042\b\u0010\u0012\u001a\u0004\u0018\u00010\u000f2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\r0\u0006H\u0096@\u00a2\u0006\u0002\u0010\u0014J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0010\u0010\u0019\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002J\u0010\u0010\u001b\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002J\u0010\u0010\u001c\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0010\u0010\u001d\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002J\u0010\u0010\u001e\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0010\u0010\u001f\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u001e\u0010 \u001a\u00020\u00042\u0006\u0010!\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020#H\u0096@\u00a2\u0006\u0002\u0010$J\u0010\u0010%\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002J$\u0010&\u001a\u00020\u00042\u0006\u0010\'\u001a\u00020(2\f\u0010)\u001a\b\u0012\u0004\u0012\u00020*0\u0006H\u0096@\u00a2\u0006\u0002\u0010+J\u0010\u0010,\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002J(\u0010-\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u00162\u0006\u0010.\u001a\u00020/H\u0096@\u00a2\u0006\u0002\u00100J\u0010\u00101\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J\u0010\u00102\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002J$\u00103\u001a\b\u0012\u0004\u0012\u00020\u00160\u00062\u0006\u00104\u001a\u0002052\u0006\u00106\u001a\u000207H\u0096@\u00a2\u0006\u0002\u00108J\u0010\u00109\u001a\u00020\u00162\u0006\u0010\u001a\u001a\u00020\u0016H\u0002\u00a8\u0006:"}, d2 = {"Lcom/focusflow/service/MockAIService;", "Lcom/focusflow/service/AIService;", "()V", "analyzeDebtSituation", "Lcom/focusflow/service/AIResponse;", "creditCards", "", "Lcom/focusflow/service/CreditCardData;", "paymentHistory", "Lcom/focusflow/service/PaymentData;", "(Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "analyzeSpending", "expenses", "Lcom/focusflow/service/ExpenseData;", "budget", "Lcom/focusflow/service/BudgetData;", "(Ljava/util/List;Lcom/focusflow/service/BudgetData;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateBudgetAdvice", "currentBudget", "spendingHistory", "(Lcom/focusflow/service/BudgetData;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateBudgetOptimizationInsight", "", "context", "Lcom/focusflow/service/UserContext;", "generateBudgetResponse", "prompt", "generateDebtResponse", "generateDebtStrategyInsight", "generateGeneralResponse", "generateGoalProgressInsight", "generateHabitImprovementInsight", "generateInsight", "userContext", "insightType", "Lcom/focusflow/service/InsightType;", "(Lcom/focusflow/service/UserContext;Lcom/focusflow/service/InsightType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateInsightResponse", "generateMotivationalMessage", "userProgress", "Lcom/focusflow/service/UserProgress;", "recentAchievements", "Lcom/focusflow/service/Achievement;", "(Lcom/focusflow/service/UserProgress;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateProgressResponse", "generateResponse", "maxTokens", "", "(Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateSpendingPatternInsight", "generateSpendingResponse", "generateTaskBreakdown", "task", "Lcom/focusflow/service/TaskData;", "userPreferences", "Lcom/focusflow/service/UserPreferences;", "(Lcom/focusflow/service/TaskData;Lcom/focusflow/service/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateTaskResponse", "app_release"})
public final class MockAIService implements com.focusflow.service.AIService {
    
    @javax.inject.Inject
    public MockAIService() {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object generateResponse(@org.jetbrains.annotations.NotNull
    java.lang.String prompt, @org.jetbrains.annotations.Nullable
    java.lang.String context, int maxTokens, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object generateInsight(@org.jetbrains.annotations.NotNull
    com.focusflow.service.UserContext userContext, @org.jetbrains.annotations.NotNull
    com.focusflow.service.InsightType insightType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object analyzeSpending(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.ExpenseData> expenses, @org.jetbrains.annotations.Nullable
    com.focusflow.service.BudgetData budget, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object generateBudgetAdvice(@org.jetbrains.annotations.Nullable
    com.focusflow.service.BudgetData currentBudget, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.ExpenseData> spendingHistory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object analyzeDebtSituation(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.CreditCardData> creditCards, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.PaymentData> paymentHistory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object generateTaskBreakdown(@org.jetbrains.annotations.NotNull
    com.focusflow.service.TaskData task, @org.jetbrains.annotations.NotNull
    com.focusflow.service.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.Nullable
    public java.lang.Object generateMotivationalMessage(@org.jetbrains.annotations.NotNull
    com.focusflow.service.UserProgress userProgress, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.Achievement> recentAchievements, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AIResponse> $completion) {
        return null;
    }
    
    private final java.lang.String generateSpendingResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateBudgetResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateDebtResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateTaskResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateProgressResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateInsightResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateGeneralResponse(java.lang.String prompt) {
        return null;
    }
    
    private final java.lang.String generateSpendingPatternInsight(com.focusflow.service.UserContext context) {
        return null;
    }
    
    private final java.lang.String generateBudgetOptimizationInsight(com.focusflow.service.UserContext context) {
        return null;
    }
    
    private final java.lang.String generateDebtStrategyInsight(com.focusflow.service.UserContext context) {
        return null;
    }
    
    private final java.lang.String generateHabitImprovementInsight(com.focusflow.service.UserContext context) {
        return null;
    }
    
    private final java.lang.String generateGoalProgressInsight(com.focusflow.service.UserContext context) {
        return null;
    }
}
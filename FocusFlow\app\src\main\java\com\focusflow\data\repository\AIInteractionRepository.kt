package com.focusflow.data.repository

import com.focusflow.data.dao.AIInteractionDao
import com.focusflow.data.model.AIInteraction
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AIInteractionRepository @Inject constructor(
    private val aiInteractionDao: AIInteractionDao
) {
    
    fun getAllInteractions(): Flow<List<AIInteraction>> = 
        aiInteractionDao.getAllInteractions()
    
    fun getInteractionsByType(type: String): Flow<List<AIInteraction>> = 
        aiInteractionDao.getInteractionsByType(type)
    
    fun getRecentInteractions(limit: Int = 50): Flow<List<AIInteraction>> = 
        aiInteractionDao.getRecentInteractions(limit)
    
    suspend fun insertInteraction(interaction: AIInteraction): Long = 
        aiInteractionDao.insertInteraction(interaction)
    
    suspend fun deleteInteraction(interaction: AIInteraction) = 
        aiInteractionDao.deleteInteraction(interaction)
    
    suspend fun deleteOldInteractions(cutoffDate: LocalDateTime) = 
        aiInteractionDao.deleteOldInteractions(cutoffDate)
    
    // Helper methods for common interaction types
    suspend fun saveSpendingAnalysis(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "spending_analysis",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    suspend fun saveBudgetAdvice(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "budget_advice",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    suspend fun saveDebtAdvice(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "debt_advice",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    suspend fun saveTaskBreakdown(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "task_breakdown",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    suspend fun saveProgressReport(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "progress_report",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    suspend fun saveMotivationalMessage(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "motivation",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    suspend fun saveGeneralInteraction(
        userMessage: String,
        aiResponse: String,
        contextData: String? = null
    ): Long {
        return insertInteraction(
            AIInteraction(
                userMessage = userMessage,
                aiResponse = aiResponse,
                interactionType = "general",
                timestamp = kotlinx.datetime.Clock.System.now().toLocalDateTime(kotlinx.datetime.TimeZone.currentSystemDefault()),
                contextData = contextData
            )
        )
    }
    
    // Analytics methods
    suspend fun getInteractionCount(): Int {
        return try {
            getAllInteractions().let { flow ->
                // This is a simplified approach - in a real implementation,
                // you'd want a proper count query in the DAO
                0 // Placeholder - would need to implement count query
            }
        } catch (e: Exception) {
            0
        }
    }
    
    fun getInteractionsByDateRange(
        startDate: LocalDateTime,
        endDate: LocalDateTime
    ): Flow<List<AIInteraction>> {
        // This would need a corresponding DAO method
        return getAllInteractions() // Placeholder implementation
    }
}

package com.focusflow.security;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DataProtectionService_Factory implements Factory<DataProtectionService> {
  private final Provider<Context> contextProvider;

  private final Provider<SecurityManager> securityManagerProvider;

  public DataProtectionService_Factory(Provider<Context> contextProvider,
      Provider<SecurityManager> securityManagerProvider) {
    this.contextProvider = contextProvider;
    this.securityManagerProvider = securityManagerProvider;
  }

  @Override
  public DataProtectionService get() {
    return newInstance(contextProvider.get(), securityManagerProvider.get());
  }

  public static DataProtectionService_Factory create(Provider<Context> contextProvider,
      Provider<SecurityManager> securityManagerProvider) {
    return new DataProtectionService_Factory(contextProvider, securityManagerProvider);
  }

  public static DataProtectionService newInstance(Context context,
      SecurityManager securityManager) {
    return new DataProtectionService(context, securityManager);
  }
}

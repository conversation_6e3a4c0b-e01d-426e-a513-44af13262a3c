package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0017\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u000e\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0014\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00140\u0013H\'J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00140\u00132\u0006\u0010\u0017\u001a\u00020\u0018H\'J\u001c\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00140\u00132\u0006\u0010\u001a\u001a\u00020\u001bH\'J\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00140\u00132\u0006\u0010\u001d\u001a\u00020\u0018H\'J\u001c\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00140\u00132\u0006\u0010\u001f\u001a\u00020\u0018H\'J\u0010\u0010 \u001a\u0004\u0018\u00010\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0010\u0010!\u001a\u0004\u0018\u00010\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u0011J$\u0010\"\u001a\b\u0012\u0004\u0012\u00020\t0\u00142\u0006\u0010#\u001a\u00020\u001b2\u0006\u0010$\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010%J\u0010\u0010&\u001a\u0004\u0018\u00010\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\'\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010(\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010)\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010*\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010+\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010,J\u0016\u0010-\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ*\u0010.\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\u0010/\u001a\u0004\u0018\u00010\u00102\b\u00100\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u00101\u00a8\u00062"}, d2 = {"Lcom/focusflow/data/dao/AlternativeProductDao;", "", "deactivateAlternative", "", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAlternative", "alternative", "Lcom/focusflow/data/model/AlternativeProduct;", "(Lcom/focusflow/data/model/AlternativeProduct;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteUnusedOldAlternatives", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveAlternativeCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActiveAlternatives", "Lkotlinx/coroutines/flow/Flow;", "", "getAlternativeById", "getAlternativesByCategory", "category", "", "getAlternativesBySavings", "minSavings", "", "getAlternativesByType", "type", "getAlternativesForProduct", "productName", "getAverageSavings", "getAverageUserRating", "getTopAlternatives", "minConfidence", "limit", "(DILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTotalSavingsRealized", "insertAlternative", "recordAlternativeAccepted", "recordAlternativeRejected", "recordAlternativeShown", "timestamp", "(JLkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAlternative", "updateUserFeedback", "rating", "feedback", "(JLjava/lang/Integer;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
@androidx.room.Dao
public abstract interface AlternativeProductDao {
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AlternativeProduct>> getAllActiveAlternatives();
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE originalCategory = :category AND isActive = 1 ORDER BY savingsAmount DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AlternativeProduct>> getAlternativesByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category);
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE alternativeType = :type AND isActive = 1 ORDER BY savingsAmount DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AlternativeProduct>> getAlternativesByType(@org.jetbrains.annotations.NotNull
    java.lang.String type);
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE originalProductName LIKE \'%\' || :productName || \'%\' AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AlternativeProduct>> getAlternativesForProduct(@org.jetbrains.annotations.NotNull
    java.lang.String productName);
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE savingsAmount >= :minSavings AND isActive = 1 ORDER BY savingsAmount DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AlternativeProduct>> getAlternativesBySavings(double minSavings);
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAlternativeById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.AlternativeProduct> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM alternative_products WHERE confidenceScore >= :minConfidence AND isActive = 1 ORDER BY confidenceScore DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTopAlternatives(double minConfidence, int limit, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.AlternativeProduct>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM alternative_products WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getActiveAlternativeCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(savingsAmount) FROM alternative_products WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageSavings(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(savingsAmount * timesAccepted) FROM alternative_products WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalSavingsRealized(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(userRating) FROM alternative_products WHERE userRating IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageUserRating(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertAlternative(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AlternativeProduct alternative, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateAlternative(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AlternativeProduct alternative, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAlternative(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AlternativeProduct alternative, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE alternative_products SET timesShown = timesShown + 1, lastSuggested = :timestamp WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordAlternativeShown(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE alternative_products SET timesAccepted = timesAccepted + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordAlternativeAccepted(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE alternative_products SET timesRejected = timesRejected + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordAlternativeRejected(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE alternative_products SET userRating = :rating, userFeedback = :feedback WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUserFeedback(long id, @org.jetbrains.annotations.Nullable
    java.lang.Integer rating, @org.jetbrains.annotations.Nullable
    java.lang.String feedback, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE alternative_products SET isActive = 0 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deactivateAlternative(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM alternative_products WHERE createdDate < :cutoffDate AND timesAccepted = 0")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteUnusedOldAlternatives(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
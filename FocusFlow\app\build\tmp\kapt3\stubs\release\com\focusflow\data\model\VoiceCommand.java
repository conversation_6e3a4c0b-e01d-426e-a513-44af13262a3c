package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\b\n\u0002\bP\b\u0087\b\u0018\u00002\u00020\u0001B\u00d9\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u000b\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u000b\u0012\b\b\u0002\u0010 \u001a\u00020!\u0012\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010$\u001a\u00020\u0005\u0012\b\b\u0002\u0010%\u001a\u00020\u000b\u0012\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010(J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\t\u0010M\u001a\u00020\u0010H\u00c6\u0003J\u000b\u0010N\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010O\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010P\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010Q\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010R\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010S\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010T\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010U\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010V\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010W\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010X\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010]\u001a\u00020\u000bH\u00c6\u0003J\t\u0010^\u001a\u00020!H\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010`\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010c\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010d\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010g\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010h\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010i\u001a\u00020\u000bH\u00c6\u0003J\t\u0010j\u001a\u00020\rH\u00c6\u0003J\t\u0010k\u001a\u00020\u0003H\u00c6\u0003J\u00eb\u0002\u0010l\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0014\u001a\u00020\u000b2\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001b\u001a\u00020\u00052\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001f\u001a\u00020\u000b2\b\b\u0002\u0010 \u001a\u00020!2\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010$\u001a\u00020\u00052\b\b\u0002\u0010%\u001a\u00020\u000b2\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010m\u001a\u00020\u000b2\b\u0010n\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010o\u001a\u00020!H\u00d6\u0001J\t\u0010p\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0013\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010*R\u0013\u0010\"\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010*R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010*R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010*R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0013\u0010\u0013\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010*R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010*R\u0013\u0010&\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010*R\u0013\u0010\u001e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010*R\u0013\u0010\u0016\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010*R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010*R\u0011\u0010\u0014\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00108R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010*R\u0011\u0010\u001f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u00108R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u00108R\u0011\u0010%\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u00108R\u0011\u0010\u001b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010*R\u0013\u0010#\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010*R\u0013\u0010\u001d\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010*R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010*R\u0011\u0010$\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010*R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010:R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010*R\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010*R\u0011\u0010 \u001a\u00020!\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010ER\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010*R\u0013\u0010\'\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010*R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010IR\u0013\u0010\u001a\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u0010*R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u0010*\u00a8\u0006q"}, d2 = {"Lcom/focusflow/data/model/VoiceCommand;", "", "id", "", "commandText", "", "recognizedText", "commandType", "intent", "parameters", "isSuccessful", "", "confidence", "", "processingTime", "timestamp", "Lkotlinx/datetime/LocalDateTime;", "userId", "sessionId", "context", "followUpRequired", "followUpPrompt", "errorMessage", "correctedCommand", "actionTaken", "resultData", "userFeedback", "language", "accent", "noiseLevel", "deviceType", "isOffline", "retryCount", "", "alternativeCommands", "learningData", "privacyLevel", "isTrainingData", "customVocabulary", "shortcuts", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZDJLkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZILjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;)V", "getAccent", "()Ljava/lang/String;", "getActionTaken", "getAlternativeCommands", "getCommandText", "getCommandType", "getConfidence", "()D", "getContext", "getCorrectedCommand", "getCustomVocabulary", "getDeviceType", "getErrorMessage", "getFollowUpPrompt", "getFollowUpRequired", "()Z", "getId", "()J", "getIntent", "getLanguage", "getLearningData", "getNoiseLevel", "getParameters", "getPrivacyLevel", "getProcessingTime", "getRecognizedText", "getResultData", "getRetryCount", "()I", "getSessionId", "getShortcuts", "getTimestamp", "()Lkotlinx/datetime/LocalDateTime;", "getUserFeedback", "getUserId", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_release"})
@androidx.room.Entity(tableName = "voice_commands")
public final class VoiceCommand {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String commandText = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String recognizedText = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String commandType = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String intent = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String parameters = null;
    private final boolean isSuccessful = false;
    private final double confidence = 0.0;
    private final long processingTime = 0L;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime timestamp = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String userId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sessionId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String context = null;
    private final boolean followUpRequired = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String followUpPrompt = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String errorMessage = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String correctedCommand = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String actionTaken = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String resultData = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String userFeedback = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String language = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String accent = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String noiseLevel = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String deviceType = null;
    private final boolean isOffline = false;
    private final int retryCount = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String alternativeCommands = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String learningData = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String privacyLevel = null;
    private final boolean isTrainingData = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String customVocabulary = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String shortcuts = null;
    
    public VoiceCommand(long id, @org.jetbrains.annotations.NotNull
    java.lang.String commandText, @org.jetbrains.annotations.NotNull
    java.lang.String recognizedText, @org.jetbrains.annotations.NotNull
    java.lang.String commandType, @org.jetbrains.annotations.NotNull
    java.lang.String intent, @org.jetbrains.annotations.Nullable
    java.lang.String parameters, boolean isSuccessful, double confidence, long processingTime, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime timestamp, @org.jetbrains.annotations.Nullable
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String sessionId, @org.jetbrains.annotations.Nullable
    java.lang.String context, boolean followUpRequired, @org.jetbrains.annotations.Nullable
    java.lang.String followUpPrompt, @org.jetbrains.annotations.Nullable
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable
    java.lang.String correctedCommand, @org.jetbrains.annotations.Nullable
    java.lang.String actionTaken, @org.jetbrains.annotations.Nullable
    java.lang.String resultData, @org.jetbrains.annotations.Nullable
    java.lang.String userFeedback, @org.jetbrains.annotations.NotNull
    java.lang.String language, @org.jetbrains.annotations.Nullable
    java.lang.String accent, @org.jetbrains.annotations.Nullable
    java.lang.String noiseLevel, @org.jetbrains.annotations.Nullable
    java.lang.String deviceType, boolean isOffline, int retryCount, @org.jetbrains.annotations.Nullable
    java.lang.String alternativeCommands, @org.jetbrains.annotations.Nullable
    java.lang.String learningData, @org.jetbrains.annotations.NotNull
    java.lang.String privacyLevel, boolean isTrainingData, @org.jetbrains.annotations.Nullable
    java.lang.String customVocabulary, @org.jetbrains.annotations.Nullable
    java.lang.String shortcuts) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCommandText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getRecognizedText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCommandType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getIntent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getParameters() {
        return null;
    }
    
    public final boolean isSuccessful() {
        return false;
    }
    
    public final double getConfidence() {
        return 0.0;
    }
    
    public final long getProcessingTime() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getTimestamp() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getUserId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSessionId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getContext() {
        return null;
    }
    
    public final boolean getFollowUpRequired() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getFollowUpPrompt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCorrectedCommand() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getActionTaken() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getResultData() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getUserFeedback() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getLanguage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAccent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNoiseLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDeviceType() {
        return null;
    }
    
    public final boolean isOffline() {
        return false;
    }
    
    public final int getRetryCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAlternativeCommands() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getLearningData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPrivacyLevel() {
        return null;
    }
    
    public final boolean isTrainingData() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCustomVocabulary() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getShortcuts() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component13() {
        return null;
    }
    
    public final boolean component14() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component20() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component23() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component24() {
        return null;
    }
    
    public final boolean component25() {
        return false;
    }
    
    public final int component26() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component27() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component28() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component29() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component30() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component31() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component32() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final long component9() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.VoiceCommand copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String commandText, @org.jetbrains.annotations.NotNull
    java.lang.String recognizedText, @org.jetbrains.annotations.NotNull
    java.lang.String commandType, @org.jetbrains.annotations.NotNull
    java.lang.String intent, @org.jetbrains.annotations.Nullable
    java.lang.String parameters, boolean isSuccessful, double confidence, long processingTime, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime timestamp, @org.jetbrains.annotations.Nullable
    java.lang.String userId, @org.jetbrains.annotations.Nullable
    java.lang.String sessionId, @org.jetbrains.annotations.Nullable
    java.lang.String context, boolean followUpRequired, @org.jetbrains.annotations.Nullable
    java.lang.String followUpPrompt, @org.jetbrains.annotations.Nullable
    java.lang.String errorMessage, @org.jetbrains.annotations.Nullable
    java.lang.String correctedCommand, @org.jetbrains.annotations.Nullable
    java.lang.String actionTaken, @org.jetbrains.annotations.Nullable
    java.lang.String resultData, @org.jetbrains.annotations.Nullable
    java.lang.String userFeedback, @org.jetbrains.annotations.NotNull
    java.lang.String language, @org.jetbrains.annotations.Nullable
    java.lang.String accent, @org.jetbrains.annotations.Nullable
    java.lang.String noiseLevel, @org.jetbrains.annotations.Nullable
    java.lang.String deviceType, boolean isOffline, int retryCount, @org.jetbrains.annotations.Nullable
    java.lang.String alternativeCommands, @org.jetbrains.annotations.Nullable
    java.lang.String learningData, @org.jetbrains.annotations.NotNull
    java.lang.String privacyLevel, boolean isTrainingData, @org.jetbrains.annotations.Nullable
    java.lang.String customVocabulary, @org.jetbrains.annotations.Nullable
    java.lang.String shortcuts) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
{"deviceMatrix": {"description": "FocusFlow Multi-Device Testing Matrix", "version": "1.0.0", "testCategories": ["functionality", "performance", "ui_consistency", "accessibility", "security"], "devices": [{"category": "budget_phones", "description": "Budget Android phones with limited resources", "devices": [{"name": "Samsung Galaxy A12", "api_level": 30, "screen_size": "6.5 inch", "resolution": "720x1600", "density": "mdpi", "ram": "3GB", "storage": "32GB", "processor": "MediaTek Helio P35", "test_priority": "high", "test_scenarios": ["low_memory_performance", "basic_functionality", "battery_optimization"]}, {"name": "Xiaomi Redmi 9A", "api_level": 29, "screen_size": "6.53 inch", "resolution": "720x1600", "density": "mdpi", "ram": "2GB", "storage": "32GB", "processor": "MediaTek Helio G25", "test_priority": "high", "test_scenarios": ["minimum_spec_performance", "memory_constraints", "storage_limitations"]}]}, {"category": "mid_range_phones", "description": "Mid-range Android phones with balanced specs", "devices": [{"name": "Google Pixel 6a", "api_level": 33, "screen_size": "6.1 inch", "resolution": "1080x2400", "density": "xhdpi", "ram": "6GB", "storage": "128GB", "processor": "Google Tensor", "test_priority": "high", "test_scenarios": ["standard_performance", "camera_integration", "biometric_authentication", "modern_android_features"]}, {"name": "Samsung Galaxy A54", "api_level": 33, "screen_size": "6.4 inch", "resolution": "1080x2340", "density": "xhdpi", "ram": "8GB", "storage": "256GB", "processor": "Exynos 1380", "test_priority": "medium", "test_scenarios": ["samsung_ui_compatibility", "performance_optimization", "storage_management"]}]}, {"category": "flagship_phones", "description": "High-end Android phones with premium features", "devices": [{"name": "Samsung Galaxy S23 Ultra", "api_level": 34, "screen_size": "6.8 inch", "resolution": "1440x3088", "density": "xxxhdpi", "ram": "12GB", "storage": "512GB", "processor": "Snapdragon 8 Gen 2", "test_priority": "high", "test_scenarios": ["high_resolution_display", "advanced_features", "s_pen_integration", "premium_performance"]}, {"name": "Google Pixel 8 Pro", "api_level": 34, "screen_size": "6.7 inch", "resolution": "1344x2992", "density": "xxxhdpi", "ram": "12GB", "storage": "256GB", "processor": "Google Tensor G3", "test_priority": "high", "test_scenarios": ["latest_android_features", "ai_integration", "camera_ml_features", "stock_android_experience"]}]}, {"category": "tablets", "description": "Android tablets with larger screens", "devices": [{"name": "Samsung Galaxy Tab A8", "api_level": 31, "screen_size": "10.5 inch", "resolution": "1200x1920", "density": "mdpi", "ram": "4GB", "storage": "64GB", "processor": "Unisoc Tiger T618", "test_priority": "high", "test_scenarios": ["tablet_layout_adaptation", "landscape_orientation", "multi_window_support", "large_screen_ui"]}, {"name": "Samsung Galaxy Tab S8", "api_level": 32, "screen_size": "11 inch", "resolution": "1600x2560", "density": "xhdpi", "ram": "8GB", "storage": "128GB", "processor": "Snapdragon 8 Gen 1", "test_priority": "medium", "test_scenarios": ["premium_tablet_experience", "s_pen_functionality", "desktop_mode", "productivity_features"]}]}, {"category": "foldable_devices", "description": "Foldable Android devices with unique form factors", "devices": [{"name": "Samsung Galaxy Z Fold 5", "api_level": 34, "screen_size": "7.6 inch (unfolded)", "resolution": "1812x2176", "density": "xxhdpi", "ram": "12GB", "storage": "256GB", "processor": "Snapdragon 8 Gen 2", "test_priority": "medium", "test_scenarios": ["foldable_screen_adaptation", "multi_window_layouts", "screen_continuity", "fold_unfold_transitions"]}, {"name": "Samsung Galaxy Z Flip 5", "api_level": 34, "screen_size": "6.7 inch (unfolded)", "resolution": "1080x2640", "density": "xxhdpi", "ram": "8GB", "storage": "256GB", "processor": "Snapdragon 8 Gen 2", "test_priority": "low", "test_scenarios": ["compact_foldable_ui", "cover_screen_integration", "flip_gestures", "one_handed_usage"]}]}], "android_versions": [{"version": "Android 7.0", "api_level": 24, "test_priority": "high", "market_share": "5%", "test_scenarios": ["minimum_version_compatibility", "legacy_feature_support", "performance_on_older_devices"]}, {"version": "Android 8.0/8.1", "api_level": 26, "test_priority": "medium", "market_share": "8%", "test_scenarios": ["notification_channels", "background_execution_limits", "adaptive_icons"]}, {"version": "Android 9", "api_level": 28, "test_priority": "medium", "market_share": "12%", "test_scenarios": ["gesture_navigation", "display_cutout_support", "network_security_config"]}, {"version": "Android 10", "api_level": 29, "test_priority": "high", "market_share": "18%", "test_scenarios": ["scoped_storage", "dark_theme", "gesture_navigation_full", "biometric_authentication"]}, {"version": "Android 11", "api_level": 30, "test_priority": "high", "market_share": "22%", "test_scenarios": ["one_time_permissions", "chat_bubbles", "media_controls", "wireless_debugging"]}, {"version": "Android 12", "api_level": 31, "test_priority": "high", "market_share": "20%", "test_scenarios": ["material_you", "approximate_location", "splash_screen_api", "performance_improvements"]}, {"version": "Android 13", "api_level": 33, "test_priority": "high", "market_share": "12%", "test_scenarios": ["notification_permission", "themed_app_icons", "per_app_language", "media_file_access"]}, {"version": "Android 14", "api_level": 34, "test_priority": "high", "market_share": "3%", "test_scenarios": ["latest_features", "privacy_enhancements", "performance_optimizations", "accessibility_improvements"]}], "test_scenarios": {"functionality": ["app_launch_and_navigation", "data_input_and_validation", "offline_functionality", "sync_and_backup", "notification_handling", "biometric_authentication", "camera_integration", "voice_input"], "performance": ["app_startup_time", "memory_usage", "battery_consumption", "network_efficiency", "database_performance", "ui_responsiveness", "background_processing"], "ui_consistency": ["layout_adaptation", "font_scaling", "dark_mode_support", "orientation_changes", "multi_window_support", "accessibility_compliance", "touch_target_sizes"], "accessibility": ["screen_reader_support", "keyboard_navigation", "high_contrast_mode", "large_text_support", "color_blind_accessibility", "motor_impairment_support"], "security": ["data_encryption", "secure_storage", "network_security", "biometric_security", "app_signing_verification", "permission_handling"]}, "test_automation": {"frameworks": ["Espresso", "UI Automator", "Compose Testing", "Firebase Test Lab"], "ci_cd_integration": {"github_actions": true, "jenkins": true, "gitlab_ci": true}, "reporting": {"html_reports": true, "junit_xml": true, "allure_reports": true, "firebase_test_lab_reports": true}}, "performance_benchmarks": {"app_startup": {"cold_start": "< 3 seconds", "warm_start": "< 1 second", "hot_start": "< 0.5 seconds"}, "memory_usage": {"idle": "< 100 MB", "active_use": "< 200 MB", "peak_usage": "< 300 MB"}, "battery_consumption": {"background": "< 2% per hour", "active_use": "< 10% per hour"}, "network_usage": {"sync": "< 1 MB per session", "initial_setup": "< 10 MB"}}}}
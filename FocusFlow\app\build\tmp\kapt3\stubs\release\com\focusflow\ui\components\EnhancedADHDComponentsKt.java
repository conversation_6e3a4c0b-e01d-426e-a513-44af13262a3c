package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000n\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a^\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00022\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00040\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u000b2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0002H\u0007\u001av\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u00022\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00022\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u000b2\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u00072\u001c\u0010\u0018\u001a\u0018\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u00040\u0019\u00a2\u0006\u0002\b\u001b\u00a2\u0006\u0002\b\u001cH\u0007\u001aZ\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u00022\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010!\u001a\u00020\u000b2\u000e\b\u0002\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00020\u00012\b\b\u0002\u0010#\u001a\u00020$2\b\b\u0002\u0010%\u001a\u00020\u000bH\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b&\u0010\'\u001a6\u0010(\u001a\u00020\u00042\u0006\u0010)\u001a\u00020*2\u0006\u0010\u0005\u001a\u00020\u00022\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010+\u001a\u00020\u000b2\b\b\u0002\u0010,\u001a\u00020-H\u0007\"\u0014\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006."}, d2 = {"defaultMotivationalMessages", "", "", "EnhancedADHDActionButton", "", "text", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "enabled", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "buttonStyle", "Lcom/focusflow/ui/components/ADHDButtonStyle;", "showLoadingState", "successMessage", "EnhancedADHDInfoCard", "title", "subtitle", "priority", "Lcom/focusflow/ui/components/ADHDPriority;", "expandable", "content", "Lkotlin/Function1;", "Landroidx/compose/foundation/layout/ColumnScope;", "Landroidx/compose/runtime/Composable;", "Lkotlin/ExtensionFunctionType;", "EnhancedADHDProgressIndicator", "progress", "", "label", "showPercentage", "motivationalMessages", "color", "Landroidx/compose/ui/graphics/Color;", "showMilestones", "EnhancedADHDProgressIndicator-hftG7rw", "(FLjava/lang/String;Landroidx/compose/ui/Modifier;ZLjava/util/List;JZ)V", "EnhancedADHDStatusBadge", "status", "Lcom/focusflow/ui/components/ADHDStatus;", "showIcon", "size", "Lcom/focusflow/ui/components/ADHDBadgeSize;", "app_release"})
public final class EnhancedADHDComponentsKt {
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<java.lang.String> defaultMotivationalMessages = null;
    
    /**
     * Enhanced ADHD-Friendly Action Button with comprehensive feedback
     */
    @kotlin.OptIn(markerClass = {androidx.compose.animation.ExperimentalAnimationApi.class})
    @androidx.compose.runtime.Composable
    public static final void EnhancedADHDActionButton(@org.jetbrains.annotations.NotNull
    java.lang.String text, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, boolean enabled, @org.jetbrains.annotations.Nullable
    androidx.compose.ui.graphics.vector.ImageVector icon, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.ADHDButtonStyle buttonStyle, boolean showLoadingState, @org.jetbrains.annotations.Nullable
    java.lang.String successMessage) {
    }
    
    /**
     * Enhanced ADHD-Friendly Information Card with progressive disclosure
     */
    @androidx.compose.runtime.Composable
    public static final void EnhancedADHDInfoCard(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable
    java.lang.String subtitle, @org.jetbrains.annotations.Nullable
    androidx.compose.ui.graphics.vector.ImageVector icon, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.ADHDPriority priority, boolean expandable, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super androidx.compose.foundation.layout.ColumnScope, kotlin.Unit> content) {
    }
    
    /**
     * Enhanced ADHD-Friendly Status Badge with clear visual hierarchy
     */
    @androidx.compose.runtime.Composable
    public static final void EnhancedADHDStatusBadge(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.ADHDStatus status, @org.jetbrains.annotations.NotNull
    java.lang.String text, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, boolean showIcon, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.ADHDBadgeSize size) {
    }
}
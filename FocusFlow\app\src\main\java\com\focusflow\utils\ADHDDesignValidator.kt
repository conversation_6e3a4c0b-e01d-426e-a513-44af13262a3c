package com.focusflow.utils

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.pow

/**
 * ADHD-Friendly Design Validation Framework
 * Ensures all UI elements meet evidence-based ADHD design principles
 */
object ADHDDesignValidator {
    
    // ADHD-Friendly Design Standards
    object Standards {
        // Visual Hierarchy
        val MIN_TOUCH_TARGET_SIZE = 48.dp
        val RECOMMENDED_TOUCH_TARGET_SIZE = 56.dp
        val MIN_TEXT_SIZE = 14.sp
        val RECOMMENDED_BODY_TEXT_SIZE = 16.sp
        val HEADING_TEXT_SIZE = 20.sp
        
        // Spacing and Layout
        val MIN_ELEMENT_SPACING = 8.dp
        val RECOMMENDED_ELEMENT_SPACING = 16.dp
        val CARD_PADDING = 16.dp
        val SECTION_SPACING = 24.dp
        
        // Color and Contrast
        val MIN_CONTRAST_RATIO = 4.5f
        val RECOMMENDED_CONTRAST_RATIO = 7.0f
        val MAX_COLORS_PER_SCREEN = 5
        
        // Cognitive Load
        val MAX_ITEMS_PER_SCREEN = 7 // Miller's Rule: 7±2 items
        val MAX_DECISION_OPTIONS = 3 // Reduce decision fatigue
        val RECOMMENDED_CONTENT_CHUNKS = 3 // Information chunking
        
        // Animation and Feedback
        val FEEDBACK_DELAY_MS = 100L // Immediate feedback threshold
        val ANIMATION_DURATION_MS = 300L // Smooth but not distracting
        val SUCCESS_FEEDBACK_DURATION_MS = 2000L // Positive reinforcement
    }
    
    // ADHD-Friendly Color Palette with Dark Mode Support
    object Colors {
        // Calming and Focus-Supporting Colors (Light Mode)
        val CALM_BLUE = Color(0xFF64B5F6)
        val FOCUS_GREEN = Color(0xFF66BB6A)
        val GENTLE_ORANGE = Color(0xFFFFB74D)
        val SOFT_RED = Color(0xFFE57373)
        val SUCCESS_GREEN = Color(0xFF81C784)
        val WARNING_AMBER = Color(0xFFFFC107)
        val ERROR_RED = Color(0xFFE53935)

        // Dark Mode Variants (Better Contrast)
        val CALM_BLUE_DARK = Color(0xFF90CAF9)
        val FOCUS_GREEN_DARK = Color(0xFF81C784)
        val GENTLE_ORANGE_DARK = Color(0xFFFFCC02)
        val SOFT_RED_DARK = Color(0xFFEF5350)
        val SUCCESS_GREEN_DARK = Color(0xFF66BB6A)
        val WARNING_AMBER_DARK = Color(0xFFFFD54F)
        val ERROR_RED_DARK = Color(0xFFFF5252)

        // Background Colors (reduced eye strain)
        val LIGHT_BACKGROUND = Color(0xFFFAFAFA)
        val DARK_BACKGROUND = Color(0xFF121212)
        val CARD_BACKGROUND = Color(0xFFFFFFFF)
        val CARD_BACKGROUND_DARK = Color(0xFF1E1E1E)
        val SURFACE_VARIANT = Color(0xFFF5F5F5)
        val SURFACE_VARIANT_DARK = Color(0xFF2A2A2A)

        // Text Colors (high contrast)
        val PRIMARY_TEXT = Color(0xFF212121)
        val PRIMARY_TEXT_DARK = Color(0xFFE0E0E0)
        val SECONDARY_TEXT = Color(0xFF757575)
        val SECONDARY_TEXT_DARK = Color(0xFFBDBDBD)
        val DISABLED_TEXT = Color(0xFFBDBDBD)
        val DISABLED_TEXT_DARK = Color(0xFF757575)
    }
    
    /**
     * Validate if a UI element meets ADHD-friendly design standards
     */
    data class ValidationResult(
        val isValid: Boolean,
        val issues: List<String> = emptyList(),
        val recommendations: List<String> = emptyList(),
        val score: Int = 0 // 0-100 ADHD-friendliness score
    )
    
    /**
     * Validate touch target size
     */
    fun validateTouchTarget(size: Dp): ValidationResult {
        val issues = mutableListOf<String>()
        val recommendations = mutableListOf<String>()
        
        when {
            size < Standards.MIN_TOUCH_TARGET_SIZE -> {
                issues.add("Touch target too small (${size}dp < ${Standards.MIN_TOUCH_TARGET_SIZE}dp)")
                recommendations.add("Increase touch target to at least ${Standards.MIN_TOUCH_TARGET_SIZE}dp")
            }
            size < Standards.RECOMMENDED_TOUCH_TARGET_SIZE -> {
                recommendations.add("Consider increasing to ${Standards.RECOMMENDED_TOUCH_TARGET_SIZE}dp for better accessibility")
            }
        }
        
        val score = when {
            size >= Standards.RECOMMENDED_TOUCH_TARGET_SIZE -> 100
            size >= Standards.MIN_TOUCH_TARGET_SIZE -> 75
            else -> 25
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            recommendations = recommendations,
            score = score
        )
    }
    
    /**
     * Validate color contrast for ADHD users
     */
    fun validateColorContrast(foreground: Color, background: Color): ValidationResult {
        val contrastRatio = calculateContrastRatio(foreground, background)
        val issues = mutableListOf<String>()
        val recommendations = mutableListOf<String>()
        
        when {
            contrastRatio < Standards.MIN_CONTRAST_RATIO -> {
                issues.add("Insufficient contrast ratio: ${String.format("%.2f", contrastRatio)} < ${Standards.MIN_CONTRAST_RATIO}")
                recommendations.add("Increase contrast to at least ${Standards.MIN_CONTRAST_RATIO}:1")
            }
            contrastRatio < Standards.RECOMMENDED_CONTRAST_RATIO -> {
                recommendations.add("Consider increasing contrast to ${Standards.RECOMMENDED_CONTRAST_RATIO}:1 for better readability")
            }
        }
        
        val score = when {
            contrastRatio >= Standards.RECOMMENDED_CONTRAST_RATIO -> 100
            contrastRatio >= Standards.MIN_CONTRAST_RATIO -> 75
            else -> 25
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            recommendations = recommendations,
            score = score
        )
    }
    
    /**
     * Validate cognitive load of a screen
     */
    fun validateCognitiveLoad(
        itemCount: Int,
        decisionPoints: Int,
        colorCount: Int
    ): ValidationResult {
        val issues = mutableListOf<String>()
        val recommendations = mutableListOf<String>()
        
        if (itemCount > Standards.MAX_ITEMS_PER_SCREEN) {
            issues.add("Too many items on screen: $itemCount > ${Standards.MAX_ITEMS_PER_SCREEN}")
            recommendations.add("Consider pagination or grouping items")
        }
        
        if (decisionPoints > Standards.MAX_DECISION_OPTIONS) {
            issues.add("Too many decision points: $decisionPoints > ${Standards.MAX_DECISION_OPTIONS}")
            recommendations.add("Simplify choices or use progressive disclosure")
        }
        
        if (colorCount > Standards.MAX_COLORS_PER_SCREEN) {
            issues.add("Too many colors: $colorCount > ${Standards.MAX_COLORS_PER_SCREEN}")
            recommendations.add("Reduce color palette for better focus")
        }
        
        val score = when {
            issues.isEmpty() -> 100
            issues.size == 1 -> 70
            issues.size == 2 -> 40
            else -> 10
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            recommendations = recommendations,
            score = score
        )
    }
    
    /**
     * Validate ADHD-friendly feedback mechanisms
     */
    fun validateFeedback(
        hasImmediateFeedback: Boolean,
        hasPositiveReinforcement: Boolean,
        hasProgressIndicators: Boolean,
        hasErrorRecovery: Boolean
    ): ValidationResult {
        val issues = mutableListOf<String>()
        val recommendations = mutableListOf<String>()
        var score = 100
        
        if (!hasImmediateFeedback) {
            issues.add("Missing immediate feedback for user actions")
            recommendations.add("Add visual/haptic feedback within ${Standards.FEEDBACK_DELAY_MS}ms")
            score -= 25
        }
        
        if (!hasPositiveReinforcement) {
            recommendations.add("Consider adding positive reinforcement messages")
            score -= 15
        }
        
        if (!hasProgressIndicators) {
            issues.add("Missing progress indicators for multi-step processes")
            recommendations.add("Add progress bars or step indicators")
            score -= 25
        }
        
        if (!hasErrorRecovery) {
            issues.add("Missing clear error recovery options")
            recommendations.add("Provide clear error messages and recovery actions")
            score -= 35
        }
        
        return ValidationResult(
            isValid = issues.isEmpty(),
            issues = issues,
            recommendations = recommendations,
            score = maxOf(0, score)
        )
    }
    
    /**
     * Calculate contrast ratio between two colors
     */
    private fun calculateContrastRatio(color1: Color, color2: Color): Float {
        val l1 = getRelativeLuminance(color1)
        val l2 = getRelativeLuminance(color2)
        val lighter = maxOf(l1, l2)
        val darker = minOf(l1, l2)
        return (lighter + 0.05f) / (darker + 0.05f)
    }
    
    /**
     * Calculate relative luminance of a color
     */
    private fun getRelativeLuminance(color: Color): Float {
        val r = if (color.red <= 0.03928f) color.red / 12.92f else ((color.red + 0.055f) / 1.055f).pow(2.4f)
        val g = if (color.green <= 0.03928f) color.green / 12.92f else ((color.green + 0.055f) / 1.055f).pow(2.4f)
        val b = if (color.blue <= 0.03928f) color.blue / 12.92f else ((color.blue + 0.055f) / 1.055f).pow(2.4f)
        return 0.2126f * r + 0.7152f * g + 0.0722f * b
    }
    
    /**
     * Generate comprehensive ADHD-friendliness report
     */
    fun generateADHDReport(
        touchTargets: List<Dp>,
        colorPairs: List<Pair<Color, Color>>,
        itemCount: Int,
        decisionPoints: Int,
        colorCount: Int,
        feedbackFeatures: Map<String, Boolean>
    ): Map<String, Any> {
        val touchTargetResults = touchTargets.map { validateTouchTarget(it) }
        val contrastResults = colorPairs.map { validateColorContrast(it.first, it.second) }
        val cognitiveLoadResult = validateCognitiveLoad(itemCount, decisionPoints, colorCount)
        val feedbackResult = validateFeedback(
            feedbackFeatures["immediateFeedback"] ?: false,
            feedbackFeatures["positiveReinforcement"] ?: false,
            feedbackFeatures["progressIndicators"] ?: false,
            feedbackFeatures["errorRecovery"] ?: false
        )
        
        val overallScore = listOf(
            touchTargetResults.map { it.score }.average(),
            contrastResults.map { it.score }.average(),
            cognitiveLoadResult.score.toDouble(),
            feedbackResult.score.toDouble()
        ).average().toInt()
        
        return mapOf(
            "overallScore" to overallScore,
            "touchTargetScore" to touchTargetResults.map { it.score }.average().toInt(),
            "contrastScore" to contrastResults.map { it.score }.average().toInt(),
            "cognitiveLoadScore" to cognitiveLoadResult.score,
            "feedbackScore" to feedbackResult.score,
            "issues" to (touchTargetResults + contrastResults + listOf(cognitiveLoadResult, feedbackResult))
                .flatMap { it.issues },
            "recommendations" to (touchTargetResults + contrastResults + listOf(cognitiveLoadResult, feedbackResult))
                .flatMap { it.recommendations }
        )
    }
}

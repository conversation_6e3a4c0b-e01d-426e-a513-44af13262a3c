package com.focusflow.data.repository

import com.focusflow.data.dao.TaskDao
import com.focusflow.data.model.Task
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TaskRepository @Inject constructor(
    private val taskDao: TaskDao
) {
    
    fun getAllTasks(): Flow<List<Task>> = taskDao.getAllTasks()
    
    fun getIncompleteTasks(): Flow<List<Task>> = taskDao.getIncompleteTasks()
    
    fun getCompletedTasks(): Flow<List<Task>> = taskDao.getCompletedTasks()
    
    fun getTasksByCategory(category: String): Flow<List<Task>> = 
        taskDao.getTasksByCategory(category)
    
    fun getTasksByDueDateRange(startDate: LocalDateTime, endDate: LocalDateTime): Flow<List<Task>> = 
        taskDao.getTasksByDueDateRange(startDate, endDate)
    
    fun getOverdueTasks(): Flow<List<Task>> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        return taskDao.getOverdueTasks(now)
    }
    
    suspend fun insertTask(task: Task): Long = taskDao.insertTask(task)
    
    suspend fun updateTask(task: Task) = taskDao.updateTask(task)
    
    suspend fun deleteTask(task: Task) = taskDao.deleteTask(task)
    
    suspend fun markTaskAsCompleted(taskId: Long) {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        taskDao.markTaskAsCompleted(taskId, now)
    }
    
    suspend fun getIncompleteTaskCount(): Int = taskDao.getIncompleteTaskCount()
    
    suspend fun getCompletedTaskCountInPeriod(startDate: LocalDateTime, endDate: LocalDateTime): Int = 
        taskDao.getCompletedTaskCountInPeriod(startDate, endDate)
    
    // Convenience methods for common task operations
    suspend fun createTask(
        title: String,
        description: String? = null,
        dueDate: LocalDateTime? = null,
        priority: String = "medium",
        category: String? = null,
        estimatedDuration: Int? = null
    ): Long {
        val task = Task(
            title = title,
            description = description,
            dueDate = dueDate,
            priority = priority,
            category = category,
            estimatedDuration = estimatedDuration,
            createdAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        )
        return insertTask(task)
    }
    
    suspend fun createRecurringTask(
        title: String,
        description: String? = null,
        priority: String = "medium",
        category: String? = null,
        frequency: String = "daily",
        estimatedDuration: Int? = null
    ): Long {
        val nextDueDate = calculateNextDueDate(frequency)
        val task = Task(
            title = title,
            description = description,
            dueDate = nextDueDate,
            priority = priority,
            category = category,
            isRecurring = true,
            recurringFrequency = frequency,
            estimatedDuration = estimatedDuration,
            createdAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        )
        return insertTask(task)
    }
    
    suspend fun completeTask(task: Task) {
        val completedTask = task.copy(
            isCompleted = true,
            completedAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        )
        updateTask(completedTask)
        
        // If it's a recurring task, create the next instance
        if (task.isRecurring && task.recurringFrequency != null) {
            createNextRecurringTask(task)
        }
    }
    
    private suspend fun createNextRecurringTask(originalTask: Task) {
        val nextDueDate = calculateNextDueDate(originalTask.recurringFrequency!!)
        val nextTask = originalTask.copy(
            id = 0, // New task
            isCompleted = false,
            completedAt = null,
            dueDate = nextDueDate,
            createdAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        )
        insertTask(nextTask)
    }
    
    private fun calculateNextDueDate(frequency: String): LocalDateTime {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        return when (frequency) {
            "daily" -> now.plus(1, DateTimeUnit.DAY)
            "weekly" -> now.plus(7, DateTimeUnit.DAY)
            "monthly" -> now.plus(1, DateTimeUnit.MONTH)
            else -> now.plus(1, DateTimeUnit.DAY)
        }
    }
    
    // Get today's tasks
    fun getTodaysTasks(): Flow<List<Task>> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val startOfDay = today.atStartOfDayIn(TimeZone.currentSystemDefault()).toLocalDateTime(TimeZone.currentSystemDefault())
        val endOfDay = today.plus(1, DateTimeUnit.DAY).atStartOfDayIn(TimeZone.currentSystemDefault()).toLocalDateTime(TimeZone.currentSystemDefault())
        return getTasksByDueDateRange(startOfDay, endOfDay)
    }
    
    // Get this week's tasks
    fun getThisWeeksTasks(): Flow<List<Task>> {
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
        val startOfWeek = today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)
        val endOfWeek = startOfWeek.plus(7, DateTimeUnit.DAY)
        
        val startDateTime = startOfWeek.atStartOfDayIn(TimeZone.currentSystemDefault()).toLocalDateTime(TimeZone.currentSystemDefault())
        val endDateTime = endOfWeek.atStartOfDayIn(TimeZone.currentSystemDefault()).toLocalDateTime(TimeZone.currentSystemDefault())
        
        return getTasksByDueDateRange(startDateTime, endDateTime)
    }
    
    // Get task statistics
    suspend fun getTaskStatistics(): TaskStatistics {
        val totalTasks = getAllTasks().first().size
        val incompleteTasks = getIncompleteTaskCount()
        val completedTasks = totalTasks - incompleteTasks
        
        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val weekAgo = today.minus(7, DateTimeUnit.DAY)
        val completedThisWeek = getCompletedTaskCountInPeriod(weekAgo, today)
        
        val overdueTasks = getOverdueTasks().first().size
        
        return TaskStatistics(
            totalTasks = totalTasks,
            completedTasks = completedTasks,
            incompleteTasks = incompleteTasks,
            overdueTasks = overdueTasks,
            completedThisWeek = completedThisWeek,
            completionRate = if (totalTasks > 0) (completedTasks.toDouble() / totalTasks * 100) else 0.0
        )
    }
    
    // Get tasks by priority
    fun getTasksByPriority(priority: String): Flow<List<Task>> {
        return getAllTasks().map { tasks ->
            tasks.filter { it.priority == priority && !it.isCompleted }
                .sortedBy { it.dueDate }
        }
    }
    
    // Search tasks
    fun searchTasks(query: String): Flow<List<Task>> {
        return getAllTasks().map { tasks ->
            tasks.filter { task ->
                task.title.contains(query, ignoreCase = true) ||
                task.description?.contains(query, ignoreCase = true) == true ||
                task.category?.contains(query, ignoreCase = true) == true
            }
        }
    }
    
    // Get task categories
    suspend fun getTaskCategories(): List<String> {
        return getAllTasks().first()
            .mapNotNull { it.category }
            .distinct()
            .sorted()
    }
    
    // Bulk operations
    suspend fun markMultipleTasksCompleted(taskIds: List<Long>) {
        taskIds.forEach { taskId ->
            markTaskAsCompleted(taskId)
        }
    }
    
    suspend fun deleteMultipleTasks(taskIds: List<Long>) {
        val tasks = getAllTasks().first().filter { it.id in taskIds }
        tasks.forEach { task ->
            deleteTask(task)
        }
    }
    
    // ADHD-friendly task management
    suspend fun breakDownLargeTask(
        originalTask: Task,
        subtasks: List<String>
    ): List<Long> {
        val subtaskIds = mutableListOf<Long>()
        
        subtasks.forEachIndexed { index, subtaskTitle ->
            val subtask = Task(
                title = subtaskTitle,
                description = "Subtask of: ${originalTask.title}",
                dueDate = originalTask.dueDate,
                priority = originalTask.priority,
                category = originalTask.category,
                estimatedDuration = (originalTask.estimatedDuration ?: 60) / subtasks.size,
                createdAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            )
            subtaskIds.add(insertTask(subtask))
        }
        
        // Mark original task as completed or delete it
        deleteTask(originalTask)
        
        return subtaskIds
    }
    
    // Get recommended tasks based on ADHD principles
    fun getRecommendedTasks(): Flow<List<Task>> {
        return getIncompleteTasks().map { tasks ->
            tasks.filter { task ->
                // Prioritize tasks with shorter estimated duration (easier to start)
                (task.estimatedDuration ?: 60) <= 30 ||
                // High priority tasks
                task.priority == "high" ||
                // Tasks due soon
                task.dueDate?.let { dueDate ->
                    val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                    dueDate <= now.plus(1, DateTimeUnit.DAY)
                } == true
            }.sortedWith(
                compareBy<Task> { it.priority != "high" }
                    .thenBy { it.estimatedDuration ?: 60 }
                    .thenBy { it.dueDate }
            ).take(5)
        }
    }
}

data class TaskStatistics(
    val totalTasks: Int,
    val completedTasks: Int,
    val incompleteTasks: Int,
    val overdueTasks: Int,
    val completedThisWeek: Int,
    val completionRate: Double
)

// Extension function to get Flow.first() safely
private suspend fun <T> Flow<T>.first(): T {
    return kotlinx.coroutines.flow.first()
}

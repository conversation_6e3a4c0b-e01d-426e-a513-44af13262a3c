package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0003H\u0007\u001a.\u0010\u0005\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a.\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0003\u001a.\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00072\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001aB\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u00032\u0006\u0010\u001a\u001a\u00020\u000e2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a\u0010\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u0003H\u0007\u001a \u0010\u001f\u001a\u00020\u00012\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001a\u0016\u0010!\u001a\u00020\u00012\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a\u0010\u0010#\u001a\u00020\u000e2\u0006\u0010\f\u001a\u00020\u0007H\u0002\u00a8\u0006$"}, d2 = {"BudgetWarningCard", "", "remainingBudget", "", "expenseAmount", "CooldownPeriodCard", "hoursRemaining", "", "onRemoveFromCooldown", "Lkotlin/Function0;", "onExtendDelay", "DelayOptionItem", "hours", "label", "", "isSelected", "", "onClick", "DelayPeriodSelector", "selectedHours", "onPeriodSelected", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "ImpulseControlDialog", "amount", "category", "onConfirm", "onCancel", "onDelay", "ImpulseControlQuestions", "MindfulnessBreathingExercise", "onComplete", "SpendingWatchlistCard", "onAddToWatchlist", "getDelayDescription", "app_release"})
public final class ImpulseControlComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void ImpulseControlDialog(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelay) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ImpulseControlQuestions(double amount) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SpendingWatchlistCard(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddToWatchlist) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetWarningCard(double remainingBudget, double expenseAmount) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CooldownPeriodCard(int hoursRemaining, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onRemoveFromCooldown, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onExtendDelay) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DelayPeriodSelector(int selectedHours, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onPeriodSelected, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void DelayOptionItem(int hours, java.lang.String label, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    private static final java.lang.String getDelayDescription(int hours) {
        return null;
    }
    
    @androidx.compose.runtime.Composable
    public static final void MindfulnessBreathingExercise(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onComplete, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
}
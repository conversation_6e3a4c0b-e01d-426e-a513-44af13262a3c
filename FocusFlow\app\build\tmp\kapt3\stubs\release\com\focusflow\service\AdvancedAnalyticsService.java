package com.focusflow.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001:\u0003012B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0002J\u001c\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0002J\u001c\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0002J$\u0010\u000e\u001a\u00020\u000f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\bH\u0002J\"\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u000f0\u00132\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0002J\u0016\u0010\u0015\u001a\u00020\u000f2\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\bH\u0002J\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u001e\u0010\u001d\u001a\u00020\u000f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\u0006\u0010\u001e\u001a\u00020\u000fH\u0002J0\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u000f0\u00132\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\bH\u0002J\u001c\u0010 \u001a\u00020\u000f2\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u000f0\u0013H\u0002J$\u0010\"\u001a\u00020\u000f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\bH\u0002J\"\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u001b\u0012\u0004\u0012\u00020\u000f0\u00132\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0002J\u0016\u0010$\u001a\u00020\u000f2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bH\u0002J\u001c\u0010%\u001a\u00020\u000f2\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u000f0\u0013H\u0002J\u001c\u0010&\u001a\u00020\u000f2\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u000f0\u0013H\u0002J\u001c\u0010\'\u001a\b\u0012\u0004\u0012\u00020\t0\b2\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ.\u0010(\u001a\b\u0012\u0004\u0012\u00020\u001b0\b2\u0006\u0010)\u001a\u00020\u000f2\u0006\u0010*\u001a\u00020\u000f2\u0006\u0010+\u001a\u00020\u000f2\u0006\u0010,\u001a\u00020\u000fH\u0002J\u0016\u0010-\u001a\u00020.2\u0006\u0010\u001a\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ$\u0010/\u001a\b\u0012\u0004\u0012\u00020\u001b0\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\u0006\u0010\u001e\u001a\u00020\u000fH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/focusflow/service/AdvancedAnalyticsService;", "", "database", "Lcom/focusflow/data/database/FocusFlowDatabase;", "performanceService", "Lcom/focusflow/service/PerformanceOptimizationService;", "(Lcom/focusflow/data/database/FocusFlowDatabase;Lcom/focusflow/service/PerformanceOptimizationService;)V", "analyzeAmountPatterns", "", "Lcom/focusflow/service/AdvancedAnalyticsService$BehaviorInsight;", "expenses", "Lcom/focusflow/data/model/Expense;", "analyzeSpendingPatterns", "analyzeTimingPatterns", "calculateBudgetScore", "", "budgetCategories", "Lcom/focusflow/data/model/BudgetCategory;", "calculateDailySpending", "", "Lkotlinx/datetime/LocalDate;", "calculateDebtScore", "creditCards", "Lcom/focusflow/data/model/CreditCard;", "calculateFinancialHealthScore", "Lcom/focusflow/service/AdvancedAnalyticsService$FinancialHealthScore;", "userId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateForecastConfidence", "trend", "calculateHealthTrends", "calculateMonthlyAverage", "dailySpending", "calculateSavingsScore", "calculateSeasonalFactors", "calculateSpendingConsistencyScore", "calculateSpendingTrend", "calculateWeeklyAverage", "generateBehaviorInsights", "generateHealthRecommendations", "budgetScore", "debtScore", "savingsScore", "consistencyScore", "generateSpendingForecast", "Lcom/focusflow/service/AdvancedAnalyticsService$SpendingForecast;", "identifyRiskFactors", "BehaviorInsight", "FinancialHealthScore", "SpendingForecast", "app_release"})
public final class AdvancedAnalyticsService {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.database.FocusFlowDatabase database = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.PerformanceOptimizationService performanceService = null;
    
    @javax.inject.Inject
    public AdvancedAnalyticsService(@org.jetbrains.annotations.NotNull
    com.focusflow.data.database.FocusFlowDatabase database, @org.jetbrains.annotations.NotNull
    com.focusflow.service.PerformanceOptimizationService performanceService) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object calculateFinancialHealthScore(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AdvancedAnalyticsService.FinancialHealthScore> $completion) {
        return null;
    }
    
    private final double calculateBudgetScore(java.util.List<com.focusflow.data.model.Expense> expenses, java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories) {
        return 0.0;
    }
    
    private final double calculateDebtScore(java.util.List<com.focusflow.data.model.CreditCard> creditCards) {
        return 0.0;
    }
    
    private final double calculateSavingsScore(java.util.List<com.focusflow.data.model.Expense> expenses, java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories) {
        return 0.0;
    }
    
    private final double calculateSpendingConsistencyScore(java.util.List<com.focusflow.data.model.Expense> expenses) {
        return 0.0;
    }
    
    private final java.util.List<java.lang.String> generateHealthRecommendations(double budgetScore, double debtScore, double savingsScore, double consistencyScore) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Double> calculateHealthTrends(java.util.List<com.focusflow.data.model.Expense> expenses, java.util.List<com.focusflow.data.model.CreditCard> creditCards) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object generateSpendingForecast(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.AdvancedAnalyticsService.SpendingForecast> $completion) {
        return null;
    }
    
    private final java.util.Map<kotlinx.datetime.LocalDate, java.lang.Double> calculateDailySpending(java.util.List<com.focusflow.data.model.Expense> expenses) {
        return null;
    }
    
    private final double calculateWeeklyAverage(java.util.Map<kotlinx.datetime.LocalDate, java.lang.Double> dailySpending) {
        return 0.0;
    }
    
    private final double calculateMonthlyAverage(java.util.Map<kotlinx.datetime.LocalDate, java.lang.Double> dailySpending) {
        return 0.0;
    }
    
    private final double calculateSpendingTrend(java.util.Map<kotlinx.datetime.LocalDate, java.lang.Double> dailySpending) {
        return 0.0;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Double> calculateSeasonalFactors(java.util.List<com.focusflow.data.model.Expense> expenses) {
        return null;
    }
    
    private final double calculateForecastConfidence(java.util.List<com.focusflow.data.model.Expense> expenses, double trend) {
        return 0.0;
    }
    
    private final java.util.List<java.lang.String> identifyRiskFactors(java.util.List<com.focusflow.data.model.Expense> expenses, double trend) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object generateBehaviorInsights(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight>> $completion) {
        return null;
    }
    
    private final java.util.List<com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight> analyzeSpendingPatterns(java.util.List<com.focusflow.data.model.Expense> expenses) {
        return null;
    }
    
    private final java.util.List<com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight> analyzeTimingPatterns(java.util.List<com.focusflow.data.model.Expense> expenses) {
        return null;
    }
    
    private final java.util.List<com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight> analyzeAmountPatterns(java.util.List<com.focusflow.data.model.Expense> expenses) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0017\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BC\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\bH\u00c6\u0003J\u000f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00030\nH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\fH\u00c6\u0003JU\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u00c6\u0001J\u0013\u0010!\u001a\u00020\b2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00030\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0013\u00a8\u0006&"}, d2 = {"Lcom/focusflow/service/AdvancedAnalyticsService$BehaviorInsight;", "", "insightType", "", "title", "description", "impact", "actionable", "", "suggestedActions", "", "confidence", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/util/List;D)V", "getActionable", "()Z", "getConfidence", "()D", "getDescription", "()Ljava/lang/String;", "getImpact", "getInsightType", "getSuggestedActions", "()Ljava/util/List;", "getTitle", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_release"})
    public static final class BehaviorInsight {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String insightType = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String title = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String description = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String impact = null;
        private final boolean actionable = false;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> suggestedActions = null;
        private final double confidence = 0.0;
        
        public BehaviorInsight(@org.jetbrains.annotations.NotNull
        java.lang.String insightType, @org.jetbrains.annotations.NotNull
        java.lang.String title, @org.jetbrains.annotations.NotNull
        java.lang.String description, @org.jetbrains.annotations.NotNull
        java.lang.String impact, boolean actionable, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> suggestedActions, double confidence) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getInsightType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getImpact() {
            return null;
        }
        
        public final boolean getActionable() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getSuggestedActions() {
            return null;
        }
        
        public final double getConfidence() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component4() {
            return null;
        }
        
        public final boolean component5() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        public final double component7() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.service.AdvancedAnalyticsService.BehaviorInsight copy(@org.jetbrains.annotations.NotNull
        java.lang.String insightType, @org.jetbrains.annotations.NotNull
        java.lang.String title, @org.jetbrains.annotations.NotNull
        java.lang.String description, @org.jetbrains.annotations.NotNull
        java.lang.String impact, boolean actionable, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> suggestedActions, double confidence) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BO\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u0012\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0003J\u0015\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003Ja\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0001J\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\t\u0010%\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000fR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u001d\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017\u00a8\u0006&"}, d2 = {"Lcom/focusflow/service/AdvancedAnalyticsService$FinancialHealthScore;", "", "overallScore", "", "budgetScore", "debtScore", "savingsScore", "spendingConsistencyScore", "recommendations", "", "", "trends", "", "(DDDDDLjava/util/List;Ljava/util/Map;)V", "getBudgetScore", "()D", "getDebtScore", "getOverallScore", "getRecommendations", "()Ljava/util/List;", "getSavingsScore", "getSpendingConsistencyScore", "getTrends", "()Ljava/util/Map;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
    public static final class FinancialHealthScore {
        private final double overallScore = 0.0;
        private final double budgetScore = 0.0;
        private final double debtScore = 0.0;
        private final double savingsScore = 0.0;
        private final double spendingConsistencyScore = 0.0;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> recommendations = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.Map<java.lang.String, java.lang.Double> trends = null;
        
        public FinancialHealthScore(double overallScore, double budgetScore, double debtScore, double savingsScore, double spendingConsistencyScore, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> recommendations, @org.jetbrains.annotations.NotNull
        java.util.Map<java.lang.String, java.lang.Double> trends) {
            super();
        }
        
        public final double getOverallScore() {
            return 0.0;
        }
        
        public final double getBudgetScore() {
            return 0.0;
        }
        
        public final double getDebtScore() {
            return 0.0;
        }
        
        public final double getSavingsScore() {
            return 0.0;
        }
        
        public final double getSpendingConsistencyScore() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getRecommendations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.Map<java.lang.String, java.lang.Double> getTrends() {
            return null;
        }
        
        public final double component1() {
            return 0.0;
        }
        
        public final double component2() {
            return 0.0;
        }
        
        public final double component3() {
            return 0.0;
        }
        
        public final double component4() {
            return 0.0;
        }
        
        public final double component5() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.Map<java.lang.String, java.lang.Double> component7() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.service.AdvancedAnalyticsService.FinancialHealthScore copy(double overallScore, double budgetScore, double debtScore, double savingsScore, double spendingConsistencyScore, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> recommendations, @org.jetbrains.annotations.NotNull
        java.util.Map<java.lang.String, java.lang.Double> trends) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010$\n\u0000\n\u0002\u0010 \n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BG\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00030\t\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\u0015\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00030\tH\u00c6\u0003J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00c6\u0003JW\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\u0014\b\u0002\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00030\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u001f2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\t\u0010#\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001d\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00030\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006$"}, d2 = {"Lcom/focusflow/service/AdvancedAnalyticsService$SpendingForecast;", "", "nextWeekPrediction", "", "nextMonthPrediction", "confidence", "trendDirection", "", "seasonalFactors", "", "riskFactors", "", "(DDDLjava/lang/String;Ljava/util/Map;Ljava/util/List;)V", "getConfidence", "()D", "getNextMonthPrediction", "getNextWeekPrediction", "getRiskFactors", "()Ljava/util/List;", "getSeasonalFactors", "()Ljava/util/Map;", "getTrendDirection", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
    public static final class SpendingForecast {
        private final double nextWeekPrediction = 0.0;
        private final double nextMonthPrediction = 0.0;
        private final double confidence = 0.0;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String trendDirection = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.Map<java.lang.String, java.lang.Double> seasonalFactors = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> riskFactors = null;
        
        public SpendingForecast(double nextWeekPrediction, double nextMonthPrediction, double confidence, @org.jetbrains.annotations.NotNull
        java.lang.String trendDirection, @org.jetbrains.annotations.NotNull
        java.util.Map<java.lang.String, java.lang.Double> seasonalFactors, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> riskFactors) {
            super();
        }
        
        public final double getNextWeekPrediction() {
            return 0.0;
        }
        
        public final double getNextMonthPrediction() {
            return 0.0;
        }
        
        public final double getConfidence() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getTrendDirection() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.Map<java.lang.String, java.lang.Double> getSeasonalFactors() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getRiskFactors() {
            return null;
        }
        
        public final double component1() {
            return 0.0;
        }
        
        public final double component2() {
            return 0.0;
        }
        
        public final double component3() {
            return 0.0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.Map<java.lang.String, java.lang.Double> component5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component6() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.service.AdvancedAnalyticsService.SpendingForecast copy(double nextWeekPrediction, double nextMonthPrediction, double confidence, @org.jetbrains.annotations.NotNull
        java.lang.String trendDirection, @org.jetbrains.annotations.NotNull
        java.util.Map<java.lang.String, java.lang.Double> seasonalFactors, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> riskFactors) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}
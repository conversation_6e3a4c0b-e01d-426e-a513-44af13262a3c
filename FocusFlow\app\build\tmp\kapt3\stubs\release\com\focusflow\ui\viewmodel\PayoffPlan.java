package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u000f\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B+\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u00a2\u0006\u0002\u0010\nJ\u000f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J7\u0010\u0016\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001a\u001a\u00020\u0006H\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000e\u00a8\u0006\u001d"}, d2 = {"Lcom/focusflow/ui/viewmodel/PayoffPlan;", "", "steps", "", "Lcom/focusflow/ui/viewmodel/PayoffStep;", "totalMonths", "", "totalInterestPaid", "", "totalPayments", "(Ljava/util/List;IDD)V", "getSteps", "()Ljava/util/List;", "getTotalInterestPaid", "()D", "getTotalMonths", "()I", "getTotalPayments", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "", "app_release"})
public final class PayoffPlan {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.ui.viewmodel.PayoffStep> steps = null;
    private final int totalMonths = 0;
    private final double totalInterestPaid = 0.0;
    private final double totalPayments = 0.0;
    
    public PayoffPlan(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.viewmodel.PayoffStep> steps, int totalMonths, double totalInterestPaid, double totalPayments) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.ui.viewmodel.PayoffStep> getSteps() {
        return null;
    }
    
    public final int getTotalMonths() {
        return 0;
    }
    
    public final double getTotalInterestPaid() {
        return 0.0;
    }
    
    public final double getTotalPayments() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.ui.viewmodel.PayoffStep> component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.PayoffPlan copy(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.viewmodel.PayoffStep> steps, int totalMonths, double totalInterestPaid, double totalPayments) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/focusflow/ui/viewmodel/TaskFilter;", "", "(Ljava/lang/String;I)V", "ALL", "INCOMPLETE", "COMPLETED", "TODAY", "OVERDUE", "HIGH_PRIORITY", "MEDIUM_PRIORITY", "LOW_PRIORITY", "app_debug"})
public enum TaskFilter {
    /*public static final*/ ALL /* = new ALL() */,
    /*public static final*/ INCOMPLETE /* = new INCOMPLETE() */,
    /*public static final*/ COMPLETED /* = new COMPLETED() */,
    /*public static final*/ TODAY /* = new TODAY() */,
    /*public static final*/ OVERDUE /* = new OVERDUE() */,
    /*public static final*/ HIGH_PRIORITY /* = new HIGH_PRIORITY() */,
    /*public static final*/ MEDIUM_PRIORITY /* = new MEDIUM_PRIORITY() */,
    /*public static final*/ LOW_PRIORITY /* = new LOW_PRIORITY() */;
    
    TaskFilter() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.ui.viewmodel.TaskFilter> getEntries() {
        return null;
    }
}
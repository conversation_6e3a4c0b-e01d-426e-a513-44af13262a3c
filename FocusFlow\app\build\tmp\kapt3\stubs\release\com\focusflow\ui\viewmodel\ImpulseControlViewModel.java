package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006JJ\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00172\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00172\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00172\b\b\u0002\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u001f\u001a\u00020\u0017J \u0010 \u001a\u00020\u00152\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$2\b\u0010%\u001a\u0004\u0018\u00010\u0017J\u000e\u0010&\u001a\u00020\"2\u0006\u0010\'\u001a\u00020\rJ\b\u0010(\u001a\u00020\u0015H\u0002J\u0006\u0010)\u001a\u00020\u0015J\u0006\u0010*\u001a\u00020\u0015J\u0006\u0010+\u001a\u00020\u0015J\u0016\u0010,\u001a\u00020\u00152\u0006\u0010!\u001a\u00020\"2\u0006\u0010-\u001a\u00020\u001eJ\u0016\u0010.\u001a\u00020/2\u0006\u00100\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0017J\f\u00101\u001a\b\u0012\u0004\u0012\u0002020\fJ\u0016\u00103\u001a\u00020\u00152\u0006\u00100\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0017J\b\u00104\u001a\u00020\u0015H\u0002J\"\u00105\u001a\u00020\u00152\u0006\u0010!\u001a\u00020\"2\u0006\u00106\u001a\u00020\u00192\n\b\u0002\u00107\u001a\u0004\u0018\u00010\u0017J\u000e\u00108\u001a\u00020\u00152\u0006\u0010!\u001a\u00020\"J\u0016\u00109\u001a\u00020$2\u0006\u00100\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0017J\u0006\u0010:\u001a\u00020\u0015J\u000e\u0010;\u001a\u00020\u00152\u0006\u0010<\u001a\u00020$J\u000e\u0010=\u001a\u00020\u00152\u0006\u0010<\u001a\u00020$J\u000e\u0010>\u001a\u00020\u00152\u0006\u0010<\u001a\u00020$J\u000e\u0010?\u001a\u00020\u00152\u0006\u0010<\u001a\u00020$J\u000e\u0010@\u001a\u00020\u00152\u0006\u0010A\u001a\u00020\u001eJ\u000e\u0010B\u001a\u00020\u00152\u0006\u0010C\u001a\u00020\u0019R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u001d\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000fR\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Lcom/focusflow/ui/viewmodel/ImpulseControlViewModel;", "Landroidx/lifecycle/ViewModel;", "wishlistRepository", "Lcom/focusflow/data/repository/WishlistRepository;", "purchaseDelayService", "Lcom/focusflow/service/PurchaseDelayService;", "(Lcom/focusflow/data/repository/WishlistRepository;Lcom/focusflow/service/PurchaseDelayService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/ImpulseControlUiState;", "activeDelayItems", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/focusflow/data/model/WishlistItem;", "getActiveDelayItems", "()Lkotlinx/coroutines/flow/StateFlow;", "activeWishlistItems", "getActiveWishlistItems", "uiState", "getUiState", "addItemToWishlist", "", "itemName", "", "estimatedPrice", "", "category", "description", "merchant", "delayPeriodHours", "", "priority", "addReflection", "itemId", "", "stillWanted", "", "notes", "calculateRemainingDelayTime", "item", "checkExpiredDelays", "clearError", "completeBreathingExercise", "dismissBreathingExercise", "extendDelay", "additionalHours", "getBudgetImpactPreview", "Lcom/focusflow/ui/viewmodel/BudgetImpactPreview;", "amount", "getDelayPeriodOptions", "Lcom/focusflow/service/DelayPeriodOption;", "getRecommendedDelayPeriod", "loadDelayStatistics", "markAsPurchased", "actualPrice", "reflectionNotes", "removeFromDelayList", "shouldShowImpulseControl", "startBreathingExercise", "toggleBudgetWarnings", "enabled", "toggleImpulseControl", "toggleReflectionQuestions", "toggleWishlistSuggestions", "updateCoolingOffPeriod", "seconds", "updateSpendingThreshold", "threshold", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class ImpulseControlViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.WishlistRepository wishlistRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.PurchaseDelayService purchaseDelayService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.ImpulseControlUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.ImpulseControlUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.WishlistItem>> activeWishlistItems = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.WishlistItem>> activeDelayItems = null;
    
    @javax.inject.Inject
    public ImpulseControlViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.WishlistRepository wishlistRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.PurchaseDelayService purchaseDelayService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.ImpulseControlUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.WishlistItem>> getActiveWishlistItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.WishlistItem>> getActiveDelayItems() {
        return null;
    }
    
    public final void addItemToWishlist(@org.jetbrains.annotations.NotNull
    java.lang.String itemName, double estimatedPrice, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String merchant, int delayPeriodHours, @org.jetbrains.annotations.NotNull
    java.lang.String priority) {
    }
    
    public final void getRecommendedDelayPeriod(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category) {
    }
    
    public final void extendDelay(long itemId, int additionalHours) {
    }
    
    public final void removeFromDelayList(long itemId) {
    }
    
    public final void markAsPurchased(long itemId, double actualPrice, @org.jetbrains.annotations.Nullable
    java.lang.String reflectionNotes) {
    }
    
    public final void addReflection(long itemId, boolean stillWanted, @org.jetbrains.annotations.Nullable
    java.lang.String notes) {
    }
    
    public final void startBreathingExercise() {
    }
    
    public final void completeBreathingExercise() {
    }
    
    public final void dismissBreathingExercise() {
    }
    
    private final void loadDelayStatistics() {
    }
    
    private final void checkExpiredDelays() {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.DelayPeriodOption> getDelayPeriodOptions() {
        return null;
    }
    
    public final long calculateRemainingDelayTime(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem item) {
        return 0L;
    }
    
    public final boolean shouldShowImpulseControl(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category) {
        return false;
    }
    
    public final void clearError() {
    }
    
    public final void updateSpendingThreshold(double threshold) {
    }
    
    public final void updateCoolingOffPeriod(int seconds) {
    }
    
    public final void toggleImpulseControl(boolean enabled) {
    }
    
    public final void toggleReflectionQuestions(boolean enabled) {
    }
    
    public final void toggleBudgetWarnings(boolean enabled) {
    }
    
    public final void toggleWishlistSuggestions(boolean enabled) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.BudgetImpactPreview getBudgetImpactPreview(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category) {
        return null;
    }
}
package com.focusflow.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006\u0005"}, d2 = {"bottomNavItems", "", "Lcom/focusflow/navigation/Screen;", "getBottomNavItems", "()Ljava/util/List;", "app_release"})
public final class NavigationKt {
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<com.focusflow.navigation.Screen> bottomNavItems = null;
    
    @org.jetbrains.annotations.NotNull
    public static final java.util.List<com.focusflow.navigation.Screen> getBottomNavItems() {
        return null;
    }
}
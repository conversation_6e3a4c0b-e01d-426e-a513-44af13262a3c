package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\u001a\u001e\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a4\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\b2\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u001e\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a4\u0010\u0010\u001a\u00020\u00012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000f0\b2\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u001e\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a>\u0010\u0016\u001a\u00020\u00012\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00150\b2\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\u0019\u001a\u00020\u001aH\u0007\u00a8\u0006\u001b"}, d2 = {"OptimizedBudgetCategoryCard", "", "category", "Lcom/focusflow/data/model/BudgetCategory;", "onClick", "Lkotlin/Function0;", "OptimizedBudgetGrid", "categories", "", "onCategoryClick", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "OptimizedCreditCardItem", "creditCard", "Lcom/focusflow/data/model/CreditCard;", "OptimizedCreditCardList", "creditCards", "onCardClick", "OptimizedExpenseItem", "expense", "Lcom/focusflow/data/model/Expense;", "OptimizedExpenseList", "expenses", "onExpenseClick", "listState", "Landroidx/compose/foundation/lazy/LazyListState;", "app_debug"})
public final class OptimizedComponentsKt {
    
    /**
     * Optimized expense list with lazy loading and minimal recompositions
     */
    @androidx.compose.runtime.Composable
    public static final void OptimizedExpenseList(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Expense> expenses, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.Expense, kotlin.Unit> onExpenseClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull
    androidx.compose.foundation.lazy.LazyListState listState) {
    }
    
    /**
     * Optimized expense item that only recomposes when expense data changes
     */
    @androidx.compose.runtime.Composable
    private static final void OptimizedExpenseItem(com.focusflow.data.model.Expense expense, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * Optimized budget category grid with stable keys
     */
    @androidx.compose.runtime.Composable
    public static final void OptimizedBudgetGrid(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> categories, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.BudgetCategory, kotlin.Unit> onCategoryClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Optimized budget category card with memoized calculations
     */
    @androidx.compose.runtime.Composable
    private static final void OptimizedBudgetCategoryCard(com.focusflow.data.model.BudgetCategory category, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    /**
     * Optimized credit card list for debt screen
     */
    @androidx.compose.runtime.Composable
    public static final void OptimizedCreditCardList(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.CreditCard, kotlin.Unit> onCardClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Optimized credit card item with memoized calculations
     */
    @androidx.compose.runtime.Composable
    private static final void OptimizedCreditCardItem(com.focusflow.data.model.CreditCard creditCard, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
}
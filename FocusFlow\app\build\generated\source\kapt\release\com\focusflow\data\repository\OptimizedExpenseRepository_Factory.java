package com.focusflow.data.repository;

import com.focusflow.data.dao.ExpenseDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OptimizedExpenseRepository_Factory implements Factory<OptimizedExpenseRepository> {
  private final Provider<ExpenseDao> expenseDaoProvider;

  public OptimizedExpenseRepository_Factory(Provider<ExpenseDao> expenseDaoProvider) {
    this.expenseDaoProvider = expenseDaoProvider;
  }

  @Override
  public OptimizedExpenseRepository get() {
    return newInstance(expenseDaoProvider.get());
  }

  public static OptimizedExpenseRepository_Factory create(Provider<ExpenseDao> expenseDaoProvider) {
    return new OptimizedExpenseRepository_Factory(expenseDaoProvider);
  }

  public static OptimizedExpenseRepository newInstance(ExpenseDao expenseDao) {
    return new OptimizedExpenseRepository(expenseDao);
  }
}

package com.focusflow.service

import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI Service interface for handling AI-powered features
 * This interface can be implemented with different AI providers (Claude, OpenAI, etc.)
 */
interface AIService {
    suspend fun generateResponse(
        prompt: String,
        context: String? = null,
        maxTokens: Int = 500
    ): AIResponse
    
    suspend fun generateInsight(
        userContext: UserContext,
        insightType: InsightType
    ): AIResponse
    
    suspend fun analyzeSpending(
        expenses: List<ExpenseData>,
        budget: BudgetData?
    ): AIResponse
    
    suspend fun generateBudgetAdvice(
        currentBudget: BudgetData?,
        spendingHistory: List<ExpenseData>
    ): AIResponse
    
    suspend fun analyzeDebtSituation(
        creditCards: List<CreditCardData>,
        paymentHistory: List<PaymentData>
    ): AIResponse
    
    suspend fun generateTaskBreakdown(
        task: TaskData,
        userPreferences: UserPreferences
    ): List<String>
    
    suspend fun generateMotivationalMessage(
        userProgress: UserProgress,
        recentAchievements: List<Achievement>
    ): AIResponse
}

/**
 * Mock implementation of AIService for development and testing
 * This provides realistic responses without requiring external AI API calls
 */
@Singleton
class MockAIService @Inject constructor() : AIService {
    
    override suspend fun generateResponse(
        prompt: String,
        context: String?,
        maxTokens: Int
    ): AIResponse {
        // Simulate API call delay
        delay(1000 + (Math.random() * 2000).toLong())
        
        val response = when {
            prompt.contains("spending", ignoreCase = true) -> generateSpendingResponse(prompt)
            prompt.contains("budget", ignoreCase = true) -> generateBudgetResponse(prompt)
            prompt.contains("debt", ignoreCase = true) -> generateDebtResponse(prompt)
            prompt.contains("task", ignoreCase = true) -> generateTaskResponse(prompt)
            prompt.contains("progress", ignoreCase = true) -> generateProgressResponse(prompt)
            prompt.contains("insight", ignoreCase = true) -> generateInsightResponse(prompt)
            else -> generateGeneralResponse(prompt)
        }
        
        return AIResponse(
            content = response,
            confidence = 0.85 + (Math.random() * 0.15),
            tokens = response.length / 4, // Rough token estimate
            model = "mock-ai-v1.0"
        )
    }
    
    override suspend fun generateInsight(
        userContext: UserContext,
        insightType: InsightType
    ): AIResponse {
        delay(800)
        
        val insight = when (insightType) {
            InsightType.SPENDING_PATTERN -> generateSpendingPatternInsight(userContext)
            InsightType.BUDGET_OPTIMIZATION -> generateBudgetOptimizationInsight(userContext)
            InsightType.DEBT_STRATEGY -> generateDebtStrategyInsight(userContext)
            InsightType.HABIT_IMPROVEMENT -> generateHabitImprovementInsight(userContext)
            InsightType.GOAL_PROGRESS -> generateGoalProgressInsight(userContext)
        }
        
        return AIResponse(
            content = insight,
            confidence = 0.9,
            tokens = insight.length / 4,
            model = "mock-ai-v1.0"
        )
    }
    
    override suspend fun analyzeSpending(
        expenses: List<ExpenseData>,
        budget: BudgetData?
    ): AIResponse {
        delay(1200)
        
        val totalSpent = expenses.sumOf { it.amount }
        val categoryBreakdown = expenses.groupBy { it.category }
            .mapValues { it.value.sumOf { expense -> expense.amount } }
        val topCategory = categoryBreakdown.maxByOrNull { it.value }
        
        val analysis = """
            📊 **Your Spending Analysis**
            
            **This Week's Summary:**
            • Total Spent: $${String.format("%.2f", totalSpent)}
            • Transactions: ${expenses.size}
            • Top Category: ${topCategory?.key ?: "None"} ($${String.format("%.2f", topCategory?.value ?: 0.0)})
            
            **ADHD-Friendly Insights:**
            ${if (totalSpent > (budget?.weeklyAmount ?: 300.0)) {
                "⚠️ You're over your weekly budget. Try the '24-hour rule' for non-essential purchases."
            } else {
                "✅ Great job staying within budget! You have $${String.format("%.2f", (budget?.weeklyAmount ?: 300.0) - totalSpent)} left this week."
            }}
            
            **Quick Tips:**
            • Set up spending alerts for your top categories
            • Use the envelope method for better control
            • Celebrate small wins - you're building great habits! 🎉
        """.trimIndent()
        
        return AIResponse(
            content = analysis,
            confidence = 0.92,
            tokens = analysis.length / 4,
            model = "mock-ai-v1.0"
        )
    }
    
    override suspend fun generateBudgetAdvice(
        currentBudget: BudgetData?,
        spendingHistory: List<ExpenseData>
    ): AIResponse {
        delay(1000)
        
        val advice = if (currentBudget == null) {
            """
            📊 **Let's Create Your First Budget!**
            
            Based on your spending patterns, here's my ADHD-friendly recommendation:
            
            **Start Simple with 3 Categories:**
            1. **Essentials** (50%): Rent, groceries, utilities
            2. **Fun** (30%): Entertainment, dining out, hobbies  
            3. **Savings** (20%): Emergency fund, debt payments
            
            **ADHD Success Tips:**
            • Use visual budgeting (our envelope system)
            • Start with weekly budgets (easier to track)
            • Automate everything possible
            • Celebrate when you stick to it! 🎉
            
            Ready to set up your first budget? I'll guide you through it step by step.
            """
        } else {
            val avgSpending = spendingHistory.sumOf { it.amount } / maxOf(1, spendingHistory.size)
            """
            📊 **Budget Optimization Suggestions**
            
            **Current Performance:**
            • Weekly Budget: $${String.format("%.2f", currentBudget.weeklyAmount)}
            • Average Spending: $${String.format("%.2f", avgSpending)}
            
            **Recommendations:**
            ${if (avgSpending > currentBudget.weeklyAmount) {
                "• Consider increasing your budget or finding areas to cut back\n• Focus on your biggest spending category first"
            } else {
                "• Great job staying under budget!\n• Consider allocating extra funds to savings or debt payoff"
            }}
            
            **ADHD-Friendly Adjustments:**
            • Round numbers are easier to remember
            • Use the 50/30/20 rule as a starting point
            • Set up automatic transfers for savings
            • Review and adjust monthly, not daily
            """
        }
        
        return AIResponse(
            content = advice.trimIndent(),
            confidence = 0.88,
            tokens = advice.length / 4,
            model = "mock-ai-v1.0"
        )
    }
    
    override suspend fun analyzeDebtSituation(
        creditCards: List<CreditCardData>,
        paymentHistory: List<PaymentData>
    ): AIResponse {
        delay(1100)
        
        if (creditCards.isEmpty()) {
            return AIResponse(
                content = """
                    💳 **Debt-Free Status!**
                    
                    Congratulations! You don't have any credit card debt right now. Here's how to stay debt-free:
                    
                    **ADHD-Friendly Prevention:**
                    • Use the envelope budgeting method
                    • Set up automatic payments for any future cards
                    • Create a "fun money" category for impulse purchases
                    • Use the 24-hour rule for purchases over $50
                    
                    Keep up the excellent work! 🎉
                """.trimIndent(),
                confidence = 0.95,
                tokens = 100,
                model = "mock-ai-v1.0"
            )
        }
        
        val totalDebt = creditCards.sumOf { it.balance }
        val totalMinPayments = creditCards.sumOf { it.minimumPayment }
        val highestRate = creditCards.maxOfOrNull { it.interestRate } ?: 0.0
        val smallestDebt = creditCards.minByOrNull { it.balance }
        val highestRateCard = creditCards.maxByOrNull { it.interestRate }
        
        val analysis = """
            💳 **Your Debt Analysis**
            
            **Current Situation:**
            • Total Debt: $${String.format("%.2f", totalDebt)}
            • Monthly Minimums: $${String.format("%.2f", totalMinPayments)}
            • Highest Interest Rate: ${String.format("%.1f", highestRate)}%
            
            **ADHD-Friendly Strategy Recommendations:**
            
            **🎯 Snowball Method (Motivation-focused):**
            Start with: ${smallestDebt?.name} ($${String.format("%.2f", smallestDebt?.balance ?: 0.0)})
            • Quick wins build momentum
            • Great for ADHD motivation
            • Celebrate each payoff! 🎉
            
            **📊 Avalanche Method (Math-focused):**
            Start with: ${highestRateCard?.name} (${String.format("%.1f", highestRateCard?.interestRate ?: 0.0)}% APR)
            • Saves more money long-term
            • Better if you're motivated by numbers
            
            **Next Steps:**
            1. Choose your strategy (I recommend Snowball for ADHD)
            2. Set up automatic minimum payments
            3. Find extra money in your budget
            4. Use our Payoff Planner to see your debt-free date!
        """.trimIndent()
        
        return AIResponse(
            content = analysis,
            confidence = 0.91,
            tokens = analysis.length / 4,
            model = "mock-ai-v1.0"
        )
    }
    
    override suspend fun generateTaskBreakdown(
        task: TaskData,
        userPreferences: UserPreferences
    ): List<String> {
        delay(800)
        
        // Generate ADHD-friendly task breakdown
        val subtasks = when {
            task.title.contains("budget", ignoreCase = true) -> listOf(
                "Gather last month's bank statements",
                "List all income sources",
                "Categorize expenses into needs vs wants",
                "Set up 3 main budget categories",
                "Choose budgeting app or method"
            )
            task.title.contains("debt", ignoreCase = true) -> listOf(
                "List all credit cards and balances",
                "Note minimum payments and due dates",
                "Choose debt payoff strategy (snowball vs avalanche)",
                "Set up automatic minimum payments",
                "Find extra money in budget for debt payments"
            )
            task.title.contains("save", ignoreCase = true) -> listOf(
                "Open a separate savings account",
                "Set up automatic transfer",
                "Determine savings goal amount",
                "Choose savings frequency (weekly/monthly)",
                "Track progress monthly"
            )
            else -> listOf(
                "Break down into smaller steps",
                "Set a specific deadline",
                "Gather necessary resources",
                "Start with the easiest part",
                "Schedule time to work on it"
            )
        }
        
        return subtasks.take(userPreferences.maxSubtasks ?: 5)
    }
    
    override suspend fun generateMotivationalMessage(
        userProgress: UserProgress,
        recentAchievements: List<Achievement>
    ): AIResponse {
        delay(600)
        
        val messages = listOf(
            "🌟 You're building amazing financial habits! Every small step counts toward your goals.",
            "💪 Progress over perfection! You're doing better than you think.",
            "🎯 Consistency beats perfection every time. Keep up the great work!",
            "🚀 You're on fire! Your dedication to tracking is paying off.",
            "🏆 Look how far you've come! Your future self will thank you.",
            "✨ Small daily actions create big life changes. You've got this!",
            "🎉 Celebrating your wins, big and small! You're crushing your goals.",
            "💎 You're turning good intentions into lasting habits. Amazing work!"
        )
        
        val selectedMessage = if (recentAchievements.isNotEmpty()) {
            "🎉 Congratulations on your recent achievements! ${messages.random()}"
        } else {
            messages.random()
        }
        
        return AIResponse(
            content = selectedMessage,
            confidence = 0.95,
            tokens = selectedMessage.length / 4,
            model = "mock-ai-v1.0"
        )
    }
    
    // Helper methods for generating specific types of responses
    private fun generateSpendingResponse(prompt: String): String {
        return "Based on your spending patterns, I notice you're doing well with tracking! Here are some personalized insights to help you optimize your financial habits..."
    }
    
    private fun generateBudgetResponse(prompt: String): String {
        return "Let's work on your budget together! For ADHD-friendly budgeting, I recommend starting with simple categories and visual tracking..."
    }
    
    private fun generateDebtResponse(prompt: String): String {
        return "I can help you create a debt payoff strategy that works with your ADHD. Let's focus on building momentum with quick wins..."
    }
    
    private fun generateTaskResponse(prompt: String): String {
        return "Breaking down tasks into smaller pieces is perfect for ADHD! Let me help you create manageable steps..."
    }
    
    private fun generateProgressResponse(prompt: String): String {
        return "You're making excellent progress! Let me show you how far you've come and what's next on your financial journey..."
    }
    
    private fun generateInsightResponse(prompt: String): String {
        return "Here's a personalized insight based on your financial data: You're building strong habits that will serve you well long-term..."
    }
    
    private fun generateGeneralResponse(prompt: String): String {
        return "I'm here to help with your financial journey! Whether it's budgeting, debt management, or building better habits, I've got ADHD-friendly strategies for you..."
    }
    
    private fun generateSpendingPatternInsight(context: UserContext): String {
        return "💡 I notice you tend to spend more on weekends. Consider setting aside a specific 'weekend fun budget' to stay on track while still enjoying yourself!"
    }
    
    private fun generateBudgetOptimizationInsight(context: UserContext): String {
        return "🎯 Your budget is working well! Consider automating your savings to make it even easier to stick to your goals."
    }
    
    private fun generateDebtStrategyInsight(context: UserContext): String {
        return "💪 Focus on paying off your smallest debt first - the psychological win will motivate you to tackle the larger ones!"
    }
    
    private fun generateHabitImprovementInsight(context: UserContext): String {
        return "🌟 You're building great tracking habits! Try linking expense logging to an existing habit like your morning coffee."
    }
    
    private fun generateGoalProgressInsight(context: UserContext): String {
        return "🚀 You're 30% closer to your financial goals than last month! Keep up the momentum with small, consistent actions."
    }
}

// Data classes for AI service
data class AIResponse(
    val content: String,
    val confidence: Double,
    val tokens: Int,
    val model: String,
    val timestamp: Long = System.currentTimeMillis()
)

data class UserContext(
    val userId: String,
    val preferences: UserPreferences,
    val recentActivity: List<String>,
    val goals: List<String>
)

data class ExpenseData(
    val amount: Double,
    val category: String,
    val date: String,
    val description: String?
)

data class BudgetData(
    val weeklyAmount: Double,
    val monthlyAmount: Double,
    val categories: Map<String, Double>
)

data class CreditCardData(
    val name: String,
    val balance: Double,
    val minimumPayment: Double,
    val interestRate: Double,
    val dueDate: String
)

data class PaymentData(
    val amount: Double,
    val date: String,
    val cardName: String
)

data class TaskData(
    val title: String,
    val description: String?,
    val estimatedDuration: Int?,
    val priority: String
)

data class UserPreferences(
    val maxSubtasks: Int? = 5,
    val preferredResponseLength: String = "medium",
    val motivationStyle: String = "encouraging"
)

data class UserProgress(
    val level: Int,
    val experience: Int,
    val streaks: Map<String, Int>,
    val completedGoals: Int
)

data class Achievement(
    val id: String,
    val title: String,
    val description: String,
    val earnedDate: String
)

enum class InsightType {
    SPENDING_PATTERN,
    BUDGET_OPTIMIZATION,
    DEBT_STRATEGY,
    HABIT_IMPROVEMENT,
    GOAL_PROGRESS
}

package com.focusflow.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\b\b\u0007\u0018\u0000 52\u00020\u0001:\u00015B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006JR\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\n2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0013J(\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\b2\u0006\u0010\u0017\u001a\u00020\u00152\b\u0010\u0018\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010\u0019J\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001c0\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u001e\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010 J\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u001bJ\u000e\u0010#\u001a\u00020$H\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u001e\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010(J*\u0010)\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\b2\u0006\u0010*\u001a\u00020\f2\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010,J\u000e\u0010-\u001a\u00020.H\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u0016\u0010/\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u00100J(\u00101\u001a\u00020.2\u0006\u0010\u0016\u001a\u00020\b2\u0006\u00102\u001a\u00020\u00112\b\b\u0002\u00103\u001a\u00020\u0015H\u0082@\u00a2\u0006\u0002\u00104R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/focusflow/service/PurchaseDelayService;", "", "wishlistRepository", "Lcom/focusflow/data/repository/WishlistRepository;", "notificationService", "Lcom/focusflow/service/NotificationService;", "(Lcom/focusflow/data/repository/WishlistRepository;Lcom/focusflow/service/NotificationService;)V", "addItemToDelayList", "", "itemName", "", "estimatedPrice", "", "category", "description", "merchant", "delayPeriodHours", "", "priority", "(Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addReflection", "", "itemId", "stillWanted", "notes", "(JZLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkExpiredDelays", "", "Lcom/focusflow/data/model/WishlistItem;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extendDelay", "additionalHours", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getDelayPeriodOptions", "Lcom/focusflow/service/DelayPeriodOption;", "getDelayStatistics", "Lcom/focusflow/service/DelayStatistics;", "getRecommendedDelayPeriod", "Lcom/focusflow/service/DelayRecommendation;", "amount", "(DLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markAsPurchased", "actualPrice", "reflectionNotes", "(JDLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processExpiredDelays", "", "removeFromDelayList", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "scheduleDelayEndNotification", "delayHours", "isExtension", "(JIZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_release"})
public final class PurchaseDelayService {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.WishlistRepository wishlistRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.NotificationService notificationService = null;
    public static final long DELAY_5_MINUTES = 300000L;
    public static final long DELAY_30_MINUTES = 1800000L;
    public static final long DELAY_24_HOURS = 86400000L;
    public static final long DELAY_48_HOURS = 172800000L;
    public static final long DELAY_1_WEEK = 604800000L;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.PurchaseDelayService.Companion Companion = null;
    
    @javax.inject.Inject
    public PurchaseDelayService(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.WishlistRepository wishlistRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.NotificationService notificationService) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object addItemToDelayList(@org.jetbrains.annotations.NotNull
    java.lang.String itemName, double estimatedPrice, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String merchant, int delayPeriodHours, @org.jetbrains.annotations.NotNull
    java.lang.String priority, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getRecommendedDelayPeriod(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.DelayRecommendation> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object checkExpiredDelays(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.WishlistItem>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object processExpiredDelays(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object extendDelay(long itemId, int additionalHours, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object removeFromDelayList(long itemId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object markAsPurchased(long itemId, double actualPrice, @org.jetbrains.annotations.Nullable
    java.lang.String reflectionNotes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object addReflection(long itemId, boolean stillWanted, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getDelayStatistics(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.DelayStatistics> $completion) {
        return null;
    }
    
    private final java.lang.Object scheduleDelayEndNotification(long itemId, int delayHours, boolean isExtension, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.DelayPeriodOption> getDelayPeriodOptions() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/focusflow/service/PurchaseDelayService$Companion;", "", "()V", "DELAY_1_WEEK", "", "DELAY_24_HOURS", "DELAY_30_MINUTES", "DELAY_48_HOURS", "DELAY_5_MINUTES", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
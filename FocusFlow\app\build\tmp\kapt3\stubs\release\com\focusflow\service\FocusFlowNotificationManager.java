package com.focusflow.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\u0010\b\n\u0002\b\u0010\b\u0007\u0018\u0000 #2\u00020\u0001:\u0001#B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\t\u001a\u00020\nJ\u0006\u0010\u000b\u001a\u00020\nJ\b\u0010\f\u001a\u00020\nH\u0002J$\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013J\u0016\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0014J\u001e\u0010\u0018\u001a\u00020\n2\u0006\u0010\u0019\u001a\u00020\u000f2\u0006\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u0014J\u0006\u0010\u001a\u001a\u00020\nJ\u0016\u0010\u001b\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\u001d\u001a\u00020\u000fJ\u000e\u0010\u001e\u001a\u00020\n2\u0006\u0010\u001d\u001a\u00020\u000fJ2\u0010\u001f\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u000f2\u0006\u0010\u001d\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u00142\b\b\u0002\u0010\"\u001a\u00020\u0014H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/focusflow/service/FocusFlowNotificationManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "alarmManager", "Landroid/app/AlarmManager;", "notificationManager", "Landroidx/core/app/NotificationManagerCompat;", "cancelAllNotifications", "", "cancelDailySpendingReminder", "createNotificationChannels", "scheduleBillReminder", "billName", "", "dueDate", "", "daysBeforeList", "", "", "scheduleDailySpendingReminder", "hour", "minute", "scheduleHabitReminder", "habitName", "scheduleWeeklyBudgetSummary", "showAchievementNotification", "title", "message", "showBudgetWarning", "showNotification", "channelId", "notificationId", "priority", "Companion", "app_release"})
public final class FocusFlowNotificationManager {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String CHANNEL_SPENDING = "spending_notifications";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String CHANNEL_BILLS = "bill_notifications";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String CHANNEL_HABITS = "habit_notifications";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String CHANNEL_ACHIEVEMENTS = "achievement_notifications";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String CHANNEL_BUDGET = "budget_notifications";
    public static final int NOTIFICATION_DAILY_SPENDING = 1001;
    public static final int NOTIFICATION_BILL_REMINDER = 1002;
    public static final int NOTIFICATION_HABIT_REMINDER = 1003;
    public static final int NOTIFICATION_ACHIEVEMENT = 1004;
    public static final int NOTIFICATION_BUDGET_SUMMARY = 1005;
    public static final int NOTIFICATION_BUDGET_WARNING = 1006;
    public static final int REQUEST_DAILY_SPENDING = 2001;
    public static final int REQUEST_BILL_REMINDER = 2002;
    public static final int REQUEST_HABIT_REMINDER = 2003;
    public static final int REQUEST_BUDGET_SUMMARY = 2004;
    @org.jetbrains.annotations.NotNull
    private final androidx.core.app.NotificationManagerCompat notificationManager = null;
    @org.jetbrains.annotations.NotNull
    private final android.app.AlarmManager alarmManager = null;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.FocusFlowNotificationManager.Companion Companion = null;
    
    @javax.inject.Inject
    public FocusFlowNotificationManager(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super();
    }
    
    private final void createNotificationChannels() {
    }
    
    public final void scheduleDailySpendingReminder(int hour, int minute) {
    }
    
    public final void scheduleBillReminder(@org.jetbrains.annotations.NotNull
    java.lang.String billName, long dueDate, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.Integer> daysBeforeList) {
    }
    
    public final void scheduleWeeklyBudgetSummary() {
    }
    
    public final void scheduleHabitReminder(@org.jetbrains.annotations.NotNull
    java.lang.String habitName, int hour, int minute) {
    }
    
    public final void showAchievementNotification(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    public final void showBudgetWarning(@org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    private final void showNotification(java.lang.String title, java.lang.String message, java.lang.String channelId, int notificationId, int priority) {
    }
    
    public final void cancelDailySpendingReminder() {
    }
    
    public final void cancelAllNotifications() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\nX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/focusflow/service/FocusFlowNotificationManager$Companion;", "", "()V", "CHANNEL_ACHIEVEMENTS", "", "CHANNEL_BILLS", "CHANNEL_BUDGET", "CHANNEL_HABITS", "CHANNEL_SPENDING", "NOTIFICATION_ACHIEVEMENT", "", "NOTIFICATION_BILL_REMINDER", "NOTIFICATION_BUDGET_SUMMARY", "NOTIFICATION_BUDGET_WARNING", "NOTIFICATION_DAILY_SPENDING", "NOTIFICATION_HABIT_REMINDER", "REQUEST_BILL_REMINDER", "REQUEST_BUDGET_SUMMARY", "REQUEST_DAILY_SPENDING", "REQUEST_HABIT_REMINDER", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}
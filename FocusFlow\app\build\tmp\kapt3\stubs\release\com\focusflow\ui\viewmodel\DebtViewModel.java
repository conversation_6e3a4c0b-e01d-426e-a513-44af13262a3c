package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\t\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J6\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u001b2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001bJ&\u0010!\u001a\u00020\u00072\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u001bH\u0002J\u001e\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u001b2\u0006\u0010)\u001a\u00020\u001b2\u0006\u0010 \u001a\u00020\u001bJ\u0006\u0010*\u001a\u00020\u0017J\u000e\u0010+\u001a\u00020\u00172\u0006\u0010,\u001a\u00020\rJ\u0018\u0010-\u001a\u00020\u00172\u0006\u0010#\u001a\u00020$2\b\b\u0002\u0010%\u001a\u00020\u001bJ\b\u0010.\u001a\u00020\u0017H\u0002J\u0016\u0010/\u001a\u00020\u00172\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u00020\u001bJ\u000e\u00103\u001a\u00020\u00172\u0006\u0010,\u001a\u00020\rR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013\u00a8\u00064"}, d2 = {"Lcom/focusflow/ui/viewmodel/DebtViewModel;", "Landroidx/lifecycle/ViewModel;", "creditCardRepository", "Lcom/focusflow/data/repository/CreditCardRepository;", "(Lcom/focusflow/data/repository/CreditCardRepository;)V", "_payoffPlan", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/PayoffPlan;", "_uiState", "Lcom/focusflow/ui/viewmodel/DebtUiState;", "allCreditCards", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/focusflow/data/model/CreditCard;", "getAllCreditCards", "()Lkotlinx/coroutines/flow/Flow;", "payoffPlan", "Lkotlinx/coroutines/flow/StateFlow;", "getPayoffPlan", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "addCreditCard", "", "name", "", "currentBalance", "", "creditLimit", "minimumPayment", "dueDate", "Lkotlinx/datetime/LocalDate;", "interestRate", "calculateDetailedPayoffPlan", "cards", "strategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "extraPayment", "calculatePayoffTime", "Lcom/focusflow/ui/viewmodel/PayoffCalculation;", "balance", "monthlyPayment", "clearError", "deleteCreditCard", "creditCard", "generatePayoffPlan", "loadDebtData", "makePayment", "cardId", "", "paymentAmount", "updateCreditCard", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class DebtViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.CreditCardRepository creditCardRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.DebtUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.DebtUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.PayoffPlan> _payoffPlan = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.PayoffPlan> payoffPlan = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> allCreditCards = null;
    
    @javax.inject.Inject
    public DebtViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.CreditCardRepository creditCardRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.DebtUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.PayoffPlan> getPayoffPlan() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.CreditCard>> getAllCreditCards() {
        return null;
    }
    
    private final void loadDebtData() {
    }
    
    public final void addCreditCard(@org.jetbrains.annotations.NotNull
    java.lang.String name, double currentBalance, double creditLimit, double minimumPayment, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate dueDate, double interestRate) {
    }
    
    public final void updateCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard) {
    }
    
    public final void makePayment(long cardId, double paymentAmount) {
    }
    
    public final void deleteCreditCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.CreditCard creditCard) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.PayoffCalculation calculatePayoffTime(double balance, double monthlyPayment, double interestRate) {
        return null;
    }
    
    public final void generatePayoffPlan(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy strategy, double extraPayment) {
    }
    
    private final com.focusflow.ui.viewmodel.PayoffPlan calculateDetailedPayoffPlan(java.util.List<com.focusflow.data.model.CreditCard> cards, com.focusflow.ui.viewmodel.PayoffStrategy strategy, double extraPayment) {
        return null;
    }
    
    public final void clearError() {
    }
}
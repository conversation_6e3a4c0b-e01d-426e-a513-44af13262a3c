package com.focusflow.ui.validation;

/**
 * ADHD Design Validation Report Generator
 * Creates comprehensive reports on ADHD-friendly design compliance
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0003\u0014\u0015\u0016B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u0006\u0010\u0007\u001a\u00020\bJ$\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u0006\u0010\r\u001a\u00020\u000bJ\b\u0010\u000e\u001a\u00020\u000fH\u0002J\b\u0010\u0010\u001a\u00020\u000fH\u0002J\b\u0010\u0011\u001a\u00020\u000fH\u0002J\b\u0010\u0012\u001a\u00020\u000fH\u0002J\b\u0010\u0013\u001a\u00020\u000fH\u0002\u00a8\u0006\u0017"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignReport;", "", "()V", "determineComplianceLevel", "Lcom/focusflow/ui/validation/ADHDDesignReport$ComplianceLevel;", "overallScore", "", "generateComprehensiveReport", "Lcom/focusflow/ui/validation/ADHDDesignReport$ADHDValidationReport;", "generateNextSteps", "", "", "criticalIssues", "generateSummaryReport", "validateBudgetScreen", "Lcom/focusflow/ui/validation/ADHDDesignReport$ScreenValidation;", "validateDashboardScreen", "validateDebtScreen", "validateExpenseScreen", "validatePayoffPlannerScreen", "ADHDValidationReport", "ComplianceLevel", "ScreenValidation", "app_debug"})
public final class ADHDDesignReport {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.ui.validation.ADHDDesignReport INSTANCE = null;
    
    private ADHDDesignReport() {
        super();
    }
    
    /**
     * Generate comprehensive ADHD design validation report for FocusFlow
     */
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.validation.ADHDDesignReport.ADHDValidationReport generateComprehensiveReport() {
        return null;
    }
    
    private final com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation validateDashboardScreen() {
        return null;
    }
    
    private final com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation validateExpenseScreen() {
        return null;
    }
    
    private final com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation validateDebtScreen() {
        return null;
    }
    
    private final com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation validatePayoffPlannerScreen() {
        return null;
    }
    
    private final com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation validateBudgetScreen() {
        return null;
    }
    
    private final com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel determineComplianceLevel(int overallScore) {
        return null;
    }
    
    private final java.util.List<java.lang.String> generateNextSteps(java.util.List<java.lang.String> criticalIssues, int overallScore) {
        return null;
    }
    
    /**
     * Generate summary report for quick overview
     */
    @org.jetbrains.annotations.NotNull
    public final java.lang.String generateSummaryReport() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\"\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B\u008b\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u0005\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u0012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\u0006\u0010\u0011\u001a\u00020\u0012\u0012\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\u0002\u0010\u0014J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u000f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\t\u0010)\u001a\u00020\u0012H\u00c6\u0003J\u000f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\t\u0010+\u001a\u00020\u0005H\u00c6\u0003J\t\u0010,\u001a\u00020\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\u0005H\u00c6\u0003J\t\u0010.\u001a\u00020\u0005H\u00c6\u0003J\t\u0010/\u001a\u00020\u0005H\u00c6\u0003J\t\u00100\u001a\u00020\u0005H\u00c6\u0003J\u000f\u00101\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\u000f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u00a9\u0001\u00103\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\b\b\u0002\u0010\u0011\u001a\u00020\u00122\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0001J\u0013\u00104\u001a\u0002052\b\u00106\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00107\u001a\u00020\u0005H\u00d6\u0001J\t\u00108\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0016R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001cR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0016R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001cR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001cR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001cR\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0016\u00a8\u00069"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignReport$ADHDValidationReport;", "", "timestamp", "", "overallScore", "", "visualHierarchyScore", "cognitiveLoadScore", "accessibilityScore", "feedbackScore", "colorContrastScore", "screenValidations", "", "Lcom/focusflow/ui/validation/ADHDDesignReport$ScreenValidation;", "criticalIssues", "topRecommendations", "strengths", "complianceLevel", "Lcom/focusflow/ui/validation/ADHDDesignReport$ComplianceLevel;", "nextSteps", "(Ljava/lang/String;IIIIIILjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lcom/focusflow/ui/validation/ADHDDesignReport$ComplianceLevel;Ljava/util/List;)V", "getAccessibilityScore", "()I", "getCognitiveLoadScore", "getColorContrastScore", "getComplianceLevel", "()Lcom/focusflow/ui/validation/ADHDDesignReport$ComplianceLevel;", "getCriticalIssues", "()Ljava/util/List;", "getFeedbackScore", "getNextSteps", "getOverallScore", "getScreenValidations", "getStrengths", "getTimestamp", "()Ljava/lang/String;", "getTopRecommendations", "getVisualHierarchyScore", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class ADHDValidationReport {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String timestamp = null;
        private final int overallScore = 0;
        private final int visualHierarchyScore = 0;
        private final int cognitiveLoadScore = 0;
        private final int accessibilityScore = 0;
        private final int feedbackScore = 0;
        private final int colorContrastScore = 0;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation> screenValidations = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> criticalIssues = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> topRecommendations = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> strengths = null;
        @org.jetbrains.annotations.NotNull
        private final com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel complianceLevel = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> nextSteps = null;
        
        public ADHDValidationReport(@org.jetbrains.annotations.NotNull
        java.lang.String timestamp, int overallScore, int visualHierarchyScore, int cognitiveLoadScore, int accessibilityScore, int feedbackScore, int colorContrastScore, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation> screenValidations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> criticalIssues, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> topRecommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths, @org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel complianceLevel, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> nextSteps) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getTimestamp() {
            return null;
        }
        
        public final int getOverallScore() {
            return 0;
        }
        
        public final int getVisualHierarchyScore() {
            return 0;
        }
        
        public final int getCognitiveLoadScore() {
            return 0;
        }
        
        public final int getAccessibilityScore() {
            return 0;
        }
        
        public final int getFeedbackScore() {
            return 0;
        }
        
        public final int getColorContrastScore() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation> getScreenValidations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getCriticalIssues() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getTopRecommendations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getStrengths() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel getComplianceLevel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getNextSteps() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component10() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component11() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel component12() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component13() {
            return null;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int component6() {
            return 0;
        }
        
        public final int component7() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation> component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignReport.ADHDValidationReport copy(@org.jetbrains.annotations.NotNull
        java.lang.String timestamp, int overallScore, int visualHierarchyScore, int cognitiveLoadScore, int accessibilityScore, int feedbackScore, int colorContrastScore, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation> screenValidations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> criticalIssues, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> topRecommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths, @org.jetbrains.annotations.NotNull
        com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel complianceLevel, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> nextSteps) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignReport$ComplianceLevel;", "", "(Ljava/lang/String;I)V", "EXCELLENT", "GOOD", "ACCEPTABLE", "NEEDS_IMPROVEMENT", "POOR", "app_debug"})
    public static enum ComplianceLevel {
        /*public static final*/ EXCELLENT /* = new EXCELLENT() */,
        /*public static final*/ GOOD /* = new GOOD() */,
        /*public static final*/ ACCEPTABLE /* = new ACCEPTABLE() */,
        /*public static final*/ NEEDS_IMPROVEMENT /* = new NEEDS_IMPROVEMENT() */,
        /*public static final*/ POOR /* = new POOR() */;
        
        ComplianceLevel() {
        }
        
        @org.jetbrains.annotations.NotNull
        public static kotlin.enums.EnumEntries<com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u001c\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001Bg\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u0005\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u000f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u007f\u0010\'\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0001J\u0013\u0010(\u001a\u00020)2\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\u0005H\u00d6\u0001J\t\u0010,\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0011R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0015R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0011\u00a8\u0006-"}, d2 = {"Lcom/focusflow/ui/validation/ADHDDesignReport$ScreenValidation;", "", "screenName", "", "overallScore", "", "visualHierarchyScore", "cognitiveLoadScore", "accessibilityScore", "feedbackScore", "colorContrastScore", "criticalIssues", "", "recommendations", "strengths", "(Ljava/lang/String;IIIIIILjava/util/List;Ljava/util/List;Ljava/util/List;)V", "getAccessibilityScore", "()I", "getCognitiveLoadScore", "getColorContrastScore", "getCriticalIssues", "()Ljava/util/List;", "getFeedbackScore", "getOverallScore", "getRecommendations", "getScreenName", "()Ljava/lang/String;", "getStrengths", "getVisualHierarchyScore", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
    public static final class ScreenValidation {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String screenName = null;
        private final int overallScore = 0;
        private final int visualHierarchyScore = 0;
        private final int cognitiveLoadScore = 0;
        private final int accessibilityScore = 0;
        private final int feedbackScore = 0;
        private final int colorContrastScore = 0;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> criticalIssues = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> recommendations = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.List<java.lang.String> strengths = null;
        
        public ScreenValidation(@org.jetbrains.annotations.NotNull
        java.lang.String screenName, int overallScore, int visualHierarchyScore, int cognitiveLoadScore, int accessibilityScore, int feedbackScore, int colorContrastScore, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> criticalIssues, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> recommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getScreenName() {
            return null;
        }
        
        public final int getOverallScore() {
            return 0;
        }
        
        public final int getVisualHierarchyScore() {
            return 0;
        }
        
        public final int getCognitiveLoadScore() {
            return 0;
        }
        
        public final int getAccessibilityScore() {
            return 0;
        }
        
        public final int getFeedbackScore() {
            return 0;
        }
        
        public final int getColorContrastScore() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getCriticalIssues() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getRecommendations() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> getStrengths() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component10() {
            return null;
        }
        
        public final int component2() {
            return 0;
        }
        
        public final int component3() {
            return 0;
        }
        
        public final int component4() {
            return 0;
        }
        
        public final int component5() {
            return 0;
        }
        
        public final int component6() {
            return 0;
        }
        
        public final int component7() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.List<java.lang.String> component9() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.ui.validation.ADHDDesignReport.ScreenValidation copy(@org.jetbrains.annotations.NotNull
        java.lang.String screenName, int overallScore, int visualHierarchyScore, int cognitiveLoadScore, int accessibilityScore, int feedbackScore, int colorContrastScore, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> criticalIssues, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> recommendations, @org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> strengths) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}
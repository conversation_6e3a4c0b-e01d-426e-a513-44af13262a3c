package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000e\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u0010H\'J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u000e\u0010\u0015\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0014\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u0010H\'J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u00102\u0006\u0010\u0019\u001a\u00020\u0013H\'J\u001c\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\r0\u00112\u0006\u0010\u001b\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u001cJ\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001e0\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u000e\u0010!\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u000e\u0010\"\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0014\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u0010H\'J\u001c\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u00102\u0006\u0010%\u001a\u00020\tH\'J\u0018\u0010&\u001a\u0004\u0018\u00010\r2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u00102\u0006\u0010(\u001a\u00020)H\'J\u001c\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u00102\u0006\u0010+\u001a\u00020)H\'J\u001c\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00110\u00102\u0006\u0010-\u001a\u00020)H\'J\u000e\u0010.\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u000e\u0010/\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0016\u00100\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001e\u00101\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u00102\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u00103J\u0016\u00104\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u00105\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u00106\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u00107\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u00108J\u001e\u00109\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010:\u001a\u00020;H\u00a7@\u00a2\u0006\u0002\u0010<J\u0016\u0010=\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000e\u00a8\u0006>"}, d2 = {"Lcom/focusflow/data/dao/SpendingPatternDao;", "", "deactivateSpendingPattern", "", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldUnconfirmedPatterns", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSpendingPattern", "spendingPattern", "Lcom/focusflow/data/model/SpendingPattern;", "(Lcom/focusflow/data/model/SpendingPattern;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActiveSpendingPatterns", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageConfidenceScore", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getConfirmedPatternCount", "", "getConfirmedSpendingPatterns", "getHighConfidencePatterns", "minConfidence", "getMostEffectiveInterventions", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPatternCountByCategory", "Lcom/focusflow/data/dao/PatternCategoryCount;", "getPatternCountByType", "Lcom/focusflow/data/dao/PatternTypeCount;", "getPatternsWithSuccessfulPrevention", "getPendingPatternCount", "getPendingSpendingPatterns", "getRecentSpendingPatterns", "recentDate", "getSpendingPatternById", "getSpendingPatternsByCategory", "category", "", "getSpendingPatternsBySeverity", "severity", "getSpendingPatternsByType", "patternType", "getTotalPreventionAttempts", "getTotalPreventionSuccesses", "insertSpendingPattern", "recordPatternOccurrence", "occurrence", "(JLkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recordPreventionAttempt", "recordSuccessfulPrevention", "updateInterventionEffectiveness", "effectiveness", "(JDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePatternConfirmation", "confirmed", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSpendingPattern", "app_release"})
@androidx.room.Dao
public abstract interface SpendingPatternDao {
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getAllActiveSpendingPatterns();
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE patternType = :patternType AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getSpendingPatternsByType(@org.jetbrains.annotations.NotNull
    java.lang.String patternType);
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE category = :category AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getSpendingPatternsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category);
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE userConfirmed IS NULL AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getPendingSpendingPatterns();
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE userConfirmed = 1 AND isActive = 1 ORDER BY lastOccurrence DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getConfirmedSpendingPatterns();
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE severity = :severity AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getSpendingPatternsBySeverity(@org.jetbrains.annotations.NotNull
    java.lang.String severity);
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getSpendingPatternById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.SpendingPattern> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE confidenceScore >= :minConfidence AND isActive = 1 ORDER BY confidenceScore DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getHighConfidencePatterns(double minConfidence);
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE lastOccurrence >= :recentDate AND isActive = 1 ORDER BY lastOccurrence DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.SpendingPattern>> getRecentSpendingPatterns(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime recentDate);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM spending_patterns WHERE userConfirmed IS NULL AND isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPendingPatternCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM spending_patterns WHERE userConfirmed = 1 AND isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getConfirmedPatternCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(confidenceScore) FROM spending_patterns WHERE userConfirmed = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageConfidenceScore(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT patternType, COUNT(*) as count FROM spending_patterns WHERE isActive = 1 GROUP BY patternType ORDER BY count DESC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPatternCountByType(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.dao.PatternTypeCount>> $completion);
    
    @androidx.room.Query(value = "SELECT category, COUNT(*) as count FROM spending_patterns WHERE isActive = 1 GROUP BY category ORDER BY count DESC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPatternCountByCategory(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.dao.PatternCategoryCount>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM spending_patterns WHERE interventionEffectiveness IS NOT NULL ORDER BY interventionEffectiveness DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getMostEffectiveInterventions(int limit, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.SpendingPattern>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM spending_patterns WHERE preventionSuccess > 0")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getPatternsWithSuccessfulPrevention(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(preventionSuccess) FROM spending_patterns")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalPreventionSuccesses(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(preventionAttempts) FROM spending_patterns")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalPreventionAttempts(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertSpendingPattern(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SpendingPattern spendingPattern, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateSpendingPattern(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SpendingPattern spendingPattern, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteSpendingPattern(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SpendingPattern spendingPattern, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE spending_patterns SET userConfirmed = :confirmed WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updatePatternConfirmation(long id, boolean confirmed, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE spending_patterns SET lastOccurrence = :occurrence, occurrenceCount = occurrenceCount + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordPatternOccurrence(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime occurrence, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE spending_patterns SET preventionSuccess = preventionSuccess + 1, preventionAttempts = preventionAttempts + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordSuccessfulPrevention(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE spending_patterns SET preventionAttempts = preventionAttempts + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordPreventionAttempt(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE spending_patterns SET interventionEffectiveness = :effectiveness WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateInterventionEffectiveness(long id, double effectiveness, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE spending_patterns SET isActive = 0 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deactivateSpendingPattern(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM spending_patterns WHERE detectedDate < :cutoffDate AND userConfirmed != 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteOldUnconfirmedPatterns(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
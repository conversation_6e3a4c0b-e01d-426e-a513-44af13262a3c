package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.Task
import com.focusflow.data.repository.TaskRepository
import com.focusflow.data.repository.TaskStatistics
import com.focusflow.service.GamificationService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class TaskViewModel @Inject constructor(
    private val taskRepository: TaskRepository,
    private val gamificationService: GamificationService
) : ViewModel() {

    private val _uiState = MutableStateFlow(TaskUiState())
    val uiState: StateFlow<TaskUiState> = _uiState.asStateFlow()

    private val _selectedFilter = MutableStateFlow(TaskFilter.ALL)
    val selectedFilter: StateFlow<TaskFilter> = _selectedFilter.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<SearchQuery> = _searchQuery.asStateFlow()

    val allTasks = taskRepository.getAllTasks()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val incompleteTasks = taskRepository.getIncompleteTasks()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val completedTasks = taskRepository.getCompletedTasks()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val todaysTasks = taskRepository.getTodaysTasks()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val overdueTasks = taskRepository.getOverdueTasks()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val recommendedTasks = taskRepository.getRecommendedTasks()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Filtered tasks based on current filter and search
    val filteredTasks = combine(
        allTasks,
        incompleteTasks,
        completedTasks,
        todaysTasks,
        overdueTasks,
        selectedFilter,
        searchQuery
    ) { all, incomplete, completed, today, overdue, filter, query ->
        val baseList = when (filter) {
            TaskFilter.ALL -> all
            TaskFilter.INCOMPLETE -> incomplete
            TaskFilter.COMPLETED -> completed
            TaskFilter.TODAY -> today
            TaskFilter.OVERDUE -> overdue
            TaskFilter.HIGH_PRIORITY -> incomplete.filter { it.priority == "high" }
            TaskFilter.MEDIUM_PRIORITY -> incomplete.filter { it.priority == "medium" }
            TaskFilter.LOW_PRIORITY -> incomplete.filter { it.priority == "low" }
        }

        if (query.isBlank()) {
            baseList
        } else {
            baseList.filter { task ->
                task.title.contains(query, ignoreCase = true) ||
                task.description?.contains(query, ignoreCase = true) == true ||
                task.category?.contains(query, ignoreCase = true) == true
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )

    init {
        loadTaskStatistics()
        loadTaskCategories()
    }

    private fun loadTaskStatistics() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val statistics = taskRepository.getTaskStatistics()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    taskStatistics = statistics
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load task statistics: ${e.message}"
                )
            }
        }
    }

    private fun loadTaskCategories() {
        viewModelScope.launch {
            try {
                val categories = taskRepository.getTaskCategories()
                _uiState.value = _uiState.value.copy(availableCategories = categories)
            } catch (e: Exception) {
                // Silently handle category loading errors
            }
        }
    }

    fun createTask(
        title: String,
        description: String = "",
        dueDate: LocalDateTime? = null,
        priority: String = "medium",
        category: String? = null,
        estimatedDuration: Int? = null
    ) {
        if (title.isBlank()) return

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isCreating = true, error = null)

                val taskId = taskRepository.createTask(
                    title = title.trim(),
                    description = description.trim().takeIf { it.isNotBlank() },
                    dueDate = dueDate,
                    priority = priority,
                    category = category?.takeIf { it.isNotBlank() },
                    estimatedDuration = estimatedDuration
                )

                // Award gamification points
                gamificationService.awardPoints(5)
                gamificationService.feedVirtualPet(2)

                _uiState.value = _uiState.value.copy(
                    isCreating = false,
                    lastAction = "Task created successfully! 📝"
                )

                loadTaskStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isCreating = false,
                    error = "Failed to create task: ${e.message}"
                )
            }
        }
    }

    fun completeTask(task: Task) {
        viewModelScope.launch {
            try {
                taskRepository.completeTask(task)

                // Award gamification points based on task priority and estimated duration
                val points = when (task.priority) {
                    "high" -> 15
                    "medium" -> 10
                    "low" -> 5
                    else -> 10
                }
                
                gamificationService.awardPoints(points)
                gamificationService.feedVirtualPet(5)

                _uiState.value = _uiState.value.copy(
                    lastAction = "Task completed! Great job! 🎉 (+$points points)"
                )

                loadTaskStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to complete task: ${e.message}"
                )
            }
        }
    }

    fun deleteTask(task: Task) {
        viewModelScope.launch {
            try {
                taskRepository.deleteTask(task)
                
                _uiState.value = _uiState.value.copy(
                    lastAction = "Task deleted"
                )

                loadTaskStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete task: ${e.message}"
                )
            }
        }
    }

    fun updateTask(task: Task) {
        viewModelScope.launch {
            try {
                taskRepository.updateTask(task)
                
                _uiState.value = _uiState.value.copy(
                    lastAction = "Task updated"
                )

                loadTaskStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update task: ${e.message}"
                )
            }
        }
    }

    fun breakDownTask(task: Task, subtasks: List<String>) {
        if (subtasks.isEmpty()) return

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isCreating = true, error = null)

                taskRepository.breakDownLargeTask(task, subtasks)

                _uiState.value = _uiState.value.copy(
                    isCreating = false,
                    lastAction = "Task broken down into ${subtasks.size} subtasks! 🧩"
                )

                loadTaskStatistics()

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isCreating = false,
                    error = "Failed to break down task: ${e.message}"
                )
            }
        }
    }

    fun setFilter(filter: TaskFilter) {
        _selectedFilter.value = filter
    }

    fun setSearchQuery(query: String) {
        _searchQuery.value = query
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun clearLastAction() {
        _uiState.value = _uiState.value.copy(lastAction = null)
    }

    // Helper methods for UI
    fun getPriorityEmoji(priority: String): String {
        return when (priority) {
            "high" -> "🔴"
            "medium" -> "🟡"
            "low" -> "🟢"
            else -> "⚪"
        }
    }

    fun getPriorityColor(priority: String): androidx.compose.ui.graphics.Color {
        return when (priority) {
            "high" -> androidx.compose.ui.graphics.Color(0xFFF44336)
            "medium" -> androidx.compose.ui.graphics.Color(0xFFFF9800)
            "low" -> androidx.compose.ui.graphics.Color(0xFF4CAF50)
            else -> androidx.compose.ui.graphics.Color(0xFF9E9E9E)
        }
    }

    fun formatDuration(minutes: Int?): String {
        if (minutes == null) return "No estimate"
        return when {
            minutes < 60 -> "${minutes}m"
            minutes < 1440 -> "${minutes / 60}h ${minutes % 60}m"
            else -> "${minutes / 1440}d ${(minutes % 1440) / 60}h"
        }
    }

    fun getTaskStatusMessage(task: Task): String {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        
        return when {
            task.isCompleted -> "✅ Completed"
            task.dueDate == null -> "📝 No due date"
            task.dueDate!! < now -> "⚠️ Overdue"
            task.dueDate!!.date == now.date -> "📅 Due today"
            task.dueDate!!.date == now.date.plus(1, DateTimeUnit.DAY) -> "📅 Due tomorrow"
            else -> "📅 Due ${task.dueDate!!.date}"
        }
    }
}

data class TaskUiState(
    val isLoading: Boolean = false,
    val isCreating: Boolean = false,
    val taskStatistics: TaskStatistics? = null,
    val availableCategories: List<String> = emptyList(),
    val lastAction: String? = null,
    val error: String? = null
)

enum class TaskFilter {
    ALL,
    INCOMPLETE,
    COMPLETED,
    TODAY,
    OVERDUE,
    HIGH_PRIORITY,
    MEDIUM_PRIORITY,
    LOW_PRIORITY
}

typealias SearchQuery = String

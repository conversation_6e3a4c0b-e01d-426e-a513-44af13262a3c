package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0013\u001a\u00020\u00102\u0006\u0010\u0014\u001a\u00020\u0012J\u0006\u0010\u0015\u001a\u00020\u0010J\u0006\u0010\u0016\u001a\u00020\u0010J\u0006\u0010\u0017\u001a\u00020\u0010J\b\u0010\u0018\u001a\u00020\u0010H\u0002J\u0006\u0010\u0019\u001a\u00020\u0010J\u000e\u0010\u001a\u001a\u00020\u00102\u0006\u0010\u001b\u001a\u00020\u0012J\b\u0010\u001c\u001a\u00020\u0010H\u0002R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0019\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/focusflow/ui/viewmodel/VirtualPetViewModel;", "Landroidx/lifecycle/ViewModel;", "virtualPetRepository", "Lcom/focusflow/data/repository/VirtualPetRepository;", "(Lcom/focusflow/data/repository/VirtualPetRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/VirtualPetUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "virtualPet", "Lcom/focusflow/data/model/VirtualPet;", "getVirtualPet", "addAccessory", "", "accessory", "", "changePetType", "newType", "clearError", "dismissActionFeedback", "feedPet", "loadOrCreatePet", "playWithPet", "renamePet", "newName", "simulatePetCare", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class VirtualPetViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.VirtualPetRepository virtualPetRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.VirtualPetUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.VirtualPetUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.data.model.VirtualPet> virtualPet = null;
    
    @javax.inject.Inject
    public VirtualPetViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.VirtualPetRepository virtualPetRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.VirtualPetUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.data.model.VirtualPet> getVirtualPet() {
        return null;
    }
    
    private final void loadOrCreatePet() {
    }
    
    public final void feedPet() {
    }
    
    public final void playWithPet() {
    }
    
    public final void renamePet(@org.jetbrains.annotations.NotNull
    java.lang.String newName) {
    }
    
    public final void changePetType(@org.jetbrains.annotations.NotNull
    java.lang.String newType) {
    }
    
    public final void addAccessory(@org.jetbrains.annotations.NotNull
    java.lang.String accessory) {
    }
    
    private final void simulatePetCare() {
    }
    
    public final void clearError() {
    }
    
    public final void dismissActionFeedback() {
    }
}
package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0012\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00100\u000fJ\u000e\u0010\u0011\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\u0013J\"\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00100\u000f2\u0006\u0010\u0015\u001a\u00020\f2\u0006\u0010\u0016\u001a\u00020\fJ\u001a\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00100\u000f2\u0006\u0010\u0018\u001a\u00020\u0019J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00100\u000f2\b\b\u0002\u0010\u001b\u001a\u00020\u0012J\u0016\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ*\u0010\u001e\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"J*\u0010#\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"J*\u0010$\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"J*\u0010%\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"J*\u0010&\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"J*\u0010\'\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"J*\u0010(\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u00192\u0006\u0010 \u001a\u00020\u00192\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u0010\"R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/focusflow/data/repository/AIInteractionRepository;", "", "aiInteractionDao", "Lcom/focusflow/data/dao/AIInteractionDao;", "(Lcom/focusflow/data/dao/AIInteractionDao;)V", "deleteInteraction", "", "interaction", "Lcom/focusflow/data/model/AIInteraction;", "(Lcom/focusflow/data/model/AIInteraction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldInteractions", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllInteractions", "Lkotlinx/coroutines/flow/Flow;", "", "getInteractionCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInteractionsByDateRange", "startDate", "endDate", "getInteractionsByType", "type", "", "getRecentInteractions", "limit", "insertInteraction", "", "saveBudgetAdvice", "userMessage", "aiResponse", "contextData", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveDebtAdvice", "saveGeneralInteraction", "saveMotivationalMessage", "saveProgressReport", "saveSpendingAnalysis", "saveTaskBreakdown", "app_release"})
public final class AIInteractionRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.AIInteractionDao aiInteractionDao = null;
    
    @javax.inject.Inject
    public AIInteractionRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.AIInteractionDao aiInteractionDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AIInteraction>> getAllInteractions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AIInteraction>> getInteractionsByType(@org.jetbrains.annotations.NotNull
    java.lang.String type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AIInteraction>> getRecentInteractions(int limit) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertInteraction(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AIInteraction interaction, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteInteraction(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AIInteraction interaction, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteOldInteractions(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveSpendingAnalysis(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveBudgetAdvice(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveDebtAdvice(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveTaskBreakdown(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveProgressReport(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveMotivationalMessage(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object saveGeneralInteraction(@org.jetbrains.annotations.NotNull
    java.lang.String userMessage, @org.jetbrains.annotations.NotNull
    java.lang.String aiResponse, @org.jetbrains.annotations.Nullable
    java.lang.String contextData, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getInteractionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AIInteraction>> getInteractionsByDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endDate) {
        return null;
    }
}
package com.focusflow.security;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0004\u0012\u0006\u0010\t\u001a\u00020\n\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\fJ\u000f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0004H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\nH\u00c6\u0003J\u000f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003JQ\u0010\u001d\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00042\b\b\u0002\u0010\t\u001a\u00020\n2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u00072\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020\u0004H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0011\u0010\u0005\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006#"}, d2 = {"Lcom/focusflow/security/PrivacyReport;", "", "dataTypesStored", "", "", "encryptionStatus", "biometricProtection", "", "dataRetentionPolicy", "lastSecurityAudit", "", "complianceStandards", "(Ljava/util/List;Ljava/lang/String;ZLjava/lang/String;JLjava/util/List;)V", "getBiometricProtection", "()Z", "getComplianceStandards", "()Ljava/util/List;", "getDataRetentionPolicy", "()Ljava/lang/String;", "getDataTypesStored", "getEncryptionStatus", "getLastSecurityAudit", "()J", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class PrivacyReport {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> dataTypesStored = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String encryptionStatus = null;
    private final boolean biometricProtection = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String dataRetentionPolicy = null;
    private final long lastSecurityAudit = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> complianceStandards = null;
    
    public PrivacyReport(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> dataTypesStored, @org.jetbrains.annotations.NotNull
    java.lang.String encryptionStatus, boolean biometricProtection, @org.jetbrains.annotations.NotNull
    java.lang.String dataRetentionPolicy, long lastSecurityAudit, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> complianceStandards) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getDataTypesStored() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getEncryptionStatus() {
        return null;
    }
    
    public final boolean getBiometricProtection() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDataRetentionPolicy() {
        return null;
    }
    
    public final long getLastSecurityAudit() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getComplianceStandards() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    public final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.security.PrivacyReport copy(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> dataTypesStored, @org.jetbrains.annotations.NotNull
    java.lang.String encryptionStatus, boolean biometricProtection, @org.jetbrains.annotations.NotNull
    java.lang.String dataRetentionPolicy, long lastSecurityAudit, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> complianceStandards) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}
package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\f\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\fH\'J\u0010\u0010\u000e\u001a\u0004\u0018\u00010\rH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0010\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0011\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u0014J\u0016\u0010\u0015\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0018\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u0014\u00a8\u0006\u0019"}, d2 = {"Lcom/focusflow/data/dao/UserStatsDao;", "", "addDebtPaid", "", "amount", "", "(DLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addPoints", "points", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserStats", "Lkotlinx/coroutines/flow/Flow;", "Lcom/focusflow/data/model/UserStats;", "getUserStatsSync", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "incrementAchievementsUnlocked", "incrementExpensesLogged", "insertUserStats", "userStats", "(Lcom/focusflow/data/model/UserStats;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateBudgetAdherenceStreak", "streak", "updateExpenseLoggingStreak", "updateUserStats", "app_release"})
@androidx.room.Dao
public abstract interface UserStatsDao {
    
    @androidx.room.Query(value = "SELECT * FROM user_stats WHERE id = 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.focusflow.data.model.UserStats> getUserStats();
    
    @androidx.room.Query(value = "SELECT * FROM user_stats WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserStatsSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.UserStats> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertUserStats(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.UserStats userStats, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUserStats(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.UserStats userStats, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_stats SET totalPoints = totalPoints + :points WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object addPoints(int points, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_stats SET expenseLoggingStreak = :streak WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateExpenseLoggingStreak(int streak, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_stats SET budgetAdherenceStreak = :streak WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateBudgetAdherenceStreak(int streak, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_stats SET totalExpensesLogged = totalExpensesLogged + 1 WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object incrementExpensesLogged(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_stats SET totalDebtPaid = totalDebtPaid + :amount WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object addDebtPaid(double amount, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_stats SET achievementsUnlocked = achievementsUnlocked + 1 WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object incrementAchievementsUnlocked(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}
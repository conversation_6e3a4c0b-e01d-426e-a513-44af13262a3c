package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.NotificationRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.service.GamificationService;
import com.focusflow.service.SampleDataService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  private final Provider<NotificationRepository> notificationRepositoryProvider;

  private final Provider<SampleDataService> sampleDataServiceProvider;

  public MainViewModel_Factory(
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider,
      Provider<NotificationRepository> notificationRepositoryProvider,
      Provider<SampleDataService> sampleDataServiceProvider) {
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
    this.notificationRepositoryProvider = notificationRepositoryProvider;
    this.sampleDataServiceProvider = sampleDataServiceProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(userPreferencesRepositoryProvider.get(), gamificationServiceProvider.get(), notificationRepositoryProvider.get(), sampleDataServiceProvider.get());
  }

  public static MainViewModel_Factory create(
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider,
      Provider<NotificationRepository> notificationRepositoryProvider,
      Provider<SampleDataService> sampleDataServiceProvider) {
    return new MainViewModel_Factory(userPreferencesRepositoryProvider, gamificationServiceProvider, notificationRepositoryProvider, sampleDataServiceProvider);
  }

  public static MainViewModel newInstance(UserPreferencesRepository userPreferencesRepository,
      GamificationService gamificationService, NotificationRepository notificationRepository,
      SampleDataService sampleDataService) {
    return new MainViewModel(userPreferencesRepository, gamificationService, notificationRepository, sampleDataService);
  }
}

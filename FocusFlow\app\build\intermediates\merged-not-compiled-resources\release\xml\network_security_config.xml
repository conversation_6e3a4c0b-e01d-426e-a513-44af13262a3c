<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Production API domains with certificate pinning -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.focusflow.com</domain>
        <domain includeSubdomains="true">secure.focusflow.com</domain>
        <domain includeSubdomains="true">auth.focusflow.com</domain>
        <pin-set expiration="2026-12-31">
            <!-- Primary certificate pin (replace with actual production pins) -->
            <pin digest="SHA-256">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</pin>
            <!-- Backup certificate pin -->
            <pin digest="SHA-256">BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=</pin>
            <!-- CA certificate pin for additional security -->
            <pin digest="SHA-256">CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC=</pin>
        </pin-set>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- AI service domains with enhanced security -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.openai.com</domain>
        <domain includeSubdomains="true">api.anthropic.com</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- Financial service integrations -->
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.plaid.com</domain>
        <domain includeSubdomains="true">production.plaid.com</domain>
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </domain-config>

    <!-- Debug configuration for development (remove in production) -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="user"/>
            <certificates src="system"/>
        </trust-anchors>
    </debug-overrides>

    <!-- Block all cleartext traffic by default -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>


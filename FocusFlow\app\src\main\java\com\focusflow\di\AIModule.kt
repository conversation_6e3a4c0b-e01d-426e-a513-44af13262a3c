package com.focusflow.di

import com.focusflow.service.AIService
import com.focusflow.service.MockAIService
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class AIModule {
    
    @Binds
    @Singleton
    abstract fun bindAIService(
        mockAIService: MockAIService
    ): AIService
}

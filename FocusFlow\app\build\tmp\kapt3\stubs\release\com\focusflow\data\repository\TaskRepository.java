package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u000e\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J*\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006H\u0086@\u00a2\u0006\u0002\u0010\fJ\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000bH\u0002J\u0016\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\u00112\u0006\u0010\b\u001a\u00020\tH\u0082@\u00a2\u0006\u0002\u0010\u0013JN\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\u000b2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0018\u001a\u00020\u000b2\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u000f\u001a\u00020\u000b2\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001cJP\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\u000b2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u0018\u001a\u00020\u000b2\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u001bH\u0086@\u00a2\u0006\u0002\u0010\u001fJ\u001c\u0010 \u001a\u00020\u00112\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u0010#\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0012\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u001e\u0010&\u001a\u00020\u001b2\u0006\u0010\'\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010)J\u0012\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u000e\u0010+\u001a\u00020\u001bH\u0086@\u00a2\u0006\u0002\u0010,J\u0012\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u0012\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u0012\u0010/\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u0014\u00100\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006H\u0086@\u00a2\u0006\u0002\u0010,J\u000e\u00101\u001a\u000202H\u0086@\u00a2\u0006\u0002\u0010,J\u001a\u00103\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%2\u0006\u0010\u0019\u001a\u00020\u000bJ\"\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%2\u0006\u0010\'\u001a\u00020\u000e2\u0006\u0010(\u001a\u00020\u000eJ\u001a\u00105\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%2\u0006\u0010\u0018\u001a\u00020\u000bJ\u0012\u00106\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u0012\u00107\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%J\u0016\u00108\u001a\u00020\u00072\u0006\u0010\u0012\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u001c\u00109\u001a\u00020\u00112\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0086@\u00a2\u0006\u0002\u0010\"J\u0016\u0010:\u001a\u00020\u00112\u0006\u0010;\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010<J\u001a\u0010=\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00060%2\u0006\u0010>\u001a\u00020\u000bJ\u0016\u0010?\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0013R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006@"}, d2 = {"Lcom/focusflow/data/repository/TaskRepository;", "", "taskDao", "Lcom/focusflow/data/dao/TaskDao;", "(Lcom/focusflow/data/dao/TaskDao;)V", "breakDownLargeTask", "", "", "originalTask", "Lcom/focusflow/data/model/Task;", "subtasks", "", "(Lcom/focusflow/data/model/Task;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "calculateNextDueDate", "Lkotlinx/datetime/LocalDateTime;", "frequency", "completeTask", "", "task", "(Lcom/focusflow/data/model/Task;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNextRecurringTask", "createRecurringTask", "title", "description", "priority", "category", "estimatedDuration", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createTask", "dueDate", "(Ljava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteMultipleTasks", "taskIds", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteTask", "getAllTasks", "Lkotlinx/coroutines/flow/Flow;", "getCompletedTaskCountInPeriod", "startDate", "endDate", "(Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCompletedTasks", "getIncompleteTaskCount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getIncompleteTasks", "getOverdueTasks", "getRecommendedTasks", "getTaskCategories", "getTaskStatistics", "Lcom/focusflow/data/repository/TaskStatistics;", "getTasksByCategory", "getTasksByDueDateRange", "getTasksByPriority", "getThisWeeksTasks", "getTodaysTasks", "insertTask", "markMultipleTasksCompleted", "markTaskAsCompleted", "taskId", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchTasks", "query", "updateTask", "app_release"})
public final class TaskRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.TaskDao taskDao = null;
    
    @javax.inject.Inject
    public TaskRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.TaskDao taskDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getAllTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getIncompleteTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getCompletedTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getTasksByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getTasksByDueDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endDate) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getOverdueTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object markTaskAsCompleted(long taskId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getIncompleteTaskCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getCompletedTaskCountInPeriod(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createTask(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime dueDate, @org.jetbrains.annotations.NotNull
    java.lang.String priority, @org.jetbrains.annotations.Nullable
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.Integer estimatedDuration, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createRecurringTask(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.NotNull
    java.lang.String priority, @org.jetbrains.annotations.Nullable
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String frequency, @org.jetbrains.annotations.Nullable
    java.lang.Integer estimatedDuration, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object completeTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object createNextRecurringTask(com.focusflow.data.model.Task originalTask, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final kotlinx.datetime.LocalDateTime calculateNextDueDate(java.lang.String frequency) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getTodaysTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getThisWeeksTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTaskStatistics(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.repository.TaskStatistics> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getTasksByPriority(@org.jetbrains.annotations.NotNull
    java.lang.String priority) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> searchTasks(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTaskCategories(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object markMultipleTasksCompleted(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.Long> taskIds, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteMultipleTasks(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.Long> taskIds, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object breakDownLargeTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task originalTask, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> subtasks, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.Task>> getRecommendedTasks() {
        return null;
    }
}
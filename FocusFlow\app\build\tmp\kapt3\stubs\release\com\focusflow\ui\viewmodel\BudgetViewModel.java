package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0011J\u000e\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u0019J.\u0010\u001a\u001a\u0018\u0012\u0004\u0012\u00020\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u001c\u0012\u0006\u0012\u0004\u0018\u00010\u001c0\u001b2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u0013H\u0002J\b\u0010 \u001a\u00020\u0011H\u0002J\u000e\u0010!\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u0019R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/focusflow/ui/viewmodel/BudgetViewModel;", "Landroidx/lifecycle/ViewModel;", "budgetCategoryRepository", "Lcom/focusflow/data/repository/BudgetCategoryRepository;", "expenseRepository", "Lcom/focusflow/data/repository/ExpenseRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "(Lcom/focusflow/data/repository/BudgetCategoryRepository;Lcom/focusflow/data/repository/ExpenseRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/BudgetUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "addBudgetCategory", "", "name", "", "allocatedAmount", "", "clearError", "deleteBudgetCategory", "budgetCategory", "Lcom/focusflow/data/model/BudgetCategory;", "getCurrentPeriodValues", "Lkotlin/Triple;", "", "now", "Lkotlinx/datetime/LocalDateTime;", "period", "loadBudgetData", "updateBudgetCategory", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class BudgetViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.BudgetUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.BudgetUiState> uiState = null;
    
    @javax.inject.Inject
    public BudgetViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.BudgetUiState> getUiState() {
        return null;
    }
    
    private final void loadBudgetData() {
    }
    
    public final void addBudgetCategory(@org.jetbrains.annotations.NotNull
    java.lang.String name, double allocatedAmount) {
    }
    
    public final void updateBudgetCategory(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory) {
    }
    
    public final void deleteBudgetCategory(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory) {
    }
    
    public final void clearError() {
    }
    
    private final kotlin.Triple<java.lang.Integer, java.lang.Integer, java.lang.Integer> getCurrentPeriodValues(kotlinx.datetime.LocalDateTime now, java.lang.String period) {
        return null;
    }
}
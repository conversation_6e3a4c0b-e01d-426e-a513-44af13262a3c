package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0000\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020\u00112\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\t0\u0010J\u0006\u0010,\u001a\u00020)J\u0006\u0010-\u001a\u00020)J\u000e\u0010.\u001a\u00020)2\u0006\u0010*\u001a\u00020\u0011JK\u0010/\u001a\u00020)2\u0006\u00100\u001a\u00020\t2\b\b\u0002\u00101\u001a\u00020\t2\n\b\u0002\u00102\u001a\u0004\u0018\u0001032\b\b\u0002\u00104\u001a\u00020\t2\n\b\u0002\u00105\u001a\u0004\u0018\u00010\t2\n\b\u0002\u00106\u001a\u0004\u0018\u000107\u00a2\u0006\u0002\u00108J\u000e\u00109\u001a\u00020)2\u0006\u0010*\u001a\u00020\u0011J\u0015\u0010:\u001a\u00020\t2\b\u0010;\u001a\u0004\u0018\u000107\u00a2\u0006\u0002\u0010<J\u001b\u0010=\u001a\u00020>2\u0006\u00104\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010@J\u000e\u0010A\u001a\u00020\t2\u0006\u00104\u001a\u00020\tJ\u000e\u0010B\u001a\u00020\t2\u0006\u0010*\u001a\u00020\u0011J\b\u0010C\u001a\u00020)H\u0002J\b\u0010D\u001a\u00020)H\u0002J\u000e\u0010E\u001a\u00020)2\u0006\u0010F\u001a\u00020\u000bJ\u000e\u0010G\u001a\u00020)2\u0006\u0010H\u001a\u00020\tJ\u000e\u0010I\u001a\u00020)2\u0006\u0010*\u001a\u00020\u0011R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u001d\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0013R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u001d\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0013R\u001d\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0013R\u001b\u0010\u001f\u001a\f\u0012\b\u0012\u00060\tj\u0002` 0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0013R\u0017\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0013R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00110\u00100\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0013R\u0017\u0010&\u001a\b\u0012\u0004\u0012\u00020\r0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0013\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006J"}, d2 = {"Lcom/focusflow/ui/viewmodel/TaskViewModel;", "Landroidx/lifecycle/ViewModel;", "taskRepository", "Lcom/focusflow/data/repository/TaskRepository;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "(Lcom/focusflow/data/repository/TaskRepository;Lcom/focusflow/service/GamificationService;)V", "_searchQuery", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_selectedFilter", "Lcom/focusflow/ui/viewmodel/TaskFilter;", "_uiState", "Lcom/focusflow/ui/viewmodel/TaskUiState;", "allTasks", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/focusflow/data/model/Task;", "getAllTasks", "()Lkotlinx/coroutines/flow/StateFlow;", "completedTasks", "getCompletedTasks", "filteredTasks", "", "getFilteredTasks", "incompleteTasks", "getIncompleteTasks", "overdueTasks", "getOverdueTasks", "recommendedTasks", "getRecommendedTasks", "searchQuery", "Lcom/focusflow/ui/viewmodel/SearchQuery;", "getSearchQuery", "selectedFilter", "getSelectedFilter", "todaysTasks", "getTodaysTasks", "uiState", "getUiState", "breakDownTask", "", "task", "subtasks", "clearError", "clearLastAction", "completeTask", "createTask", "title", "description", "dueDate", "Lkotlinx/datetime/LocalDateTime;", "priority", "category", "estimatedDuration", "", "(Ljava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "deleteTask", "formatDuration", "minutes", "(Ljava/lang/Integer;)Ljava/lang/String;", "getPriorityColor", "Landroidx/compose/ui/graphics/Color;", "getPriorityColor-vNxB06k", "(Ljava/lang/String;)J", "getPriorityEmoji", "getTaskStatusMessage", "loadTaskCategories", "loadTaskStatistics", "setFilter", "filter", "setSearchQuery", "query", "updateTask", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class TaskViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.TaskRepository taskRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.TaskUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.TaskFilter> _selectedFilter = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskFilter> selectedFilter = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _searchQuery = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> searchQuery = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> allTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> incompleteTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> completedTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> todaysTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> overdueTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> recommendedTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Object> filteredTasks = null;
    
    @javax.inject.Inject
    public TaskViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.TaskRepository taskRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskFilter> getSelectedFilter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSearchQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getAllTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getIncompleteTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getCompletedTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getTodaysTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getOverdueTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getRecommendedTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Object> getFilteredTasks() {
        return null;
    }
    
    private final void loadTaskStatistics() {
    }
    
    private final void loadTaskCategories() {
    }
    
    public final void createTask(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime dueDate, @org.jetbrains.annotations.NotNull
    java.lang.String priority, @org.jetbrains.annotations.Nullable
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.Integer estimatedDuration) {
    }
    
    public final void completeTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void deleteTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void updateTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void breakDownTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> subtasks) {
    }
    
    public final void setFilter(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TaskFilter filter) {
    }
    
    public final void setSearchQuery(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
    }
    
    public final void clearError() {
    }
    
    public final void clearLastAction() {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPriorityEmoji(@org.jetbrains.annotations.NotNull
    java.lang.String priority) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatDuration(@org.jetbrains.annotations.Nullable
    java.lang.Integer minutes) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTaskStatusMessage(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
        return null;
    }
}
#!/bin/bash

# FocusFlow Production Build Script
# Builds and tests the app for production release across multiple device configurations

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PROJECT_DIR/build"
REPORTS_DIR="$BUILD_DIR/reports"
APK_DIR="$PROJECT_DIR/app/build/outputs/apk"
BUNDLE_DIR="$PROJECT_DIR/app/build/outputs/bundle"

echo -e "${BLUE}🚀 FocusFlow Production Build Pipeline${NC}"
echo "=================================================="

# Function to print status
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Android SDK is available
    if ! command -v adb &> /dev/null; then
        print_error "Android SDK not found. Please install Android SDK and add it to PATH."
        exit 1
    fi
    
    # Check if Java is available
    if ! command -v java &> /dev/null; then
        print_error "Java not found. Please install Java 11 or higher."
        exit 1
    fi
    
    # Check Java version
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 11 ]; then
        print_error "Java 11 or higher required. Current version: $JAVA_VERSION"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to clean previous builds
clean_build() {
    print_status "Cleaning previous builds..."
    cd "$PROJECT_DIR"
    ./gradlew clean
    
    # Create reports directory
    mkdir -p "$REPORTS_DIR"
    
    print_success "Build cleaned"
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    cd "$PROJECT_DIR"
    
    ./gradlew testDebugUnitTest testReleaseUnitTest
    
    # Copy test reports
    if [ -d "app/build/reports/tests" ]; then
        cp -r app/build/reports/tests "$REPORTS_DIR/"
    fi
    
    print_success "Unit tests completed"
}

# Function to run lint checks
run_lint_checks() {
    print_status "Running lint checks..."
    cd "$PROJECT_DIR"
    
    ./gradlew lintDebug lintRelease
    
    # Copy lint reports
    if [ -d "app/build/reports/lint-results" ]; then
        cp -r app/build/reports/lint-results "$REPORTS_DIR/"
    fi
    
    print_success "Lint checks completed"
}

# Function to build debug APK
build_debug_apk() {
    print_status "Building debug APK..."
    cd "$PROJECT_DIR"
    
    ./gradlew assembleDebug
    
    if [ -f "$APK_DIR/debug/app-debug.apk" ]; then
        print_success "Debug APK built successfully"
        print_status "Debug APK location: $APK_DIR/debug/app-debug.apk"
    else
        print_error "Debug APK build failed"
        exit 1
    fi
}

# Function to build staging APK
build_staging_apk() {
    print_status "Building staging APK..."
    cd "$PROJECT_DIR"
    
    ./gradlew assembleStaging
    
    if [ -f "$APK_DIR/staging/app-staging.apk" ]; then
        print_success "Staging APK built successfully"
        print_status "Staging APK location: $APK_DIR/staging/app-staging.apk"
    else
        print_error "Staging APK build failed"
        exit 1
    fi
}

# Function to build release APK
build_release_apk() {
    print_status "Building release APK..."
    cd "$PROJECT_DIR"
    
    # Check if signing config is available
    if [ -z "$KEYSTORE_PATH" ] || [ -z "$KEYSTORE_PASSWORD" ]; then
        print_warning "Release signing not configured. Building unsigned release APK."
        ./gradlew assembleRelease
    else
        print_status "Building signed release APK..."
        ./gradlew assembleRelease \
            -Pandroid.injected.signing.store.file="$KEYSTORE_PATH" \
            -Pandroid.injected.signing.store.password="$KEYSTORE_PASSWORD" \
            -Pandroid.injected.signing.key.alias="$KEY_ALIAS" \
            -Pandroid.injected.signing.key.password="$KEY_PASSWORD"
    fi
    
    if [ -f "$APK_DIR/release/app-release.apk" ]; then
        print_success "Release APK built successfully"
        print_status "Release APK location: $APK_DIR/release/app-release.apk"
    else
        print_error "Release APK build failed"
        exit 1
    fi
}

# Function to build Android App Bundle
build_app_bundle() {
    print_status "Building Android App Bundle..."
    cd "$PROJECT_DIR"
    
    ./gradlew bundleRelease
    
    if [ -f "$BUNDLE_DIR/release/app-release.aab" ]; then
        print_success "Android App Bundle built successfully"
        print_status "App Bundle location: $BUNDLE_DIR/release/app-release.aab"
    else
        print_error "App Bundle build failed"
        exit 1
    fi
}

# Function to run instrumented tests
run_instrumented_tests() {
    print_status "Running instrumented tests..."
    
    # Check if emulator or device is connected
    DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    if [ "$DEVICES" -eq 0 ]; then
        print_warning "No Android devices/emulators connected. Skipping instrumented tests."
        return
    fi
    
    cd "$PROJECT_DIR"
    ./gradlew connectedDebugAndroidTest
    
    # Copy test reports
    if [ -d "app/build/reports/androidTests" ]; then
        cp -r app/build/reports/androidTests "$REPORTS_DIR/"
    fi
    
    print_success "Instrumented tests completed"
}

# Function to analyze APK
analyze_apk() {
    print_status "Analyzing APK..."
    cd "$PROJECT_DIR"
    
    # Generate APK analyzer report
    ./gradlew analyzeReleaseBundle
    
    # Check APK size
    if [ -f "$APK_DIR/release/app-release.apk" ]; then
        APK_SIZE=$(du -h "$APK_DIR/release/app-release.apk" | cut -f1)
        print_status "Release APK size: $APK_SIZE"
        
        # Warn if APK is too large
        APK_SIZE_BYTES=$(stat -f%z "$APK_DIR/release/app-release.apk" 2>/dev/null || stat -c%s "$APK_DIR/release/app-release.apk")
        if [ "$APK_SIZE_BYTES" -gt 100000000 ]; then  # 100MB
            print_warning "APK size is large (>100MB). Consider optimizing."
        fi
    fi
    
    print_success "APK analysis completed"
}

# Function to generate build report
generate_build_report() {
    print_status "Generating build report..."
    
    REPORT_FILE="$REPORTS_DIR/build-report.txt"
    
    cat > "$REPORT_FILE" << EOF
FocusFlow Build Report
=====================
Build Date: $(date)
Build Environment: Production
Git Commit: $(git rev-parse HEAD 2>/dev/null || echo "Unknown")
Git Branch: $(git branch --show-current 2>/dev/null || echo "Unknown")

Build Artifacts:
EOF
    
    # List build artifacts
    if [ -f "$APK_DIR/debug/app-debug.apk" ]; then
        echo "- Debug APK: $(du -h "$APK_DIR/debug/app-debug.apk" | cut -f1)" >> "$REPORT_FILE"
    fi
    
    if [ -f "$APK_DIR/staging/app-staging.apk" ]; then
        echo "- Staging APK: $(du -h "$APK_DIR/staging/app-staging.apk" | cut -f1)" >> "$REPORT_FILE"
    fi
    
    if [ -f "$APK_DIR/release/app-release.apk" ]; then
        echo "- Release APK: $(du -h "$APK_DIR/release/app-release.apk" | cut -f1)" >> "$REPORT_FILE"
    fi
    
    if [ -f "$BUNDLE_DIR/release/app-release.aab" ]; then
        echo "- App Bundle: $(du -h "$BUNDLE_DIR/release/app-release.aab" | cut -f1)" >> "$REPORT_FILE"
    fi
    
    print_success "Build report generated: $REPORT_FILE"
}

# Main build pipeline
main() {
    echo -e "${BLUE}Starting FocusFlow production build pipeline...${NC}"
    
    # Parse command line arguments
    BUILD_TYPE="all"
    RUN_TESTS="true"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --type)
                BUILD_TYPE="$2"
                shift 2
                ;;
            --skip-tests)
                RUN_TESTS="false"
                shift
                ;;
            --help)
                echo "Usage: $0 [--type debug|staging|release|all] [--skip-tests]"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute build pipeline
    check_prerequisites
    clean_build
    
    if [ "$RUN_TESTS" = "true" ]; then
        run_unit_tests
        run_lint_checks
    fi
    
    case $BUILD_TYPE in
        debug)
            build_debug_apk
            ;;
        staging)
            build_staging_apk
            ;;
        release)
            build_release_apk
            build_app_bundle
            analyze_apk
            ;;
        all)
            build_debug_apk
            build_staging_apk
            build_release_apk
            build_app_bundle
            analyze_apk
            ;;
        *)
            print_error "Invalid build type: $BUILD_TYPE"
            exit 1
            ;;
    esac
    
    if [ "$RUN_TESTS" = "true" ]; then
        run_instrumented_tests
    fi
    
    generate_build_report
    
    echo -e "${GREEN}🎉 Build pipeline completed successfully!${NC}"
    echo "=================================================="
    print_status "Build artifacts available in: $BUILD_DIR"
    print_status "Reports available in: $REPORTS_DIR"
}

# Run main function
main "$@"

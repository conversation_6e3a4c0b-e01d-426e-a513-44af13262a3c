package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004JR\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\u001e\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00062\u0006\u0010\u001d\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u000e\u0010\u001f\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010 J\u0012\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190#0\"J\u0012\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190#0\"J\u0010\u0010%\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010 J\u001c\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00190#2\u0006\u0010\'\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0012\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190#0\"J\u0010\u0010)\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010 J\u0018\u0010*\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u001c\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010+J\u001a\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190#0\"2\u0006\u0010\u000b\u001a\u00020\bJ\"\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190#0\"2\u0006\u0010.\u001a\u00020\n2\u0006\u0010/\u001a\u00020\nJ\u001a\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00190#0\"2\u0006\u0010\u0010\u001a\u00020\bJ\u0016\u00101\u001a\u00020\u00062\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ&\u00102\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00062\u0006\u00103\u001a\u00020\u00152\u0006\u00104\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u00105J\u0016\u00106\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010+J(\u00107\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u00062\u0006\u00108\u001a\u0002092\b\u0010:\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010;J\u0016\u0010<\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006="}, d2 = {"Lcom/focusflow/data/repository/WishlistRepository;", "", "wishlistItemDao", "Lcom/focusflow/data/dao/WishlistItemDao;", "(Lcom/focusflow/data/dao/WishlistItemDao;)V", "addItemToWishlist", "", "itemName", "", "estimatedPrice", "", "category", "description", "merchant", "delayPeriodHours", "", "priority", "(Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePurchasedItemsOlderThan", "", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteWishlistItem", "wishlistItem", "Lcom/focusflow/data/model/WishlistItem;", "(Lcom/focusflow/data/model/WishlistItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "extendDelay", "id", "additionalHours", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveDelayCount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveDelayItems", "Lkotlinx/coroutines/flow/Flow;", "", "getAllActiveWishlistItems", "getAverageWishlistPrice", "getItemsWithExpiredDelay", "currentTime", "getPurchasedWishlistItems", "getTotalWishlistValue", "getWishlistItemById", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWishlistItemsByCategory", "getWishlistItemsByPriceRange", "minPrice", "maxPrice", "getWishlistItemsByPriority", "insertWishlistItem", "markAsPurchased", "purchaseDate", "actualPrice", "(JLkotlinx/datetime/LocalDateTime;DLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeDelay", "updateReflection", "stillWanted", "", "notes", "(JZLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWishlistItem", "app_release"})
public final class WishlistRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.WishlistItemDao wishlistItemDao = null;
    
    @javax.inject.Inject
    public WishlistRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.WishlistItemDao wishlistItemDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getAllActiveWishlistItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getPurchasedWishlistItems() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getItemsWithExpiredDelay(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime currentTime, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.WishlistItem>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getActiveDelayItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getWishlistItemsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getWishlistItemsByPriority(@org.jetbrains.annotations.NotNull
    java.lang.String priority) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.WishlistItem>> getWishlistItemsByPriceRange(double minPrice, double maxPrice) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getWishlistItemById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.WishlistItem> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getActiveDelayCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAverageWishlistPrice(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getTotalWishlistValue(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertWishlistItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem wishlistItem, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateWishlistItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem wishlistItem, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteWishlistItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.WishlistItem wishlistItem, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object markAsPurchased(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime purchaseDate, double actualPrice, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object removeDelay(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateReflection(long id, boolean stillWanted, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deletePurchasedItemsOlderThan(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object addItemToWishlist(@org.jetbrains.annotations.NotNull
    java.lang.String itemName, double estimatedPrice, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String merchant, int delayPeriodHours, @org.jetbrains.annotations.NotNull
    java.lang.String priority, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object extendDelay(long id, int additionalHours, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}
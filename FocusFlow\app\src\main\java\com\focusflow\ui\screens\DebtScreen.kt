package com.focusflow.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.CreditCard
import com.focusflow.ui.viewmodel.DebtViewModel
import com.focusflow.ui.viewmodel.PayoffStrategy
import kotlinx.datetime.*
import kotlin.math.abs

@Composable
fun DebtScreen(
    viewModel: DebtViewModel = hiltViewModel(),
    onNavigateToPayoffPlanner: () -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val creditCards by viewModel.allCreditCards.collectAsStateWithLifecycle(initialValue = emptyList())
    
    var showAddCardDialog by remember { mutableStateOf(false) }
    var showPayoffPlannerDialog by remember { mutableStateOf(false) }
    var selectedCard by remember { mutableStateOf<CreditCard?>(null) }
    var editingCard by remember { mutableStateOf<CreditCard?>(null) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Debt Management",
                style = MaterialTheme.typography.h4,
                fontWeight = FontWeight.Bold
            )
            
            TextButton(
                onClick = { showAddCardDialog = true }
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("Add Card")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Debt overview cards
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            DebtOverviewCard(
                title = "Total Debt",
                amount = uiState.totalDebt,
                color = Color(0xFFF44336),
                modifier = Modifier.weight(1f)
            )
            
            DebtOverviewCard(
                title = "Min Payments",
                amount = uiState.totalMinimumPayments,
                color = Color(0xFFFF9800),
                modifier = Modifier.weight(1f)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Payoff Planner Button
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { showPayoffPlannerDialog = true },
            elevation = 4.dp,
            shape = RoundedCornerShape(12.dp),
            backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.KeyboardArrowUp,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(32.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Column {
                    Text(
                        text = "Debt Payoff Planner",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Create a strategy to become debt-free",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
                Spacer(modifier = Modifier.weight(1f))
                Icon(                            Icons.Default.KeyboardArrowRight,
                    contentDescription = null,
                    tint = MaterialTheme.colors.onSurface.copy(alpha = 0.5f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Credit cards list
        Text(
            text = "Your Credit Cards",
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        if (creditCards.isEmpty()) {
            EmptyStateCard()
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(creditCards) { card ->
                    CreditCardItem(
                        creditCard = card,
                        onPaymentClick = { selectedCard = card },
                        onEditClick = { editingCard = card },
                        onDeleteClick = { viewModel.deleteCreditCard(card) }
                    )
                }
            }
        }
    }

    // Add credit card dialog
    if (showAddCardDialog) {
        AddCreditCardDialog(
            onDismiss = { showAddCardDialog = false },
            onAddCard = { name, balance, limit, minPayment, dueDate, interestRate ->
                viewModel.addCreditCard(name, balance, limit, minPayment, dueDate, interestRate)
                showAddCardDialog = false
            }
        )
    }

    // Payment dialog
    selectedCard?.let { card ->
        PaymentDialog(
            creditCard = card,
            onDismiss = { selectedCard = null },
            onMakePayment = { amount ->
                viewModel.makePayment(card.id, amount)
                selectedCard = null
            }
        )
    }

    // Edit credit card dialog
    editingCard?.let { card ->
        EditCreditCardDialog(
            creditCard = card,
            onDismiss = { editingCard = null },
            onUpdateCard = { name, balance, limit, minPayment, dueDate, interestRate ->
                viewModel.updateCreditCard(card.id, name, balance, limit, minPayment, dueDate, interestRate)
                editingCard = null
            }
        )
    }

    // Payoff planner dialog
    if (showPayoffPlannerDialog) {
        PayoffPlannerDialog(
            creditCards = creditCards,
            onDismiss = { showPayoffPlannerDialog = false },
            onNavigateToPlanner = {
                showPayoffPlannerDialog = false
                onNavigateToPayoffPlanner()
            },
            viewModel = viewModel
        )
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show error message and clear it
            // In a real app, you'd show a snackbar here
            viewModel.clearError()
        }

        // Show error card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp),
            backgroundColor = MaterialTheme.colors.error.copy(alpha = 0.1f),
            elevation = 2.dp
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colors.error,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = error,
                    color = MaterialTheme.colors.error,
                    style = MaterialTheme.typography.body2,
                    modifier = Modifier.weight(1f)
                )
                IconButton(
                    onClick = { viewModel.clearError() },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "Dismiss",
                        tint = MaterialTheme.colors.error,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun DebtOverviewCard(
    title: String,
    amount: Double,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "$${String.format("%.2f", amount)}",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = color
            )
        }
    }
}

@Composable
fun CreditCardItem(
    creditCard: CreditCard,
    onPaymentClick: () -> Unit,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Card header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = creditCard.name,
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
                
                Row {
                    IconButton(
                        onClick = onEditClick,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    IconButton(
                        onClick = onDeleteClick,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = MaterialTheme.colors.error,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Balance and limit
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Balance",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "$${String.format("%.2f", creditCard.currentBalance)}",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "Limit",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "$${String.format("%.2f", creditCard.creditLimit)}",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Utilization bar
            val utilization = (creditCard.currentBalance / creditCard.creditLimit).coerceIn(0.0, 1.0)
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Utilization",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "${String.format("%.1f", utilization * 100)}%",
                        style = MaterialTheme.typography.caption,
                        color = when {
                            utilization > 0.9 -> Color(0xFFF44336)
                            utilization > 0.7 -> Color(0xFFFF9800)
                            else -> Color(0xFF4CAF50)
                        }
                    )
                }
                
                LinearProgressIndicator(
                    progress = utilization.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp),
                    color = when {
                        utilization > 0.9 -> Color(0xFFF44336)
                        utilization > 0.7 -> Color(0xFFFF9800)
                        else -> Color(0xFF4CAF50)
                    },
                    backgroundColor = MaterialTheme.colors.onSurface.copy(alpha = 0.1f)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Payment info and button
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Min Payment: $${String.format("%.2f", creditCard.minimumPayment)}",
                        style = MaterialTheme.typography.body2
                    )
                    Text(
                        text = "Due: ${formatDate(creditCard.dueDate)}",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
                
                Button(
                    onClick = onPaymentClick,
                    colors = ButtonDefaults.buttonColors(
                        backgroundColor = MaterialTheme.colors.primary
                    )
                ) {
                    Text("Pay")
                }
            }
        }
    }
}

@Composable
fun EmptyStateCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(                            Icons.Default.Star,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No Credit Cards Added",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Add your credit cards to track balances and plan debt payoff",
                style = MaterialTheme.typography.body2,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun AddCreditCardDialog(
    onDismiss: () -> Unit,
    onAddCard: (String, Double, Double, Double, LocalDate, Double) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var balance by remember { mutableStateOf("") }
    var limit by remember { mutableStateOf("") }
    var minPayment by remember { mutableStateOf("") }
    var interestRate by remember { mutableStateOf("") }
    var dueDay by remember { mutableStateOf("1") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Add Credit Card",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            LazyColumn {
                item {
                    OutlinedTextField(
                        value = name,
                        onValueChange = { name = it },
                        label = { Text("Card Name") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                item {
                    OutlinedTextField(
                        value = balance,
                        onValueChange = { balance = it },
                        label = { Text("Current Balance") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                item {
                    OutlinedTextField(
                        value = limit,
                        onValueChange = { limit = it },
                        label = { Text("Credit Limit") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                item {
                    OutlinedTextField(
                        value = minPayment,
                        onValueChange = { minPayment = it },
                        label = { Text("Minimum Payment") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                item {
                    OutlinedTextField(
                        value = interestRate,
                        onValueChange = { interestRate = it },
                        label = { Text("Interest Rate (%)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                item {
                    OutlinedTextField(
                        value = dueDay,
                        onValueChange = { dueDay = it },
                        label = { Text("Due Day of Month") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val balanceDouble = balance.toDoubleOrNull()
                    val limitDouble = limit.toDoubleOrNull()
                    val minPaymentDouble = minPayment.toDoubleOrNull()
                    val interestRateDouble = interestRate.toDoubleOrNull()
                    val dueDayInt = dueDay.toIntOrNull()
                    
                    if (name.isNotBlank() && balanceDouble != null && limitDouble != null && 
                        minPaymentDouble != null && interestRateDouble != null && dueDayInt != null) {
                        
                        // Create due date for next occurrence of the due day
                        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                        val currentMonth = today.monthNumber
                        val currentYear = today.year
                        val dueDayCoerced = dueDayInt.coerceIn(1, 28)

                        val dueDate = if (dueDayCoerced >= today.dayOfMonth) {
                            // Due date is this month
                            LocalDate(currentYear, currentMonth, dueDayCoerced)
                        } else {
                            // Due date is next month
                            val nextMonth = if (currentMonth == 12) 1 else currentMonth + 1
                            val nextYear = if (currentMonth == 12) currentYear + 1 else currentYear
                            LocalDate(nextYear, nextMonth, dueDayCoerced)
                        }
                        
                        onAddCard(name, balanceDouble, limitDouble, minPaymentDouble, dueDate, interestRateDouble)
                    }
                },
                enabled = name.isNotBlank() && 
                         balance.toDoubleOrNull() != null && 
                         limit.toDoubleOrNull() != null && 
                         minPayment.toDoubleOrNull() != null && 
                         interestRate.toDoubleOrNull() != null &&
                         dueDay.toIntOrNull() != null
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun EditCreditCardDialog(
    creditCard: CreditCard,
    onDismiss: () -> Unit,
    onUpdateCard: (String, Double, Double, Double, LocalDate, Double) -> Unit
) {
    var name by remember { mutableStateOf(creditCard.name) }
    var balance by remember { mutableStateOf(creditCard.currentBalance.toString()) }
    var limit by remember { mutableStateOf(creditCard.creditLimit.toString()) }
    var minPayment by remember { mutableStateOf(creditCard.minimumPayment.toString()) }
    var interestRate by remember { mutableStateOf(creditCard.interestRate.toString()) }
    var dueDay by remember { mutableStateOf(creditCard.dueDate.dayOfMonth.toString()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Edit Credit Card",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            LazyColumn {
                item {
                    OutlinedTextField(
                        value = name,
                        onValueChange = { name = it },
                        label = { Text("Card Name") },
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = balance,
                        onValueChange = { balance = it },
                        label = { Text("Current Balance") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = limit,
                        onValueChange = { limit = it },
                        label = { Text("Credit Limit") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = minPayment,
                        onValueChange = { minPayment = it },
                        label = { Text("Minimum Payment") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = interestRate,
                        onValueChange = { interestRate = it },
                        label = { Text("Interest Rate (%)") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }

                item {
                    OutlinedTextField(
                        value = dueDay,
                        onValueChange = { dueDay = it },
                        label = { Text("Due Day of Month") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val balanceDouble = balance.toDoubleOrNull()
                    val limitDouble = limit.toDoubleOrNull()
                    val minPaymentDouble = minPayment.toDoubleOrNull()
                    val interestRateDouble = interestRate.toDoubleOrNull()
                    val dueDayInt = dueDay.toIntOrNull()

                    if (name.isNotBlank() && balanceDouble != null && limitDouble != null &&
                        minPaymentDouble != null && interestRateDouble != null && dueDayInt != null) {

                        // Create due date for next occurrence of the due day
                        val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                        val currentMonth = today.monthNumber
                        val currentYear = today.year
                        val dueDayCoerced = dueDayInt.coerceIn(1, 28)

                        val dueDate = if (dueDayCoerced >= today.dayOfMonth) {
                            // Due date is this month
                            LocalDate(currentYear, currentMonth, dueDayCoerced)
                        } else {
                            // Due date is next month
                            val nextMonth = if (currentMonth == 12) 1 else currentMonth + 1
                            val nextYear = if (currentMonth == 12) currentYear + 1 else currentYear
                            LocalDate(nextYear, nextMonth, dueDayCoerced)
                        }

                        onUpdateCard(name, balanceDouble, limitDouble, minPaymentDouble, dueDate, interestRateDouble)
                    }
                },
                enabled = name.isNotBlank() &&
                         balance.toDoubleOrNull() != null &&
                         limit.toDoubleOrNull() != null &&
                         minPayment.toDoubleOrNull() != null &&
                         interestRate.toDoubleOrNull() != null &&
                         dueDay.toIntOrNull() != null
            ) {
                Text("Update")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun PaymentDialog(
    creditCard: CreditCard,
    onDismiss: () -> Unit,
    onMakePayment: (Double) -> Unit
) {
    var paymentAmount by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Make Payment",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Card: ${creditCard.name}",
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "Current Balance: $${String.format("%.2f", creditCard.currentBalance)}",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                OutlinedTextField(
                    value = paymentAmount,
                    onValueChange = { paymentAmount = it },
                    label = { Text("Payment Amount") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(
                        onClick = { paymentAmount = String.format("%.2f", creditCard.minimumPayment) }
                    ) {
                        Text("Min Payment")
                    }
                    TextButton(
                        onClick = { paymentAmount = String.format("%.2f", creditCard.currentBalance) }
                    ) {
                        Text("Pay Full")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val amount = paymentAmount.toDoubleOrNull()
                    if (amount != null && amount > 0) {
                        onMakePayment(amount)
                    }
                },
                enabled = paymentAmount.toDoubleOrNull() != null && 
                         paymentAmount.toDoubleOrNull()!! > 0
            ) {
                Text("Pay")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun PayoffPlannerDialog(
    creditCards: List<CreditCard>,
    onDismiss: () -> Unit,
    onNavigateToPlanner: () -> Unit,
    viewModel: DebtViewModel
) {
    // This is now a simple confirmation dialog that navigates to the full screen
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Debt Payoff Planner",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Create a detailed debt payoff strategy to become debt-free faster.",
                    style = MaterialTheme.typography.body2
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "• Compare Avalanche vs Snowball strategies",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                Text(
                    text = "• See month-by-month payment schedule",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                Text(
                    text = "• Calculate total interest savings",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = onNavigateToPlanner
            ) {
                Text("Open Planner")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

private fun formatDate(date: LocalDate): String {
    return date.toString() // Simplified formatting
}


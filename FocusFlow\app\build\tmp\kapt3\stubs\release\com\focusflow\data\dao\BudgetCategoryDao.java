package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\t\n\u0002\u0010\t\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\bH\'J\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\tH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u001c\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\b2\u0006\u0010\r\u001a\u00020\u000eH\'J\u0018\u0010\u000f\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0010\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0011J,\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\b2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\'J\u0018\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0011J(\u0010\u0018\u001a\u0004\u0018\u00010\u00172\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0019J(\u0010\u001a\u001a\u0004\u0018\u00010\u00172\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0019J\u0018\u0010\u001b\u001a\u0004\u0018\u00010\u00172\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0011J(\u0010\u001c\u001a\u0004\u0018\u00010\u00172\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0019J(\u0010\u001e\u001a\u0004\u0018\u00010\u00172\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0019J,\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\b2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u0014H\'J\u0016\u0010 \u001a\u00020!2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\"\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010#\u001a\u00020\u00032\u0006\u0010$\u001a\u00020!2\u0006\u0010%\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010&\u00a8\u0006\'"}, d2 = {"Lcom/focusflow/data/dao/BudgetCategoryDao;", "", "deleteBudgetCategory", "", "budgetCategory", "Lcom/focusflow/data/model/BudgetCategory;", "(Lcom/focusflow/data/model/BudgetCategory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllBudgetCategories", "Lkotlinx/coroutines/flow/Flow;", "", "getAllBudgetCategoriesSync", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getBudgetCategoriesByPeriod", "period", "", "getBudgetCategoryByName", "name", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getMonthlyBudgetCategories", "year", "", "month", "getTotalBudgetForPeriod", "", "getTotalMonthlyBudget", "(Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTotalMonthlySpent", "getTotalSpentForPeriod", "getTotalWeeklyBudget", "week", "getTotalWeeklySpent", "getWeeklyBudgetCategories", "insertBudgetCategory", "", "updateBudgetCategory", "updateSpentAmount", "categoryId", "spentAmount", "(JDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
@androidx.room.Dao
public abstract interface BudgetCategoryDao {
    
    @androidx.room.Query(value = "SELECT * FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetCategory>> getMonthlyBudgetCategories(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, int month);
    
    @androidx.room.Query(value = "SELECT * FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period AND budgetYear = :year AND budgetWeek = :week")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetCategory>> getWeeklyBudgetCategories(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, int week);
    
    @androidx.room.Query(value = "SELECT * FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetCategory>> getBudgetCategoriesByPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period);
    
    @androidx.room.Query(value = "SELECT SUM(allocatedAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalMonthlyBudget(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, int month, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(allocatedAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period AND budgetYear = :year AND budgetWeek = :week")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalWeeklyBudget(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, int week, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(spentAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalMonthlySpent(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, int month, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(spentAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period AND budgetYear = :year AND budgetWeek = :week")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalWeeklySpent(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, int week, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertBudgetCategory(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateBudgetCategory(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteBudgetCategory(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE budget_categories SET spentAmount = :spentAmount WHERE id = :categoryId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateSpentAmount(long categoryId, double spentAmount, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM budget_categories WHERE isActive = 1 ORDER BY name ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetCategory>> getAllBudgetCategories();
    
    @androidx.room.Query(value = "SELECT * FROM budget_categories WHERE isActive = 1 AND name = :name LIMIT 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getBudgetCategoryByName(@org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetCategory> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(allocatedAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalBudgetForPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(spentAmount) FROM budget_categories WHERE isActive = 1 AND budgetPeriod = :period")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalSpentForPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM budget_categories WHERE isActive = 1 ORDER BY name ASC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAllBudgetCategoriesSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.BudgetCategory>> $completion);
}
package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\u001a0\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u0018\u0010\u0004\u001a\u0014\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a.\u0010\b\u001a\u00020\u00012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a\u0012\u0010\f\u001a\u00020\u00012\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a6\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00072\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001aD\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00072\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032$\u0010\u0013\u001a \u0012\u0016\u0012\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00160\u0015\u0012\u0004\u0012\u00020\u00010\u0014H\u0007\u00a8\u0006\u0017"}, d2 = {"AddBudgetCategoryDialog", "", "onDismiss", "Lkotlin/Function0;", "onAddCategory", "Lkotlin/Function2;", "", "", "EmptyBudgetCategoriesCard", "onQuickSetup", "modifier", "Landroidx/compose/ui/Modifier;", "EnhancedBudgetScreen", "viewModel", "Lcom/focusflow/ui/viewmodel/ZeroBudgetViewModel;", "QuickActionsCard", "unallocatedAmount", "onAutoBalance", "QuickSetupDialog", "onSetupComplete", "Lkotlin/Function1;", "", "Lkotlin/Pair;", "app_release"})
public final class EnhancedBudgetScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void EnhancedBudgetScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.ZeroBudgetViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickActionsCard(double unallocatedAmount, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAutoBalance, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onQuickSetup, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyBudgetCategoriesCard(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onQuickSetup, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddCategory, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AddBudgetCategoryDialog(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Double, kotlin.Unit> onAddCategory) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickSetupDialog(double unallocatedAmount, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.util.List<kotlin.Pair<java.lang.String, java.lang.Double>>, kotlin.Unit> onSetupComplete) {
    }
}